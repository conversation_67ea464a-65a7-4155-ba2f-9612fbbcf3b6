package jp.co.cac.ope.shokken.lab.ui.training

/*
import android.content.Context.MODE_PRIVATE
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.OPELabApplication
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.databinding.FragmentTrainingSpeechPracticeDescriptionBinding
import jp.co.cac.ope.shokken.lab.ui.modal.BottomAnnounceTrainingSpeechDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.TrainingHamburgerMenuDialogFragment
import jp.co.cac.ope.shokken.lab.utils.BillingUtil
import jp.co.cac.ope.shokken.lab.utils.DateUtil
import jp.co.cac.ope.shokken.lab.utils.LoginLogoutUtil
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone

class TrainingSpeechPracticeDescriptionFragment : Fragment() {
    private var _binding: FragmentTrainingSpeechPracticeDescriptionBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var malePlayer: ExoPlayer
    private lateinit var maleModal: BottomAnnounceTrainingSpeechDialogFragment
    private lateinit var femalePlayer: ExoPlayer
    private lateinit var femaleModal: BottomAnnounceTrainingSpeechDialogFragment

    private val maleSeekTimer = Handler(Looper.getMainLooper())
    private val maleSeekRunnable: Runnable = object : Runnable {
        override fun run() {
            maleSeekTimerUpdate()
            maleSeekTimer.postDelayed(this, 1000L)
        }
    }
    private val femaleSeekTimer = Handler(Looper.getMainLooper())
    private val femaleSeekRunnable: Runnable = object : Runnable {
        override fun run() {
            femaleSeekTimerUpdate()
            femaleSeekTimer.postDelayed(this, 1000L)
        }
    }

    private var isMalePlayerPlaying: Boolean = false
    private var isFemalePlayerPlaying: Boolean = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.VISIBLE

        val trainingSpeechPracticeDescriptionViewModel =
            ViewModelProvider(this).get(TrainingSpeechPracticeDescriptionViewModel::class.java)

        var trainingSpeechPracticeViewModel =
            ViewModelProvider(requireActivity()).get(TrainingSpeechPracticeViewModel::class.java)

        _binding =
            FragmentTrainingSpeechPracticeDescriptionBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val titleTextview: TextView = binding.backHeaderBar.headerTitle
        trainingSpeechPracticeDescriptionViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        val thisFragment = this

        val backButton: ImageButton = binding.backHeaderBar.backButton
        backButton.setImageResource(R.drawable.icon_hamburger)

        // 課金判定
        val billingUtil: BillingUtil = BillingUtil(requireActivity())
        billingUtil.isSubscribed(requireContext()) { isSubscribed ->
            backButton.setOnClickListener {
                // ハンバーガーメニュー
                TrainingHamburgerMenuDialogFragment.newInstance(
                    isSubscribed,
                    object : TrainingHamburgerMenuDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                            Log.d("ActionLog", "Close")
                        }

                        override fun callbackFromFreeButton() {
                            findNavController().navigate(
                                R.id.action_navigation_training_speech_practice_description_to_navigation_training_face_free,
                                null,
                                NavOptions.Builder()
                                    .setPopUpTo(
                                        R.id.navigation_training_speech_practice_top,
                                        true
                                    )
                                    .build()
                            )
                        }

                        override fun callbackFromKeepButton() {
                            findNavController().navigate(
                                R.id.action_navigation_training_speech_practice_description_to_navigation_training_face_keep,
                                null,
                                NavOptions.Builder()
                                    .setPopUpTo(
                                        R.id.navigation_training_speech_practice_top,
                                        true
                                    )
                                    .build()
                            )
                        }

                        override fun callbackFromSpeechDialogCloseButton() {
                            if (LoginLogoutUtil.showPaidModal(
                                    "training",
                                    isSubscribed,
                                    R.id.action_navigation_training_speech_practice_description_to_billing_introduction,
                                    true,
                                    "このコンテンツを利用するには\nログインまたは会員登録後Premiumプランに登録してください。",
                                    false,
                                    "このコンテンツを利用するには\nPremiumプラン登録してください。\nPremiumプランは初回登録のみ\n1週間無料でお試しいただけます。",
                                    sharedPreferences,
                                    parentFragmentManager,
                                    thisFragment,
                                    requireContext(),
                                    requireActivity().application as OPELabApplication
                                )
                            ) {
                                findNavController().navigate(
                                    R.id.action_navigation_training_speech_practice_description_to_navigation_training_speech_practice_top,
                                    null,
                                    NavOptions.Builder()
                                        .setPopUpTo(
                                            R.id.navigation_training_speech_practice_top,
                                            true
                                        )
                                        .build()
                                )
                            }
                        }

                        override fun callbackFromSelectPracticeDialogCloseButton() {
                            if (LoginLogoutUtil.showPaidModal(
                                    "training",
                                    isSubscribed,
                                    R.id.action_navigation_training_speech_practice_description_to_billing_introduction,
                                    true,
                                    "このコンテンツを利用するには\nログインまたは会員登録後Premiumプランに登録してください。",
                                    false,
                                    "このコンテンツを利用するには\nPremiumプラン登録してください。\nPremiumプランは初回登録のみ\n1週間無料でお試しいただけます。",
                                    sharedPreferences,
                                    parentFragmentManager,
                                    thisFragment,
                                    requireContext(),
                                    requireActivity().application as OPELabApplication
                                )
                            ) {
                                findNavController().navigate(
                                    R.id.action_navigation_training_speech_practice_description_to_navigation_training_speech_practice_top,
                                    null,
                                    NavOptions.Builder()
                                        .setPopUpTo(
                                            R.id.navigation_training_speech_practice_top,
                                            true
                                        )
                                        .build()
                                )
                            }
                        }
                    }).show(
                    requireActivity().supportFragmentManager,
                    "TrainingHamburgerMenuDialogFragment"
                )
            }
        }

        binding.startButton.setOnClickListener {
            findNavController().navigate(R.id.action_navigation_training_speech_practice_description_to_navigation_training_speech_practice_start)
        }

        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
        formatter.timeZone = TimeZone.getTimeZone("UTC")
        formatter.applyPattern("mm:ss")

        malePlayer = ExoPlayer.Builder(requireActivity()).build().apply {
            playWhenReady = false

            // 再生する動画の パス を指定
            val mediaItem =
                MediaItem.fromUri(Uri.parse(trainingSpeechPracticeViewModel.selectTraining?.ex_url_man))
            setMediaItem(mediaItem)

            prepare()
        }

        var maleController = true
        malePlayer.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                when (state) {
                    Player.STATE_READY -> {
                        if (maleController) {
                            maleController = false

                            maleModal = BottomAnnounceTrainingSpeechDialogFragment.newInstance(
                                formatter.format(Date(malePlayer.duration)),
                                object :
                                    BottomAnnounceTrainingSpeechDialogFragment.CallbackListener {
                                    override fun callbackFromDialogCloseButton() {
                                        isMalePlayerPlaying = false

                                        malePlayer.pause()
                                        malePlayer.seekTo(0)

                                        maleModal.updateSeekPoint(0.0f, "00:00")

                                        Handler(Looper.getMainLooper()).post {
                                            maleSeekTimer.removeCallbacks(maleSeekRunnable)
                                        }
                                    }

                                    override fun callbackFromDialogPlayButton() {
                                        isMalePlayerPlaying = true
                                        malePlayer.play()

                                        maleSeekTimer.postDelayed(maleSeekRunnable, 1000L)
                                    }

                                    override fun callbackFromDialogPauseButton() {
                                        isMalePlayerPlaying = false

                                        malePlayer.pause()

                                        Handler(Looper.getMainLooper()).post {
                                            maleSeekTimer.removeCallbacks(maleSeekRunnable)
                                        }
                                    }

                                    override fun callbackFromDialogReplayButton() {
                                        malePlayer.seekTo(0)

                                        maleModal.updateSeekPoint(0.0f, "00:00")

                                        malePlayer.play()

                                        maleSeekTimer.postDelayed(maleSeekRunnable, 1000L)
                                    }

                                    override fun callbackFromDialogBackwardButton() {
                                        val currentPosition = malePlayer.currentPosition
                                        val seekPosition = currentPosition - 10000

                                        malePlayer.seekTo(maxOf(seekPosition, 0))

                                        maleModal.updateSeekPoint(
                                            malePlayer.currentPosition.toFloat() / malePlayer.duration.toFloat(),
                                            formatter.format(Date(malePlayer.currentPosition))
                                        )
                                    }

                                    override fun callbackFromDialogForwardButton() {
                                        val currentPosition = malePlayer.currentPosition
                                        val seekPosition = currentPosition + 10000
                                        val duration = malePlayer.duration

                                        malePlayer.seekTo(minOf(seekPosition, duration))

                                        maleModal.updateSeekPoint(
                                            malePlayer.currentPosition.toFloat() / malePlayer.duration.toFloat(),
                                            formatter.format(Date(malePlayer.currentPosition))
                                        )
                                    }
                                })

                            // ボタンON
                            binding.maleButton.isEnabled = true
                            binding.maleButton.setTextColor((requireActivity().getColor(R.color.base_text)))
                            binding.maleButton.setBackgroundResource(
                                R.drawable.round_rectangle_25dp_white_and_color_primary_line
                            )

                            binding.maleButton.setOnClickListener {
                                maleModal.show(
                                    requireActivity().supportFragmentManager,
                                    "BottomAnnounceTrainingSpeechDialogFragment"
                                )
                            }
                        }
                    }

                    Player.STATE_ENDED -> {
                        Log.d("ActionLog", "male end")

                        isMalePlayerPlaying = false

                        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
                        formatter.timeZone = TimeZone.getTimeZone("UTC")
                        formatter.applyPattern("mm:ss")
                        maleModal.setReplay(formatter.format(Date(malePlayer.duration)))

                        Handler(Looper.getMainLooper()).post {
                            maleSeekTimer.removeCallbacks(maleSeekRunnable)
                        }
                    }
                }
            }
        })

        femalePlayer = ExoPlayer.Builder(requireActivity()).build().apply {
            playWhenReady = false

            // 再生する動画の パス を指定
            val mediaItem =
                MediaItem.fromUri(Uri.parse(trainingSpeechPracticeViewModel.selectTraining?.ex_url_woman))
            setMediaItem(mediaItem)

            prepare()
        }

        var femaleController = true
        femalePlayer.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                when (state) {
                    Player.STATE_READY -> {
                        if (femaleController) {
                            femaleController = false

                            femaleModal = BottomAnnounceTrainingSpeechDialogFragment.newInstance(
                                formatter.format(Date(femalePlayer.duration)),
                                object :
                                    BottomAnnounceTrainingSpeechDialogFragment.CallbackListener {
                                    override fun callbackFromDialogCloseButton() {
                                        isFemalePlayerPlaying = false

                                        femalePlayer.pause()
                                        femalePlayer.seekTo(0)

                                        femaleModal.updateSeekPoint(0.0f, "00:00")

                                        Handler(Looper.getMainLooper()).post {
                                            femaleSeekTimer.removeCallbacks(femaleSeekRunnable)
                                        }
                                    }

                                    override fun callbackFromDialogPlayButton() {
                                        isFemalePlayerPlaying = true
                                        femalePlayer.play()

                                        femaleSeekTimer.postDelayed(femaleSeekRunnable, 1000L)
                                    }

                                    override fun callbackFromDialogPauseButton() {
                                        isFemalePlayerPlaying = false

                                        femalePlayer.pause()

                                        Handler(Looper.getMainLooper()).post {
                                            femaleSeekTimer.removeCallbacks(femaleSeekRunnable)
                                        }
                                    }

                                    override fun callbackFromDialogReplayButton() {
                                        femalePlayer.seekTo(0)
                                        femaleModal.updateSeekPoint(0.0f, "00:00")

                                        femalePlayer.play()

                                        femaleSeekTimer.postDelayed(femaleSeekRunnable, 1000L)
                                    }

                                    override fun callbackFromDialogBackwardButton() {
                                        val currentPosition = femalePlayer.currentPosition
                                        val seekPosition = currentPosition - 10000

                                        femalePlayer.seekTo(maxOf(seekPosition, 0))

                                        femaleModal.updateSeekPoint(
                                            femalePlayer.currentPosition.toFloat() / femalePlayer.duration.toFloat(),
                                            formatter.format(Date(femalePlayer.currentPosition))
                                        )
                                    }

                                    override fun callbackFromDialogForwardButton() {
                                        val currentPosition = femalePlayer.currentPosition
                                        val seekPosition = currentPosition + 10000
                                        val duration = femalePlayer.duration

                                        femalePlayer.seekTo(minOf(seekPosition, duration))

                                        femaleModal.updateSeekPoint(
                                            femalePlayer.currentPosition.toFloat() / femalePlayer.duration.toFloat(),
                                            formatter.format(Date(femalePlayer.currentPosition))
                                        )
                                    }
                                })

                            // ボタンON
                            binding.femaleButton.isEnabled = true
                            binding.femaleButton.setTextColor((requireActivity().getColor(R.color.base_text)))
                            binding.femaleButton.setBackgroundResource(R.drawable.round_rectangle_25dp_white_and_color_primary_line)

                            binding.femaleButton.setOnClickListener {
                                femaleModal.show(
                                    requireActivity().supportFragmentManager,
                                    "BottomAnnounceTrainingSpeechDialogFragment"
                                )
                            }
                        }
                    }

                    Player.STATE_ENDED -> {
                        Log.d("ActionLog", "female end")

                        isFemalePlayerPlaying = false

                        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
                        formatter.timeZone = TimeZone.getTimeZone("UTC")
                        formatter.applyPattern("mm:ss")
                        femaleModal.setReplay(formatter.format(Date(femalePlayer.duration)))

                        Handler(Looper.getMainLooper()).post {
                            femaleSeekTimer.removeCallbacks(femaleSeekRunnable)
                        }
                    }
                }
            }
        })

        return root
    }

    override fun onPause() {
        Log.i("ActionLog", "onPause IN")

        if (malePlayer.isPlaying) {
            malePlayer.pause()
        }

        if (femalePlayer.isPlaying) {
            femalePlayer.pause()
        }

        Log.i("ActionLog", "onPause OUT")

        super.onPause()
    }

    override fun onResume() {
        super.onResume()

        Log.d("ActionLog", "onResume IN")

        if (isMalePlayerPlaying) {
            malePlayer.play()
        }

        if (isFemalePlayerPlaying) {
            femalePlayer.play()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        malePlayer.release()
        femalePlayer.release()

        _binding = null
    }

    private fun maleSeekTimerUpdate() {
        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
        formatter.timeZone = TimeZone.getTimeZone("UTC")
        formatter.applyPattern("mm:ss")

        maleModal.updateSeekPoint(
            malePlayer.currentPosition.toFloat() / malePlayer.duration.toFloat(),
            formatter.format(Date(malePlayer.currentPosition))
        )
    }

    private fun femaleSeekTimerUpdate() {
        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
        formatter.timeZone = TimeZone.getTimeZone("UTC")
        formatter.applyPattern("mm:ss")

        femaleModal.updateSeekPoint(
            femalePlayer.currentPosition.toFloat() / femalePlayer.duration.toFloat(),
            formatter.format(Date(femalePlayer.currentPosition))
        )
    }
}*/
