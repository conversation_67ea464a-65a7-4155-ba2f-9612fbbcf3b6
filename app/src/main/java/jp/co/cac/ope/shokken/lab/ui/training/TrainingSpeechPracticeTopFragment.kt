package jp.co.cac.ope.shokken.lab.ui.training

/*
import android.content.Context.MODE_PRIVATE
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.card.MaterialCardView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.OPELabApplication
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.TrainingAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentTrainingSpeechPracticeTopBinding
import jp.co.cac.ope.shokken.lab.model.VoiceTrainingContentsInfoModel
import jp.co.cac.ope.shokken.lab.model.VoiceTrainingContentsModel
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.TrainingHamburgerMenuDialogFragment
import jp.co.cac.ope.shokken.lab.utils.AWSCognitoUtil
import jp.co.cac.ope.shokken.lab.utils.BillingUtil
import jp.co.cac.ope.shokken.lab.utils.CustomerIOTrainingUtil
import jp.co.cac.ope.shokken.lab.utils.DateUtil
import jp.co.cac.ope.shokken.lab.utils.LoginLogoutUtil
import java.util.Locale

class TrainingSpeechPracticeTopFragment : Fragment() {
    private var _binding: FragmentTrainingSpeechPracticeTopBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.VISIBLE

        val trainingSpeechPracticeTopViewModel =
            ViewModelProvider(this).get(TrainingSpeechPracticeTopViewModel::class.java)

        var trainingSpeechPracticeViewModel =
            ViewModelProvider(requireActivity()).get(TrainingSpeechPracticeViewModel::class.java)

        _binding = FragmentTrainingSpeechPracticeTopBinding.inflate(inflater, container, false)
        val root: View = binding.root

        CustomerIOTrainingUtil.selectedTrainingType("voice")

        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val titleTextview: TextView = binding.backHeaderBar.headerTitle
        trainingSpeechPracticeTopViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        val thisFragment = this

        val backButton: ImageButton = binding.backHeaderBar.backButton
        backButton.setImageResource(R.drawable.icon_hamburger)

        // 課金判定
        val billingUtil: BillingUtil = BillingUtil(requireActivity())
        billingUtil.isSubscribed(requireContext()) { isSubscribed ->
            backButton.setOnClickListener {
                // ハンバーガーメニュー
                TrainingHamburgerMenuDialogFragment.newInstance(
                    isSubscribed,
                    object : TrainingHamburgerMenuDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                            Log.d("ActionLog", "Close")
                        }

                        override fun callbackFromFreeButton() {
                            findNavController().navigate(
                                R.id.action_navigation_training_speech_practice_top_to_navigation_training_face_free,
                                null,
                                NavOptions.Builder()
                                    .setPopUpTo(R.id.navigation_training_speech_practice_top, true)
                                    .build()
                            )
                        }

                        override fun callbackFromKeepButton() {
                            findNavController().navigate(
                                R.id.action_navigation_training_speech_practice_top_to_navigation_training_face_keep,
                                null,
                                NavOptions.Builder()
                                    .setPopUpTo(R.id.navigation_training_speech_practice_top, true)
                                    .build()
                            )
                        }

                        override fun callbackFromSpeechDialogCloseButton() {
                            if (LoginLogoutUtil.showPaidModal(
                                    "training",
                                    isSubscribed,
                                    R.id.action_navigation_training_speech_practice_top_to_billing_introduction,
                                    true,
                                    "このコンテンツを利用するには\nログインまたは会員登録後Premiumプランに登録してください。",
                                    false,
                                    "このコンテンツを利用するには\nPremiumプラン登録してください。\nPremiumプランは初回登録のみ\n1週間無料でお試しいただけます。",
                                    sharedPreferences,
                                    parentFragmentManager,
                                    thisFragment,
                                    requireContext(),
                                    requireActivity().application as OPELabApplication
                                )
                            ) {
                            }
                        }

                        override fun callbackFromSelectPracticeDialogCloseButton() {
                            if (LoginLogoutUtil.showPaidModal(
                                    "training",
                                    isSubscribed,
                                    R.id.action_navigation_training_speech_practice_top_to_billing_introduction,
                                    true,
                                    "このコンテンツを利用するには\nログインまたは会員登録後Premiumプランに登録してください。",
                                    false,
                                    "このコンテンツを利用するには\nPremiumプラン登録してください。\nPremiumプランは初回登録のみ\n1週間無料でお試しいただけます。",
                                    sharedPreferences,
                                    parentFragmentManager,
                                    thisFragment,
                                    requireContext(),
                                    requireActivity().application as OPELabApplication
                                )
                            ) {
                            }
                        }
                    }).show(
                    requireActivity().supportFragmentManager,
                    "TrainingHamburgerMenuDialogFragment"
                )
            }
        }

        //x-id-tokenが存在するAPIを叩く場合、有効期限切れか判定し、切れている場合はログイン画面に遷移させる
        if (LoginLogoutUtil.checkLoginExpireAndLogout(
                sharedPreferences,
                requireContext(),
                this,
                activity
            )
        ) {
            return root
        } else {
            // 通信して模擬面接コンテンツ一覧を取得する
            TrainingAPI.GetListTrainingVoiceCcontent(
                sharedPreferences,
                parentFragmentManager,
                { result: VoiceTrainingContentsModel, error: Exception? ->
                    if (error != null) {
                        Log.d("ActionLog", "${error}")

                        // IDトークン切れ
                        if (error.message == "Unexpected code 401") {
                            val awsCognitoUtil = AWSCognitoUtil(requireContext())
                            awsCognitoUtil.reLogin(
                                parentFragmentManager,
                                { userSession ->
                                    // 新しいトークンを保持
                                    LoginLogoutUtil.setLoginTokenInfo(
                                        sharedPreferences.edit(),
                                        userSession!!
                                    )

                                    // 通信して模擬面接コンテンツ一覧を再度取得する
                                    TrainingAPI.GetListTrainingVoiceCcontent(
                                        sharedPreferences,
                                        parentFragmentManager,
                                        { result: VoiceTrainingContentsModel, error: Exception? ->
                                            if (error != null) {
                                                CommonDialogFragment.newInstance("音声練習コンテンツ一覧の取得でエラーが発生しました。",
                                                    true,
                                                    "OK",
                                                    object : CommonDialogFragment.CallbackListener {
                                                        override fun callbackFromDialogCloseButton() {
                                                        }

                                                        override fun callbackFromDialogCancelButton() {
                                                        }
                                                    })
                                                    .show(
                                                        parentFragmentManager,
                                                        "CommonDialogFragment"
                                                    )
                                            } else {
                                                Log.d(
                                                    "ActionLog",
                                                    "TrainingAPI.GetListTrainingVoiceCcontent SUCCESS"
                                                )

                                                trainingSpeechPracticeTopViewModel.item = result

                                                trainingSpeechPracticeTopViewModel.connectionComplete.postValue(
                                                    true
                                                )
                                            }
                                        })
                                },
                                { exception ->
                                    LoginLogoutUtil.safeLogout(
                                        sharedPreferences,
                                        requireContext(),
                                        this,
                                        activity
                                    )
                                })
                        } else {
                            CommonDialogFragment.newInstance("音声練習コンテンツ一覧の取得でエラーが発生しました。",
                                true,
                                "OK",
                                object : CommonDialogFragment.CallbackListener {
                                    override fun callbackFromDialogCloseButton() {
                                    }

                                    override fun callbackFromDialogCancelButton() {
                                    }
                                })
                                .show(parentFragmentManager, "CommonDialogFragment")
                        }
                    } else {
                        Log.d("ActionLog", "TrainingAPI.GetListTrainingVoiceCcontent SUCCESS")

                        trainingSpeechPracticeTopViewModel.item = result

                        trainingSpeechPracticeTopViewModel.connectionComplete.postValue(true)
                    }
                })

            // データ取得後画面に反映
            // 戻ってきたときはconnectionCompleteを変えないままだと即座に反映
            // 再取得の場合はconnectionCompleteを変えておく
            trainingSpeechPracticeTopViewModel.connectionComplete.observe(viewLifecycleOwner) { shouldNavigate ->
                if (shouldNavigate) {
                    val contentsContainer: LinearLayout = binding.contentsContainer
                    contentsContainer.removeAllViews()

                    // リストを動的に追加
                    for ((index, voiceItem) in trainingSpeechPracticeTopViewModel.item.voice_training_contents_list.withIndex()) {
                        val menuButtonView: View = layoutInflater.inflate(
                            R.layout.training_speech_menu_button,
                            contentsContainer,
                            false
                        )

                        // タイトル部分を設定
                        val titleTextView: TextView =
                            menuButtonView.findViewById(R.id.title)
                        titleTextView.text = voiceItem.voice_training_contents_name

                        val targetLabelTextView: TextView =
                            menuButtonView.findViewById(R.id.target_time)
                        targetLabelTextView.text = String.format(
                            Locale.JAPAN,
                            "%d分",
                            DateUtil.string2Minutes(voiceItem.estimated_time)
                        )

                        if (voiceItem.comp_training == VoiceTrainingContentsInfoModel.COMP_TRAINING_COMPLETE) {
                            val constraintLayout: ConstraintLayout =
                                menuButtonView.findViewById(R.id.constraintLayout)
                            constraintLayout.setBackgroundResource(R.drawable.round_rectangle_5dp_color_primary_usui)

                            // チェックアイコン
                            val checkedImage: ImageView =
                                menuButtonView.findViewById(R.id.checked_image)
                            checkedImage.visibility = View.VISIBLE
                        }

                        val card: MaterialCardView = menuButtonView.findViewById(R.id.card)
                        card.setOnClickListener {
                            trainingSpeechPracticeViewModel.selectTraining = voiceItem

                            findNavController().navigate(R.id.action_navigation_training_speech_practice_top_to_navigation_training_speech_practice_description)
                        }

                        // コンテナに追加
                        contentsContainer.addView(menuButtonView)
                    }
                }
            }
        }

        return root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}*/
