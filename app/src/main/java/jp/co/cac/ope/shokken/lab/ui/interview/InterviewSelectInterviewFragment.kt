package jp.co.cac.ope.shokken.lab.ui.interview

import android.content.Context.MODE_PRIVATE
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.card.MaterialCardView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.OPELabApplication
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.MockInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentInterviewSelectInterviewBinding
import jp.co.cac.ope.shokken.lab.model.MockInterviewContentModel
import jp.co.cac.ope.shokken.lab.model.TrainingContentModel
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.BillingUtil
import jp.co.cac.ope.shokken.lab.utils.CustomerIOMockinterviewUtil
import jp.co.cac.ope.shokken.lab.utils.DateUtil
import jp.co.cac.ope.shokken.lab.utils.LoginLogoutUtil
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil
import java.util.Locale
import kotlin.collections.forEach

class InterviewSelectInterviewFragment : Fragment() {
    private var _binding: FragmentInterviewSelectInterviewBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.VISIBLE

        val interviewSelectTrainingViewModel =
            ViewModelProvider(this).get(InterviewSelectInterviewViewModel::class.java)

        var interviewViewModel =
            ViewModelProvider(requireActivity()).get(InterviewViewModel::class.java)

        _binding = FragmentInterviewSelectInterviewBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val titleTextview: TextView = binding.backHeaderBar.headerTitle
        interviewSelectTrainingViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        val backButton: ImageButton = binding.backHeaderBar.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        // 課金判定
        val billingUtil: BillingUtil = BillingUtil(requireActivity())
        billingUtil.isSubscribed(requireContext()) { isSubscribed ->
            val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)
            val isLoggedIn = SharedPreferenceUtil.isLoggedIn(sharedPreferences)

            // 通信して模擬面接コンテンツ一覧を取得する
            MockInterviewAPI.GetListMockInterviewContent(
                sharedPreferences,
                parentFragmentManager,
                true,
                { result: ArrayList<MockInterviewContentModel>, error: Exception? ->
                    if (error != null) {
                        CommonDialogFragment.newInstance("通信に失敗しました。", true, "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                }

                                override fun callbackFromDialogCancelButton() {
                                }
                            }).show(parentFragmentManager, "CommonDialogFragment")
                    } else {
                        Log.d("ActionLog", "模擬面接コンテンツ一覧件数:${result.count()}")

                        interviewSelectTrainingViewModel.items =
                            arrayListOf<MockInterviewContentModel>()

                        result.forEach { item ->
                            // ユーザ設定のモードと同じかすべて表示のコンテンツのみ表示
                            if (item.disp_mode == 0 || item.disp_mode == SharedPreferenceUtil.getMode(
                                    sharedPreferences
                                )
                            ) {
                                interviewSelectTrainingViewModel.items.add(item)
                            }
                        }

                        // ユーザ設定のモードによって並び順を変える
                        if (SharedPreferenceUtil.getMode(sharedPreferences) == 1) {
                            interviewSelectTrainingViewModel.items.sortBy { it.order_training }
                        } else {
                            interviewSelectTrainingViewModel.items.sortBy { it.order_assignment }
                        }

                        interviewSelectTrainingViewModel.connectionComplete.postValue(true)
                    }
                })

            // データ取得後画面に反映
            // 戻ってきたときはconnectionCompleteを変えないままだと即座に反映
            // 再取得の場合はconnectionCompleteを変えておく
            interviewSelectTrainingViewModel.connectionComplete.observe(viewLifecycleOwner) { shouldNavigate ->
                if (shouldNavigate) {
                    val contentsContainer: LinearLayout = binding.contentsContainer
                    contentsContainer.removeAllViews()

                    // リストを動的に追加
                    for ((index, interviewItem) in interviewSelectTrainingViewModel.items.withIndex()) {
                        val menuButtonView: View = layoutInflater.inflate(
                            R.layout.interview_practice_menu_button,
                            contentsContainer,
                            false
                        )

                        // タイトル部分を設定
                        val noTextView: TextView =
                            menuButtonView.findViewById(R.id.no)
                        noTextView.visibility = View.VISIBLE
                        noTextView.text = String.format(Locale.JAPAN, "%02d", index)

                        val titleTextView: TextView =
                            menuButtonView.findViewById(R.id.title)
                        titleTextView.text = interviewItem.mock_interview_contents_name

                        val kindLayout: LinearLayout = menuButtonView.findViewById(R.id.kind_layout)
                        kindLayout.visibility = View.VISIBLE

                        val kindLabelTextView: TextView =
                            menuButtonView.findViewById(R.id.kind_label)
                        kindLabelTextView.text = interviewItem.practice_index_name

                        val targetLabelTextView: TextView =
                            menuButtonView.findViewById(R.id.target_time)
                        targetLabelTextView.text = String.format(
                            Locale.JAPAN,
                            "%d分",
                            DateUtil.string2Minutes(interviewItem.estimated_time)
                        )

                        if (interviewItem.disp_billing_status != TrainingContentModel.DISP_BILLING_STATUS_FREE) {
                            if (interviewItem.disp_billing_status == TrainingContentModel.DISP_BILLING_STATUS_BILLING) {
                                // ログイン有料ユーザー：模擬面接画面へ遷移
                                if (isLoggedIn && isSubscribed) {
                                    if (interviewItem.is_implemented == MockInterviewContentModel.IS_IMPLEMENTED_DONE) {
                                        doneMockInterview(menuButtonView, interviewItem)
                                    } else {
                                        openMockInterview(menuButtonView, interviewItem)
                                    }
                                } else {
                                    // 非ログインユーザー：課金誘導モーダル（非ログイン向け）表示
                                    // ログイン無料ユーザー：課金誘導モーダル表示
                                    lockMockInterview(menuButtonView, isSubscribed)
                                }
                            } else {
                                lockMockInterview(menuButtonView, isSubscribed)
                            }
                        } else {
                            if (interviewItem.is_implemented == MockInterviewContentModel.IS_IMPLEMENTED_DONE) {
                                doneMockInterview(menuButtonView, interviewItem)
                            } else {
                                openMockInterview(menuButtonView, interviewItem)
                            }
                        }

                        // 最後のコンテンツは矢印非表示
                        if (index == interviewSelectTrainingViewModel.items.count() - 1) {
                            val nextImage: ImageView = menuButtonView.findViewById(R.id.next_image)
                            nextImage.visibility = View.INVISIBLE
                        }

                        // コンテナに追加
                        contentsContainer.addView(menuButtonView)
                    }

                    interviewSelectTrainingViewModel.connectionComplete.postValue(false)
                }
            }
        }

        return root
    }

    private fun lockMockInterview(view: View, isPaidUser: Boolean) {
        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val card: MaterialCardView = view.findViewById(R.id.card)

        // 鍵アイコン
        val billingImage: ImageView = view.findViewById(R.id.billing_image)
        billingImage.visibility = View.VISIBLE

        // タップで課金modal
        card.setOnClickListener {
            LoginLogoutUtil.showPaidModal(
                "interview",
                isPaidUser,
                R.id.action_navigation_interview_select_interview_to_billing_introduction,
                true,
                "このコンテンツを利用するには\nログインまたは会員登録後Premiumプランに登録してください。",
                false,
                "このコンテンツを利用するには\nPremiumプラン登録してください。\nPremiumプランは初回登録のみ\n1週間無料でお試しいただけます。",
                sharedPreferences,
                parentFragmentManager,
                this,
                requireContext(),
                requireActivity().application as OPELabApplication
            )
        }
    }

    private fun openMockInterview(view: View, item: MockInterviewContentModel) {
        val card: MaterialCardView = view.findViewById(R.id.card)

        // コンテンツアイコン
        val contentsImage: ImageView =
            view.findViewById(R.id.contents_image)
        contentsImage.visibility = View.VISIBLE

        // タップで次へ
        card.setOnClickListener {
            nextPage(item)
        }
    }

    private fun doneMockInterview(view: View, item: MockInterviewContentModel) {
        val card: MaterialCardView = view.findViewById(R.id.card)

        // 背景色
        val constraintLayout: ConstraintLayout =
            view.findViewById(R.id.constraintLayout)
        constraintLayout.setBackgroundResource(R.drawable.round_rectangle_5dp_color_primary_40)

        // 完了アイコン
        val checkedImage: ImageView =
            view.findViewById(R.id.checked_image)
        checkedImage.visibility = View.VISIBLE

        // タップで次へ
        card.setOnClickListener {
            nextPage(item)
        }
    }

    private fun nextPage(item: MockInterviewContentModel) {
        var interviewViewModel =
            ViewModelProvider(requireActivity()).get(InterviewViewModel::class.java)

        // CustomerIOへデータ送信
        CustomerIOMockinterviewUtil.selectedMockinterviewType(item.mock_interview_contents_code)

        var skipMyself: Boolean = false
        // 新卒ヒントと転職ヒント両方がnilの場合
        if (item.script_hint_new == null && item.script_hint_change == null) {
            skipMyself = true
        }

        if (item.disp_billing_status != MockInterviewContentModel.DISP_BILLING_STATUS_FREE) {
            interviewViewModel.selectInterview = item

            // 台本入力スキップ
            if (skipMyself) {
                interviewViewModel.inputMyselfText = ""
                interviewViewModel.isChecked = true
                interviewViewModel.skipMyself = true

                findNavController().navigate(R.id.action_navigation_interview_select_interview_to_navigation_interview_face_adjust)
            } else {
                findNavController().navigate(R.id.action_navigation_interview_select_interview_to_navigation_interview_my_self)
            }
        }
        // 無料コンテンツ
        else {
            interviewViewModel.selectInterview = item

            // 台本入力スキップ
            if (skipMyself) {
                interviewViewModel.inputMyselfText = ""
                interviewViewModel.isChecked = true
                interviewViewModel.skipMyself = true

                findNavController().navigate(R.id.action_navigation_interview_select_interview_to_navigation_interview_face_adjust)
            } else {
                findNavController().navigate(R.id.action_navigation_interview_select_interview_to_navigation_interview_my_self)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}