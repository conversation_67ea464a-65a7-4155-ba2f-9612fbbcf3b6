package jp.co.cac.ope.shokken.lab.ui.mypage

/*import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.PendingPurchasesParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesResponseListener
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryPurchasesParams*/
import android.content.Context.MODE_PRIVATE
import android.content.SharedPreferences
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.activity.HomeActivity
import jp.co.cac.ope.shokken.lab.connects.UserAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentMypageSettingBinding
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.DeleteUserDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.LogoutDialogFragment
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil

class MyPageSettingFragment : Fragment() {

    private var _binding: FragmentMypageSettingBinding? = null

    private val binding get() = _binding!!

    //private lateinit var billingUtil: BillingUtil

    //private lateinit var billingClient: BillingClient

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val myPageSettingViewModel =
            ViewModelProvider(this).get(MyPageSettingViewModel::class.java)

        _binding = FragmentMypageSettingBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val sp = requireActivity().getSharedPreferences(
           AppConstants.APP_PREF_NAME,
            MODE_PRIVATE
        )


        // タイトル設定
        binding.backHeaderBar.headerTitle.text = "設定"
        // ヘッダの戻るボタンクリック
        binding.backHeaderBar.backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)
        val editor = sharedPreferences.edit()

        //ログインしていない場合は特定の要素を非表示にする
        val isLogin = SharedPreferenceUtil.isLoggedIn(sharedPreferences)
        if(!isLogin){
            //binding.campaignCodeBtn.visibility = View.GONE
            binding.passwordChangeBtn.visibility = View.GONE
            binding.emailChangeBtn.visibility = View.GONE
            //binding.premiumPlanBtn.visibility = View.GONE // Billing feature disabled
            binding.logoutBtn.visibility = View.GONE
            binding.deleteBtn.visibility = View.GONE
        }

        //新卒・転職ボタンの初期表示
        if (SharedPreferenceUtil.getMode(sp) == 2){
            binding.careerChangeMode.setBackgroundResource(R.drawable.round_rectangle_3dp_color_primary)
            binding.careerChangeMode.setTextColor(resources.getColor(R.color.base_white))

            binding.newGraduatesMode.setBackgroundResource(R.drawable.round_rectangle_3dp_white_and_gray_line)
            binding.newGraduatesMode.setTextColor(Color.parseColor("#382F2D"))
        }else{
            binding.newGraduatesMode.setBackgroundResource(R.drawable.round_rectangle_3dp_color_primary)
            binding.newGraduatesMode.setTextColor(resources.getColor(R.color.base_white))

            binding.careerChangeMode.setBackgroundResource(R.drawable.round_rectangle_3dp_white_and_gray_line)
            binding.careerChangeMode.setTextColor(Color.parseColor("#382F2D"))
        }


        //新卒・転職ボタンのクリックイベント
        binding.newGraduatesMode.setOnClickListener{
            binding.newGraduatesMode.setBackgroundResource(R.drawable.round_rectangle_3dp_color_primary)
            binding.newGraduatesMode.setTextColor(resources.getColor(R.color.base_white))

            binding.careerChangeMode.setBackgroundResource(R.drawable.round_rectangle_3dp_white_and_gray_line)
            binding.careerChangeMode.setTextColor(Color.parseColor("#382F2D"))
            SharedPreferenceUtil.setMode(editor, 1)

            editMode(1, sharedPreferences)
        }
        binding.careerChangeMode.setOnClickListener{
            binding.careerChangeMode.setBackgroundResource(R.drawable.round_rectangle_3dp_color_primary)
            binding.careerChangeMode.setTextColor(resources.getColor(R.color.base_white))

            binding.newGraduatesMode.setBackgroundResource(R.drawable.round_rectangle_3dp_white_and_gray_line)
            binding.newGraduatesMode.setTextColor(Color.parseColor("#382F2D"))
            SharedPreferenceUtil.setMode(editor, 2)

            editMode(2, sharedPreferences)
        }

        //ログインユーザー情報取得
        UserAPI.getDetail(
            sharedPreferences,
            parentFragmentManager,
            { result, error ->
                if(error != null){
                    CommonDialogFragment.newInstance("通信に失敗しました。", true, "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }
                            override fun callbackFromDialogCancelButton() {
                            }
                        })
                        .show(parentFragmentManager, "CommonDialogFragment")
                }



                //模擬面接官設定タップ時の処理
                val mockInterviewer = result.mock_interviewer
                result.let {
                    val bundle = Bundle().apply{
                        if (mockInterviewer != null) {
                            putInt("mockInterviewer", mockInterviewer)
                        }else{
                            putInt("mockInterviewer", 0)
                        }
                    }
                    //遷移先にデータを渡す//模擬面接官設定の表示//UIスレッドで行わなければならない
                    requireActivity().runOnUiThread(){
                        binding.mockInterviewerBtn.setOnClickListener{
                            findNavController().navigate(R.id.action_setting_to_mypage_setting_mock_interviewer, bundle)
                        }

                        //模擬面接官設定の表示
                        if (mockInterviewer != null) {
                            if(mockInterviewer == 0){
                                binding.currentMockInterviewer.text = "ランダム"
                            }else if(mockInterviewer == 1){
                                binding.currentMockInterviewer.text = "男性"
                            }else{
                                binding.currentMockInterviewer.text = "女性"
                            }
                        }else{
                            binding.currentMockInterviewer.text = "ランダム"
                        }
                    }

                }

            }
        )

        //キャンペーン・クーポンコード
        /*binding.campaignCodeBtn.setOnClickListener{
            val intent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("https://play.google.com/redeem")
                setPackage("com.android.vending") // Google Playアプリを指定
            }
            startActivity(intent)
        }*/

        //ヘルプ
        binding.helpBtn.setOnClickListener{
            findNavController().navigate(R.id.action_navigation_mypage_setting_to_mypage_setting_help)
        }

        //パスワード変更
        binding.passwordChangeBtn.setOnClickListener{
            findNavController().navigate(R.id.action_setting_to_mypage_setting_change_password)
        }

        //メールアドレス変更
        binding.emailChangeBtn.setOnClickListener{
            findNavController().navigate(R.id.action_setting_to_mypage_setting_change_email)
        }

        //Premiumプランを管理
        /*binding.premiumPlanBtn.setOnClickListener{ // Billing feature disabled
            findNavController().navigate(R.id.action_setting_to_billing_introduction)
            //findNavController().navigate(R.id.action_debug)//todo課金チュートリアルデバッグ用
        }*/

        //プラン名表示
        /*billingUtil = BillingUtil(requireActivity())
        billingUtil.isSubscribed(requireContext()){isSubscribed->
            Log.d("tag", "onCreateView: $isSubscribed")
            if(isSubscribed){
                //binding.planName.text = "登録済" // Billing feature disabled

                val params =
                    PendingPurchasesParams.newBuilder().enableOneTimeProducts().enablePrepaidPlans().build()
                billingClient = BillingClient.newBuilder(requireContext()).setListener(
                    object : PurchasesUpdatedListener {
                        override fun onPurchasesUpdated(
                            billingResult: BillingResult, purchases: MutableList<Purchase>?
                        ) {
                        }
                    })
                    .enablePendingPurchases(params)
                    .build()

                Log.d("ActionLog", "BillingClient: オブジェクトを新規作成")
                
                billingClient.startConnection(object : BillingClientStateListener {
                    override fun onBillingSetupFinished(billingResult: BillingResult) {
                        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                            println("接続成功")

                            Log.d("ActionLog", "購入情報取得開始...")
                            val productModel = ProductModel()
                            val productDetailsList : MutableList<ProductDetails>? = null
                            billingClient.queryPurchasesAsync(QueryPurchasesParams.newBuilder()
                                .setProductType(BillingClient.ProductType.SUBS).build(),
                                object : PurchasesResponseListener {
                                    override fun onQueryPurchasesResponse(
                                        billingResult: BillingResult,
                                        purchaseList: MutableList<Purchase>
                                    ) {
                                        if (billingResult.responseCode != BillingClient.BillingResponseCode.OK) {
                                            Log.d("ActionLog", "購入情報取得失敗(${billingResult.responseCode})")
                                            return
                                        }

                                        Log.d("ActionLog", "購入情報取得成功(${billingResult.responseCode}), size=${purchaseList.size}")

                                        Log.d("bill", "$purchaseList")

                                        productModel.setDatas(productDetailsList, purchaseList)

                                        //プラン名を表示
                                        *//*val planNameStr = productModel.displayName // Billing feature disabled
                                        requireActivity().runOnUiThread{
                                            binding.planName.text = planNameStr
                                        }*//*
                                    }
                                })
                        }
                    }

                    override fun onBillingServiceDisconnected() {
                        println("接続失敗")

                    }
                })
            }else{
                //binding.planName.text = "未登録" // Billing feature disabled
            }
        }*/


        //利用規約・プライバシーポリシー
        binding.termsBtn.setOnClickListener{
            findNavController().navigate(R.id.action_setting_to_navigation_agreement_terms)
        }
        binding.privacyPolicyBtn.setOnClickListener{
            findNavController().navigate(R.id.action_setting_to_navigation_agreement_privacy)
        }

        //アプリを評価
        /*binding.appReviewBtn.setOnClickListener{
            val appPackageName = requireActivity().packageName
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$appPackageName&showAllReviews=true"))
                requireActivity().startActivity(intent)
            } catch (e: Exception) {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$appPackageName&showAllReviews=true"))
                requireActivity().startActivity(intent)
            }
        }*/

        //ログアウト
        binding.logoutBtn.setOnClickListener{
            LogoutDialogFragment().show(childFragmentManager, "logout")
        }

        //ユーザー削除
        binding.deleteBtn.setOnClickListener{
            DeleteUserDialogFragment().show(childFragmentManager, "DeleteUserDialog")
        }



        return root
    }

    override fun onResume() {
        super.onResume()
        // ボトムナビゲーションを非表示にする
        (activity as? HomeActivity)?.hideBottomNavigationView()
    }

    override fun onStop() {
        super.onStop()
        // ボトムナビゲーションを再表示する
        (activity as? HomeActivity)?.showBottomNavigationView()
    }

    /*override fun onDestroy() {
        super.onDestroy()
        billingUtil.closeBillingClient()
    }*/

    //新卒・転職モードを切り替える関数
    fun editMode(mode: Int, sharedPreferences: SharedPreferences){
        val editReqBody: Map<String, Any> = mapOf(
            "mode" to mode
        )

        UserAPI.edit(editReqBody,
            sharedPreferences,
            parentFragmentManager,
            { result: Map<String, Any>, error: Exception? ->
                if (error != null) {
                    CommonDialogFragment.newInstance("通信に失敗しました。", true, "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }
                            override fun callbackFromDialogCancelButton() {
                            }
                        }).show(parentFragmentManager, "CommonDialogFragment")
                }
            })
    }
}

