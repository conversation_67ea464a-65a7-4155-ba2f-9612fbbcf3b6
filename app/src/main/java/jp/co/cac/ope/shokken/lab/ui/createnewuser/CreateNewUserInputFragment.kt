package jp.co.cac.ope.shokken.lab.ui.createnewuser

import android.content.Context.MODE_PRIVATE
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.InputType
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageButton
import androidx.activity.OnBackPressedCallback
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.amazonaws.services.cognitoidentityprovider.model.UsernameExistsException
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.OPELabApplication
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.activity.LoginActivity
import jp.co.cac.ope.shokken.lab.activity.MainActivity
import jp.co.cac.ope.shokken.lab.connects.UserAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentCreateNewUserInputBinding
import jp.co.cac.ope.shokken.lab.model.UserModel
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.AWSCognitoUtil
import jp.co.cac.ope.shokken.lab.utils.CustomerIOUtil
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil

class CreateNewUserInputFragment : Fragment() {
    private var _binding: FragmentCreateNewUserInputBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var createNewUserViewModel: CreateNewUserViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        createNewUserViewModel =
            ViewModelProvider(requireActivity()).get(CreateNewUserViewModel::class.java)

        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)
        val editor = sharedPreferences.edit()

        var model: UserModel? = SharedPreferenceUtil.getAuthModel(sharedPreferences)
        if (model == null) {
            CommonDialogFragment.newInstance(
                "登録情報が見つかりません。",
                true,
                "OK",
                object : CommonDialogFragment.CallbackListener {
                    override fun callbackFromDialogCloseButton() {
                        val intent = Intent(requireContext(), MainActivity::class.java)
                        startActivity(intent)
                        activity?.finish()
                    }

                    override fun callbackFromDialogCancelButton() {}
                }
            ).show(parentFragmentManager, "CommonDialogFragment")
        } else {
            createNewUserViewModel.userModel = model
            // 認証コード送信済みの場合
            val email = SharedPreferenceUtil.getAuthEmail(sharedPreferences)
            if (!email.isNullOrEmpty()) {
                findNavController().navigate(R.id.action_navigation_create_new_user_input_to_navigation_create_new_user_email_authentication)
            }
            // 通常の遷移
            else {
                // 引き継ぎユーザ登録フラグをOFFにする
                SharedPreferenceUtil.setInheritUserFlg(editor, false)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // レイアウトを全画面に拡張
        activity?.window?.setDecorFitsSystemWindows(true)

        var createNewUserInputViewModel =
            ViewModelProvider(this).get(CreateNewUserInputViewModel::class.java)


        _binding = FragmentCreateNewUserInputBinding.inflate(inflater, container, false)

        binding.viewModel = createNewUserInputViewModel
        binding.lifecycleOwner = viewLifecycleOwner

        binding.backHeaderBar.headerTitle.text = getString(R.string.title_create_new_user)

        val closeButton: ImageButton = binding.backHeaderBar.closeButton
        closeButton.setOnClickListener {
            activity?.finish()
        }

        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    val app = activity?.application as OPELabApplication
                    val fromScreen = app.getFromScreen2Login()
                    if (fromScreen == "mypage" || fromScreen == "analysis" || fromScreen == "interview" || fromScreen == "training") {
                        //ログイン画面に遷移
                        val intent =
                            Intent(requireContext(), LoginActivity::class.java)
                        startActivity(intent)
                        activity?.finish()
                    } else {
                        isEnabled = false
                        requireActivity().onBackPressedDispatcher.onBackPressed()
                    }
                }
            })
        binding.email.inputTextEmailTextInput.id = View.generateViewId()
        binding.confirmEmail.inputTextEmailTextInput.id = View.generateViewId()

        // メールアドレス
        binding.email.inputTextEmailTextInput.hint = "<EMAIL>"
        // バリデート
        binding.email.inputTextEmailTextInput.doAfterTextChanged { text ->
            createNewUserInputViewModel.validateEmail(text.toString())
            createNewUserInputViewModel.validateSameEmail(
                text.toString(),
                binding.confirmEmail.inputTextEmailTextInput.text.toString()
            )
        }
        binding.email.inputTextEmailTextInput.setOnFocusChangeListener { view, hasFocus ->
            if (!hasFocus) {
                createNewUserInputViewModel.validateEmail(binding.email.inputTextEmailTextInput.text.toString())
                createNewUserInputViewModel.validateSameEmail(
                    binding.email.inputTextEmailTextInput.text.toString(),
                    binding.confirmEmail.inputTextEmailTextInput.text.toString()
                )
            }
        }
        createNewUserInputViewModel.validationEmailMessage.observe(
            viewLifecycleOwner,
            Observer { message ->
                if (message.isEmpty()) {
                    binding.email.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border)
                    binding.emailValidation.text.text = ""
                    val emailValidation = activity?.findViewById<View>(R.id.email_validation)
                    emailValidation?.visibility = View.GONE
                } else {
                    binding.email.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border_warning)
                    binding.emailValidation.text.text = message
                    val emailValidation = activity?.findViewById<View>(R.id.email_validation)
                    emailValidation?.visibility = View.VISIBLE
                }
            })

        // メールアドレス(再入力)
        binding.confirmEmail.inputTextEmailTextInput.hint = "<EMAIL>"
        // バリデート
        binding.confirmEmail.inputTextEmailTextInput.doAfterTextChanged { text ->
            createNewUserInputViewModel.validateConfirmEmail(
                binding.email.inputTextEmailTextInput.text.toString(),
                text.toString()
            )
        }
        binding.confirmEmail.inputTextEmailTextInput.setOnFocusChangeListener { view, hasFocus ->
            if (!hasFocus) {
                createNewUserInputViewModel.validateConfirmEmail(
                    binding.email.inputTextEmailTextInput.text.toString(),
                    binding.confirmEmail.inputTextEmailTextInput.text.toString()
                )
            }
        }
        createNewUserInputViewModel.validationConfirmEmailMessage.observe(
            viewLifecycleOwner,
            Observer { message ->
                if (message.isEmpty()) {
                    binding.confirmEmail.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border)
                    binding.confirmEmailValidation.text.text = ""
                    val confirmEmailValidation =
                        activity?.findViewById<View>(R.id.confirm_email_validation)
                    confirmEmailValidation?.visibility = View.GONE
                } else {
                    binding.confirmEmail.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border_warning)
                    binding.confirmEmailValidation.text.text = message
                    val confirmEmailValidation =
                        activity?.findViewById<View>(R.id.confirm_email_validation)
                    confirmEmailValidation?.visibility = View.VISIBLE
                }
            })

        // パスワード
        var isPassword1Visible = false
        val inputPassword1Button: ImageButton = binding.password.inputTextPasswordImageButton
        inputPassword1Button.setOnClickListener {
            val inputPasswordEditText: EditText = binding.password.inputTextPasswordTextInput
            val nowSelection = inputPasswordEditText.selectionStart
            // パスワード表示/非表示を切り替え
            isPassword1Visible = !isPassword1Visible
            if (isPassword1Visible) {
                inputPasswordEditText.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                inputPassword1Button.setImageResource(R.drawable.icon_eye_on)
            } else {
                inputPasswordEditText.inputType =
                    InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                inputPassword1Button.setImageResource(R.drawable.icon_eye_off)
            }
            inputPasswordEditText.setSelection(nowSelection)
        }
        // バリデート
        binding.password.inputTextPasswordTextInput.doAfterTextChanged { text ->
            createNewUserInputViewModel.validatePassword(text.toString())
            createNewUserInputViewModel.validateSamePassword(
                text.toString(),
                binding.confirmPassword.inputTextPasswordTextInput.text.toString()
            )

        }
        binding.password.inputTextPasswordTextInput.setOnFocusChangeListener { view, hasFocus ->
            if (!hasFocus) {
                createNewUserInputViewModel.validatePassword(binding.password.inputTextPasswordTextInput.text.toString())
                createNewUserInputViewModel.validateSamePassword(
                    binding.password.inputTextPasswordTextInput.text.toString(),
                    binding.confirmPassword.inputTextPasswordTextInput.text.toString()
                )
            }
        }
        createNewUserInputViewModel.validationPasswordMessage.observe(
            viewLifecycleOwner,
            Observer { message ->
                if (message.isEmpty()) {
                    binding.password.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border)
                    binding.passwordValidation.text.text = ""
                    val passwordValidation = activity?.findViewById<View>(R.id.password_validation)
                    passwordValidation?.visibility = View.GONE
                } else {
                    binding.password.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border_warning)
                    binding.passwordValidation.text.text = message
                    val passwordValidation = activity?.findViewById<View>(R.id.password_validation)
                    passwordValidation?.visibility = View.VISIBLE
                }
            })

        // パスワード(再入力)
        var isPassword2Visible = false
        val inputPassword2Button: ImageButton =
            binding.confirmPassword.inputTextPasswordImageButton
        inputPassword2Button.setOnClickListener {
            val inputPasswordEditText: EditText =
                binding.confirmPassword.inputTextPasswordTextInput
            val nowSelection = inputPasswordEditText.selectionStart
            // パスワード表示/非表示を切り替え
            isPassword2Visible = !isPassword2Visible
            if (isPassword2Visible) {
                inputPasswordEditText.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                inputPassword2Button.setImageResource(R.drawable.icon_eye_on)
            } else {
                inputPasswordEditText.inputType =
                    InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                inputPassword2Button.setImageResource(R.drawable.icon_eye_off)
            }
            inputPasswordEditText.setSelection(nowSelection)
        }
        // バリデート
        binding.confirmPassword.inputTextPasswordTextInput.doAfterTextChanged { text ->
            createNewUserInputViewModel.validateConfirmPassword(
                binding.password.inputTextPasswordTextInput.text.toString(),
                text.toString()
            )
        }
        binding.confirmPassword.inputTextPasswordTextInput.setOnFocusChangeListener { view, hasFocus ->
            if (!hasFocus) {
                createNewUserInputViewModel.validateConfirmPassword(
                    binding.password.inputTextPasswordTextInput.text.toString(),
                    binding.confirmPassword.inputTextPasswordTextInput.text.toString()
                )
            }
        }

        binding.actionButton.setOnClickListener {
            var validationFlg = false

            createNewUserInputViewModel.validateEmail(binding.email.inputTextEmailTextInput.text.toString())
            if (!createNewUserInputViewModel.validationEmailMessage.value!!.isEmpty()) {
                validationFlg = true
            }

            createNewUserInputViewModel.validateConfirmEmail(
                binding.email.inputTextEmailTextInput.text.toString(),
                binding.confirmEmail.inputTextEmailTextInput.text.toString()
            )
            if (!createNewUserInputViewModel.validationConfirmEmailMessage.value!!.isEmpty()) {
                validationFlg = true
            }

            createNewUserInputViewModel.validatePassword(binding.password.inputTextPasswordTextInput.text.toString())
            if (!createNewUserInputViewModel.validationPasswordMessage.value!!.isEmpty()) {
                validationFlg = true
            }

            createNewUserInputViewModel.validateConfirmPassword(
                binding.password.inputTextPasswordTextInput.text.toString(),
                binding.confirmPassword.inputTextPasswordTextInput.text.toString()
            )
            if (!createNewUserInputViewModel.validationConfirmPasswordMessage.value!!.isEmpty()) {
                validationFlg = true
            }

            if (createNewUserViewModel.mode < 0) {
                binding.willGraduationButton.background =
                    requireActivity().getDrawable(R.drawable.round_rectangle_5dp_warning)
                binding.graduationButton.background =
                    requireActivity().getDrawable(R.drawable.round_rectangle_5dp_warning)
                binding.modeValidation.text.text = "卒業種別を選択してください。"
                val modeValidate =
                    activity?.findViewById<View>(R.id.mode_validation)
                modeValidate?.visibility = View.VISIBLE

                validationFlg = true
            }

            if (validationFlg) {
                return@setOnClickListener
            }

            createNewUserViewModel.email = binding.email.inputTextEmailTextInput.text.toString()
            createNewUserViewModel.confirmEmail =
                binding.confirmEmail.inputTextEmailTextInput.text.toString()
            createNewUserViewModel.password =
                binding.password.inputTextPasswordTextInput.text.toString()
            createNewUserViewModel.confirmPassword =
                binding.confirmPassword.inputTextPasswordTextInput.text.toString()

            validateEmail(createNewUserViewModel.email) { isValid ->
                if (!isValid) return@validateEmail

                registerUserToAWSCognito { registerToCognitoSuccess ->
                    if (!registerToCognitoSuccess) return@registerUserToAWSCognito

                    registerUserToAPI { registerToAPISuccess ->
                        if (!registerToAPISuccess) return@registerUserToAPI

                        Handler(Looper.getMainLooper()).post {
                            findNavController().navigate(R.id.action_navigation_create_new_user_input_to_navigation_create_new_user_email_authentication)
                        }
                    }
                }
            }
        }

        createNewUserInputViewModel.validationConfirmPasswordMessage.observe(
            viewLifecycleOwner,
            Observer { message ->
                if (message.isEmpty()) {
                    binding.confirmPassword.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border)
                    binding.confirmPasswordValidation.text.text = ""
                    val confirmPasswordValidation =
                        activity?.findViewById<View>(R.id.confirm_password_validation)
                    confirmPasswordValidation?.visibility = View.GONE
                } else {
                    binding.confirmPassword.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border_warning)
                    binding.confirmPasswordValidation.text.text = message
                    val confirmPasswordValidation =
                        activity?.findViewById<View>(R.id.confirm_password_validation)
                    confirmPasswordValidation?.visibility = View.VISIBLE
                }
            })

        if (createNewUserViewModel.mode == 1) {
            createNewUserInputViewModel.onWillGraduationButtonClick()
        } else if (createNewUserViewModel.mode == 2) {
            createNewUserInputViewModel.onGraduationButtonClick()
        }

        createNewUserInputViewModel.switchModeButton.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate == 1) {
                binding.willGraduationButton.background =
                    requireActivity().getDrawable(R.drawable.round_rectangle_5dp_color_primary)
                binding.willGraduationButton.setTextColor(requireActivity().getColor(R.color.base_white))
                binding.willGraduationButton.setTypeface(null, Typeface.BOLD)
                binding.graduationButton.background =
                    requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
                binding.graduationButton.setTextColor(requireActivity().getColor(R.color.base_text))
                binding.graduationButton.setTypeface(null, Typeface.NORMAL)

                // バリデート解除
                binding.modeValidation.text.text = ""
                val modeValidation =
                    activity?.findViewById<View>(R.id.mode_validation)
                modeValidation?.visibility = View.GONE

                createNewUserViewModel.mode = 1
            } else if (shouldNavigate == 2) {
                binding.willGraduationButton.background =
                    requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
                binding.willGraduationButton.setTextColor(requireActivity().getColor(R.color.base_text))
                binding.willGraduationButton.setTypeface(null, Typeface.NORMAL)
                binding.graduationButton.background =
                    requireActivity().getDrawable(R.drawable.round_rectangle_5dp_color_primary)
                binding.graduationButton.setTextColor(requireActivity().getColor(R.color.base_white))
                binding.graduationButton.setTypeface(null, Typeface.BOLD)

                // バリデート解除
                binding.modeValidation.text.text = ""
                val modeValidation =
                    activity?.findViewById<View>(R.id.mode_validation)
                modeValidation?.visibility = View.GONE

                createNewUserViewModel.mode = 2

            } else {
            }
        }

        restoreState(createNewUserViewModel)

        val root: View = binding.root

        return root
    }

    private fun restoreState(createNewUserViewModel: CreateNewUserViewModel) {
        if (createNewUserViewModel.email.isNotBlank()) {
            binding.email.inputTextEmailTextInput.setText(createNewUserViewModel.email)
        }
        if (createNewUserViewModel.confirmEmail.isNotBlank()) {
            binding.confirmEmail.inputTextEmailTextInput.setText(createNewUserViewModel.confirmEmail)
        }
        if (createNewUserViewModel.password.isNotBlank()) {
            binding.password.inputTextPasswordTextInput.setText(createNewUserViewModel.password)
        }
        if (createNewUserViewModel.confirmPassword.isNotBlank()) {
            binding.confirmPassword.inputTextPasswordTextInput.setText(createNewUserViewModel.confirmPassword)
        }
        if (createNewUserViewModel.mode != -1) {
            when (createNewUserViewModel.mode) {
                1 -> binding.willGraduationButton.performClick()
                2 -> binding.graduationButton.performClick()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun validateEmail(email: String, completion: (isValid: Boolean) -> Unit) {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)
        UserAPI.validateEmail(
            email,
            sharedPreferences,
            parentFragmentManager
        ) { isValid, error ->
            if (error != null) {
                CommonDialogFragment.newInstance(
                    error.message ?: "通信に失敗しました。",
                    true,
                    "OK",
                    object : CommonDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {}
                        override fun callbackFromDialogCancelButton() {}
                    }
                ).show(parentFragmentManager, "CommonDialogFragment")
                completion(false)
            }
            completion(isValid == true)
        }
    }


    private fun registerUserToAWSCognito(completion: (success: Boolean) -> Unit) {
        try {
            val model = createNewUserViewModel.userModel
            model.email = createNewUserViewModel.email
            model.mode = createNewUserViewModel.mode

            Log.d(
                "ActionLog",
                "user_pool_id = ${AppConstants.COGNITO_IDENTITY_USER_POOL_ID}"
            )
            Log.d("ActionLog", "app_client_id = ${AppConstants.COGNITO_APP_CLIENT_ID}")
            Log.d(
                "ActionLog",
                "app_client_secret = ${AppConstants.COGNITO_APP_CLIENT_SECRET}"
            )
            Log.d(
                "ActionLog",
                "user_pool_region = ${AppConstants.COGNITO_IDENTITY_USER_POOL_REGION}"
            )

            // 通信
            val awsCognitoUtil = AWSCognitoUtil(requireContext())
            awsCognitoUtil.signUp(
                createNewUserViewModel.email.lowercase(),
                createNewUserViewModel.password,
                parentFragmentManager,
                { user, signUpResult ->
                    val sharedPreferences = requireActivity().getSharedPreferences(
                        AppConstants.APP_PREF_NAME,
                        MODE_PRIVATE
                    )
                    val editor = sharedPreferences.edit()

                    //SharedPreferenceUtil.setConfiguredProfile(editor, true)
                    CustomerIOUtil.completed_create_profile(sharedPreferences, model)

                    // 認証コード再送のためメールアドレスを保持
                    SharedPreferenceUtil.setAuthEmail(editor, createNewUserViewModel.email)
                    /*SharedPreferenceUtil.setAuthDate(
                        editor,
                        System.currentTimeMillis() / 1000.0
                    )*/
                    // 認証コード確認後の登録のためsubjectを保持
                    SharedPreferenceUtil.setAuthSub(editor, signUpResult!!.userSub)
                    // 認証コード確認後の登録のためUserModelを保持
                    SharedPreferenceUtil.setAuthModel(editor, model)

                    completion(true)
                },
                { exception ->
                    if (exception is UsernameExistsException) {
                        CommonDialogFragment.newInstance(
                            "このメールアドレスは既に登録されています。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                }

                                override fun callbackFromDialogCancelButton() {
                                }
                            })
                            .show(parentFragmentManager, "CommonDialogFragment")
                    } else {
                        CommonDialogFragment.newInstance(
                            "通信に失敗しました。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                }

                                override fun callbackFromDialogCancelButton() {
                                }
                            })
                            .show(parentFragmentManager, "CommonDialogFragment")
                    }
                    completion(false)
                }
            )
        } catch (e: Exception) {
            CommonDialogFragment.newInstance(
                "通信に失敗しました。${e}", true, "OK",
                object : CommonDialogFragment.CallbackListener {
                    override fun callbackFromDialogCloseButton() {
                    }

                    override fun callbackFromDialogCancelButton() {
                    }
                }).show(parentFragmentManager, "CommonDialogFragment")
            completion(false)
        }
    }

    private fun registerUserToAPI(completion: (success: Boolean) -> Unit) {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val userSub = SharedPreferenceUtil.getAuthSub(sharedPreferences)
        val userModel = SharedPreferenceUtil.getAuthModel(sharedPreferences)

        // 引き継ぎユーザ
        if (SharedPreferenceUtil.isInheritUserFlg(sharedPreferences)) {
            val udid = SharedPreferenceUtil.getUDID(sharedPreferences)
            if (udid!!.isEmpty()) {
                CommonDialogFragment.newInstance(
                    "情報の取得に失敗しました。",
                    true,
                    "OK",
                    object : CommonDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                        }

                        override fun callbackFromDialogCancelButton() {
                        }
                    }
                ).show(parentFragmentManager, "CommonDialogFragment")
                completion(false)
                return
            }

            Log.d("ActionLog", "----------引き継ぎ新規ユーザ----------")
            Log.d("ActionLog", "UserModel:" + userModel)
            Log.d("ActionLog", "userSub:" + userSub)
            Log.d("ActionLog", "UDID:" + udid)

            UserAPI.inheritLoginRegister(
                userModel!!.getInheritRegistParams(
                    userSub.toString(),
                    udid
                ),
                sharedPreferences,
                parentFragmentManager,
                { result: String, error: Exception? ->
                    if (error != null) {
                        CommonDialogFragment.newInstance(
                            "通信に失敗しました。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {}
                                override fun callbackFromDialogCancelButton() {}
                            }
                        ).show(parentFragmentManager, "CommonDialogFragment")
                    }
                    completion(error == null)
                }
            )
        }
        // 新規ユーザ
        else {
            Log.d("ActionLog", "----------新規ユーザ----------")
            Log.d("ActionLog", "UserModel:" + userModel?.description())
            Log.d("ActionLog", "userSub:" + userSub)

            UserAPI.newLoginRegister(
                userModel!!.getNewRegistParams(
                    userSub.toString(),
                    sharedPreferences
                ),
                sharedPreferences,
                parentFragmentManager,
                { result: String, error: Exception? ->
                    if (error != null) {
                        CommonDialogFragment.newInstance(
                            "通信に失敗しました。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {}
                                override fun callbackFromDialogCancelButton() {}
                            }
                        ).show(parentFragmentManager, "CommonDialogFragment")
                    }
                    completion(error == null)
                }
            )
        }
    }
}