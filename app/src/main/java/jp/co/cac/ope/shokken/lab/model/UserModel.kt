package jp.co.cac.ope.shokken.lab.model

import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.utils.DateUtil
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil

class UserModel {
    var user_id: Int? = null // ユーザーID
    var mode: Int? = null // モード
    var use_purpose: CategoryModel? = null // 活用目的
    var industry_id_list: ArrayList<CategoryModel>? = null // 志望業界リスト
    var occupation_id_list: ArrayList<CategoryModel>? = null // 志望職種リスト
    var id_token: String? = null // IDトークン
    var user_name: String? = null // ユーザー名
    var mail_address: String? = null // メールアドレス
    var birthday: String? = null // 生年月日
    var gender: Int? = null // 性別
    var graduation_date: String? = null // 卒業年月
    var graduation_type: Int? = null // 卒業種別
    var school_name: String? = null // 学部
    var mock_interviewer: Int? = null // 模擬面接官
    var is_billing: Int? = null // 課金状態(0:非課金)
    var billing_flg_android: Int? = null
    var plan_name: String? = null // プラン名
    var plan_expire: String? = null // プラン有効期限
    var verify_receipt: Int? = null // レシート検証
    var offer_name: String? = null// オファー名
    var offer_expire: String? = null // オファー有効期限
    var disp_face_guide_mi: Int? = null //顔位置説明表示フラグ（模擬面接）
    var disp_face_guide_fr: Int? = null //顔位置説明表示フラグ（フリー練習）
    var disp_face_guide_ke: Int? = null //顔位置説明表示フラグ（表情キープ）
    var email: String? = null

    var activation_end_date: String? = null

    fun description(): String {
        return "user_id=${(user_id ?: "")}, " +
                "mode=${(mode ?: "")}, " +
                "id_token=${(id_token ?: "")}, " +
                "user_name=${(user_name ?: "")}, " +
                "mail_address=${(mail_address ?: "")}, " +
                "birthday=${(birthday ?: "")}, " +
                "gender=${(gender ?: "")}, " +
                "graduation_date=${(graduation_date ?: "")}, " +
                "graduation_type=$graduation_type, " +
                "school_name=${(school_name ?: "")}," +
                "email=${(email ?: "")},"
                "activation_end_date=${(activation_end_date ?: "")}"
    }

    // SerializeするためJSON化
    fun toJSON(): String {
        var json = "{"
        json += "\"user_id\": $user_id, "
        json += "\"mode\": $mode, "
        json += "\"use_purpose\": ${
            when (use_purpose) {
                null -> "{}"
                else -> use_purpose?.toJSON()
            }
        }, "
        json += "\"industry_id_list\": ["
        industry_id_list?.forEach { item ->
            json += "${item.toJSON()}, "
        }
        if (json.last() == ' ') {
            json = json.dropLast(2)
        }
        json += "], "
        json += "\"occupation_id_list\": ["
        occupation_id_list?.forEach { item ->
            json += "${item.toJSON()}, "
        }
        if (json.last() == ' ') {
            json = json.dropLast(2)
        }
        json += "], "
        json += "\"user_name\": \"${(user_name ?: "")}\", "
        json += "\"mail_address\": \"${(mail_address ?: "")}\", "
        json += "\"birthday\": \"${(birthday ?: "")}\", "
        json += "\"gender\": $gender, "
        json += "\"graduation_date\": \"${(graduation_date ?: "")}\", "
        json += "\"graduation_type\": $graduation_type, "
        json += "\"school_name\": \"${(school_name ?: "")}\", "
        json += "\"mock_interviewer\": $mock_interviewer, "
        json += "\"disp_face_guide_mi\": $disp_face_guide_mi, "
        json += "\"disp_face_guide_fr\": $disp_face_guide_fr, "
        json += "\"disp_face_guide_ke\": $disp_face_guide_ke,"
        json += "\"email\": $email,"
        json += "\"activation_end_date\": $activation_end_date"
        json += "}"

        return json
    }

    fun setParams(params: Map<String, Any>, use_purpose_list: ArrayList<CategoryModel>?) {
        if (params.containsKey("user_id")
            && params["user_id"] != null
            && (params["user_id"] as Double).toInt() >= 0
        ) {
            user_id = (params["user_id"] as Double).toInt()
        }
        if (params.containsKey("mode")
            && params["mode"] != null
            && (params["mode"] as Double).toInt() >= 0
        ) {
            mode = (params["mode"] as Double).toInt()
        }
        if (params.containsKey("user_name")
            && params["user_name"] != null
        ) {
            user_name = params["user_name"] as String
        }
        if (params.containsKey("mail_address")
            && params["mail_address"] != null
        ) {
            mail_address = params["mail_address"] as String
        }
        if (params.containsKey("birthday")
            && params["birthday"] != null
        ) {
            birthday = params["birthday"] as String
        }
        if (params.containsKey("gender")
            && params["gender"] != null
            && (params["gender"] as Double).toInt() >= 0
        ) {
            gender = (params["gender"] as Double).toInt()
        }
        if (params.containsKey("graduation_date")
            && params["graduation_date"] != null
        ) {
            graduation_date = params["graduation_date"] as String
        }
        if (params.containsKey("graduation_type")
            && params["graduation_type"] != null
            && (params["graduation_type"] as Double).toInt() >= 0
        ) {
            graduation_type = (params["graduation_type"] as Double).toInt()
        }
        if (params.containsKey("billing_flg_android")
            && params["billing_flg_android"] != null
            && (params["billing_flg_android"] as Double).toInt() >= 0
        ) {
            billing_flg_android = (params["billing_flg_android"] as Double).toInt()
        }
        if (params.containsKey("school_name")
            && params["school_name"] != null
        ) {
            school_name = params["school_name"] as String
        }
        if (params.containsKey("mock_interviewer")
            && params["mock_interviewer"] != null
            && (params["mock_interviewer"] as Double).toInt() >= 0
        ) {
            mock_interviewer = (params["mock_interviewer"] as Double).toInt()
        }
        if (params.containsKey("disp_face_guide_mi")
            && params["disp_face_guide_mi"] != null
            && (params["disp_face_guide_mi"] as Double).toInt() >= 0
        ) {
            disp_face_guide_mi = (params["disp_face_guide_mi"] as Double).toInt()
        }
        if (params.containsKey("disp_face_guide_fr")
            && params["disp_face_guide_fr"] != null
            && (params["disp_face_guide_fr"] as Double).toInt() >= 0
        ) {
            disp_face_guide_fr = (params["mock_interviewer"] as Double).toInt()
        }
        if (params.containsKey("disp_face_guide_ke")
            && params["disp_face_guide_ke"] != null
            && (params["disp_face_guide_ke"] as Double).toInt() >= 0
        ) {
            disp_face_guide_ke = (params["disp_face_guide_ke"] as Double).toInt()
        }
        if (params.containsKey("billing_flg_android")
            && params["billing_flg_android"] != null
            && (params["billing_flg_android"] as Double).toInt() >= 0
        ) {
            billing_flg_android = (params["billing_flg_android"] as Double).toInt()
        }
        if (params.containsKey("email")
            && params["email"] != null
        ) {
            email = params["email"] as String
        }
        if (params.containsKey("activation_end_date")
            && params["activation_end_date"] != null
        ) {
            activation_end_date = params["activation_end_date"] as String
        }

        var up = (params["use_purpose"] as? Double)?.toInt()
        if (up != null && !use_purpose_list.isNullOrEmpty()) {
            for (item in use_purpose_list) {
                if (item.categoryId.toInt() == up) {
                    use_purpose = item

                    break
                }
            }
        }

        if (params.containsKey("industry_list")) {
            var temp: ArrayList<CategoryModel> = arrayListOf<CategoryModel>()

            (params["industry_list"] as ArrayList<Map<String, Any>>).forEach { item ->
                val i = CategoryModel()

                i.categoryId = (item["industry_id"] as Double).toInt().toString()
                i.categoryLabel = item["industry_name"] as String

                temp.add(i)
            }

            industry_id_list = temp
        }

        if (params.containsKey("occupation_list")) {
            var temp: ArrayList<CategoryModel> = arrayListOf<CategoryModel>()

            (params["occupation_list"] as ArrayList<Map<String, Any>>).forEach { item ->
                val i = CategoryModel()

                i.categoryId = (item["occupation_id"] as Double).toInt().toString()
                i.categoryLabel = item["occupation_name"] as String

                temp.add(i)
            }

            occupation_id_list = temp
        }
    }

    companion object {
        fun fromJson(jsonText: String): UserModel {
            // テキストが何もない場合は空を返す
            if (jsonText.isEmpty()) {
                return UserModel()
            }

            val type = object : TypeToken<Map<String, Any>>() {}.type
            val json: Map<String, Any> = Gson().fromJson(jsonText, type)

            return fromMap(json)
        }

        fun fromMap(item: Map<String, Any>): UserModel {
            var model = UserModel()

            if (item.containsKey("user_id")
                && item["estimated_time"] != null
                && (item["user_id"] as Double) >= 0.0
            ) {
                model.user_id = (item["user_id"] as Double).toInt()
            }

            if (item.containsKey("mode")
                && item["mode"] != null
                && (item["mode"] as Double) >= 0.0
            ) {
                model.mode = (item["mode"] as Double).toInt()
            }

            if (item.containsKey("use_purpose")
                && item["use_purpose"] != null
            ) {
                if ((item["use_purpose"] as Map<String, Any>).containsKey("categoryId")
                    && (item["use_purpose"] as Map<String, Any>).containsKey("categoryLabel")
                ) {
                    model.use_purpose =
                        CategoryModel.fromMap((item["use_purpose"] as Map<String, Any>))
                }
            }

            if (item.containsKey("industry_id_list")
                && item["industry_id_list"] != null
            ) {
                val list = arrayListOf<CategoryModel>()

                (item["industry_id_list"] as ArrayList<Map<String, Any>>).forEach { i ->
                    list.add(CategoryModel.fromMap(i))
                }

                model.industry_id_list = list
            }

            if (item.containsKey("occupation_id_list")
                && item["occupation_id_list"] != null
            ) {
                val list = arrayListOf<CategoryModel>()

                (item["occupation_id_list"] as ArrayList<Map<String, Any>>).forEach { i ->
                    list.add(CategoryModel.fromMap(i))
                }

                model.industry_id_list = list
            }

            if (item.containsKey("user_name")
                && item["user_name"] != null
            ) {
                model.user_name = item["user_name"] as String
            }
            if (item.containsKey("mail_address")
                && item["mail_address"] != null
            ) {
                model.mail_address = item["mail_address"] as String
            }
            if (item.containsKey("birthday")
                && item["birthday"] != null
            ) {
                model.birthday = item["birthday"] as String
            }
            if (item.containsKey("gender")
                && item["gender"] != null
                && (item["gender"] as Double) >= 0.0
            ) {
                model.gender = (item["gender"] as Double).toInt()
            }
            if (item.containsKey("graduation_date")
                && item["graduation_date"] != null
            ) {
                model.graduation_date = item["graduation_date"] as String
            }
            if (item.containsKey("graduation_type")
                && item["graduation_type"] != null
                && (item["graduation_type"] as Double) >= 0.0
            ) {
                model.graduation_type = (item["graduation_type"] as Double).toInt()
            }
            if (item.containsKey("billing_flg_android")
                && item["billing_flg_android"] != null
                && (item["billing_flg_android"] as Double) >= 0.0
            ) {
                model.billing_flg_android = (item["billing_flg_android"] as Double).toInt()
            }
            if (item.containsKey("school_name")
                && item["school_name"] != null
            ) {
                model.school_name = item["school_name"] as String
            }
            if (item.containsKey("mock_interviewer")
                && item["mock_interviewer"] != null
                && (item["mock_interviewer"] as Double) >= 0.0
            ) {
                model.mock_interviewer = (item["mock_interviewer"] as Double).toInt()
            }
            if (item.containsKey("disp_face_guide_mi")
                && item["disp_face_guide_mi"] != null
                && (item["disp_face_guide_mi"] as Double) >= 0.0
            ) {
                model.disp_face_guide_mi = (item["disp_face_guide_mi"] as Double).toInt()
            }
            if (item.containsKey("disp_face_guide_fr")
                && item["disp_face_guide_fr"] != null
                && (item["disp_face_guide_fr"] as Double) >= 0.0
            ) {
                model.disp_face_guide_fr = (item["disp_face_guide_fr"] as Double).toInt()
            }
            if (item.containsKey("disp_face_guide_ke")
                && item["disp_face_guide_ke"] != null
                && (item["disp_face_guide_ke"] as Double) >= 0.0
            ) {
                model.disp_face_guide_ke = (item["disp_face_guide_ke"] as Double).toInt()
            }
            if (item.containsKey("email")
                && item["email"] != null
            ) {
                model.email = item["email"] as String
            }
            if (item.containsKey("activation_end_date")
                && item["activation_end_date"] != null
            ) {
                model.activation_end_date = item["activation_end_date"] as String
            }

            return model
        }
    }

    fun getBirthday4Display(): String {
        if (birthday.isNullOrEmpty()) {
            return ""
        }
        return DateUtil.date2String(DateUtil.string2Date(birthday, "yyyy-MM-dd"), "yyyy年MM月dd日")
    }

    fun getBirthday_year(): String {
        if (birthday.isNullOrEmpty()) {
            return ""
        }
        return DateUtil.date2String(DateUtil.string2Date(birthday, "yyyy-MM-dd"), "yyyy")
    }

    fun getBirthday_month(): String {
        if (birthday.isNullOrEmpty()) {
            return ""
        }
        return DateUtil.date2String(DateUtil.string2Date(birthday, "yyyy-MM-dd"), "MM")
    }

    fun getBirthday_day(): String {

        if (birthday.isNullOrEmpty()) {
            return ""
        }
        return DateUtil.date2String(DateUtil.string2Date(birthday, "yyyy-MM-dd"), "dd")
    }

    fun getGraduation(): String {
        var result = ""

        if (!graduation_date.isNullOrEmpty()) {
            result =
                DateUtil.date2String(DateUtil.string2Date(graduation_date, "yyyy-MM"), "yyyy年MM月")
        }

        if (graduation_type != null) {
            var type = ""
            for ((index, value) in AppConstants.MASTER_GRADUATION_TYPES.withIndex()) {
                if (index == graduation_type) {
                    type = value

                    break
                }

                result = "$result $type"
            }
        }

        return result
    }

    fun getGraduationDate_year(): String {
        if (graduation_date.isNullOrEmpty()) {
            return ""
        }
        return DateUtil.date2String(DateUtil.string2Date(graduation_date, "yyyy-MM-dd"), "yyyy")
    }

    fun getGraduationDate_month(): String {
        if (graduation_date.isNullOrEmpty()) {
            return ""
        }
        return DateUtil.date2String(DateUtil.string2Date(graduation_date, "yyyy-MM-dd"), "MM")
    }

    fun getGenderLabel(): String {
        if (gender != null) {
            return ""
        }

        for ((index, value) in AppConstants.MASTER_GENDERS.withIndex()) {
            if (index == gender) {
                return value
            }
        }

        return ""
    }

    fun getIndustryIds(): ArrayList<String> {
        if (industry_id_list.isNullOrEmpty()) {
            return arrayListOf<String>()
        }

        var temp = arrayListOf<String>()
        for (item in industry_id_list!!) {
            temp.add(item.categoryId)
        }

        return temp
    }

    fun getOccupationIds(): ArrayList<String> {
        if (occupation_id_list.isNullOrEmpty()) {
            return arrayListOf<String>()
        }

        var temp: ArrayList<String> = arrayListOf<String>()
        for (item in occupation_id_list!!) {
            temp.add(item.categoryId)
        }

        return temp
    }

    fun getActivationEndDateDisplay(): String {
        if (activation_end_date.isNullOrEmpty()) {
            return ""
        }
        return DateUtil.date2String(
            DateUtil.string2Date(activation_end_date, "yyyy-MM-dd"),
            "yyyy年MM月dd日"
        )
    }

    fun makeIdList(list: ArrayList<CategoryModel>?): ArrayList<Int>? {
        if (list.isNullOrEmpty()) {
            return null
        }

        var ids: ArrayList<Int> = arrayListOf<Int>()
        for (item in list) {
            ids.add(item.categoryId.toInt())
        }

        return ids
    }

    // 登録API実行に必要な情報を整形
    fun getRegistParams(sharedPreferences: SharedPreferences?): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        //モードが未設定のとき対策
        var modeParam =
            if (mode == null) SharedPreferenceUtil.getMode(sharedPreferences!!) else mode
        params["mode"] = modeParam as Any
        if (use_purpose != null) {
            params["use_purpose"] = use_purpose!!.categoryId.toInt()
        }
        var ilist = makeIdList(industry_id_list)
        if (ilist != null) {
            params["industry_id_list"] = ilist
        }
        var olist = makeIdList(occupation_id_list)
        if (olist != null) {
            params["occupation_id_list"] = olist
        }

        Log.d("ActionLog", "----------getRegistParams----------")
        Log.d("ActionLog", "params:" + params)
        Log.d("ActionLog", "modeParam:" + modeParam)

        return params
    }

    // 編集API実行に必要な情報を整形
    fun getEditUsePurposeParams(sharedPreferences: SharedPreferences): Map<String, Any> {
        return getRegistParams(sharedPreferences)
    }

    // 新規ユーザ登録API実行に必要な情報を整形
    fun getNewRegistParams(
        subject: String,
        sharedPreferences: SharedPreferences
    ): Map<String, Any> {
        var params: MutableMap<String, Any> = getRegistParams(sharedPreferences).toMutableMap()

        Log.d("ActionLog", "----------getNewRegistParams----------")
        Log.d("ActionLog", "params:" + params)
        Log.d("ActionLog", "cognito_id:" + subject)

        params["cognito_id"] = subject

        //params["user_name"] = user_name as Any
        //params["birthday"] = birthday as Any
        //params["gender"] = gender as Any
        //params["graduation_date"] = graduation_date as Any
        //params["graduation_type"] = graduation_type as Any
        //params["school_name"] = school_name as Any
        params["mode"] = mode as Any
        params["email"] = email as Any

        Log.d("ActionLog", "params:" + params)

        return params
    }

    // プロファイルの編集API実行に必要な情報を整形
    fun getEditProfileParams(): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        //IDトークンが存在する場合は設定（ログインユーザーの場合、プロフィール設定後にメール送るため）
        if (id_token != null) {
            params["id_token"] = id_token as Any
        }

        //params["user_name"] = user_name as Any
        params["mail_address"] = mail_address as Any
        //params["birthday"] = birthday as Any
        //params["gender"] = gender as Any
        //params["graduation_date"] = graduation_date as Any
        //params["graduation_type"] = graduation_type as Any
        //params["school_name"] = school_name as Any
        params["mode"] = mode as Any
        params["email"] = email as Any

        return params
    }

    // 引き継ぎユーザ登録API実行に必要な情報を整形
    fun getInheritRegistParams(subject: String, udid: String): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        params["udid"] = udid
        params["cognito_id"] = subject
        //params["user_name"] = user_name as Any
        //params["birthday"] = birthday as Any
        //params["gender"] = gender as Any
        //params["graduation_date"] = graduation_date as Any
        //params["graduation_type"] = graduation_type as Any
        //params["school_name"] = school_name as Any
        params["mode"] = mode as Any
        params["email"] = email as Any

        return params
    }

    // 編集API実行に必要な情報を整形
    fun getEditModeParams(): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        params["mode"] = mode as Any

        return params
    }

    // 編集API実行に必要な情報を整形
    fun getEditInterviewerParams(): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        params["mock_interviewer"] = mock_interviewer as Any

        return params
    }

    // 編集API実行に必要な情報を整形
    fun getEditDispFaceGuideMIParams(): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        params["disp_face_guide_mi"] = disp_face_guide_mi as Any

        return params
    }

    // 編集API実行に必要な情報を整形
    fun getEditDispFaceGuideFRParams(): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        params["disp_face_guide_fr"] = disp_face_guide_fr as Any

        return params
    }

    // 編集API実行に必要な情報を整形
    fun getEditDispFaceGuideKEParams(): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        params["disp_face_guide_ke"] = disp_face_guide_ke as Any

        return params
    }
}