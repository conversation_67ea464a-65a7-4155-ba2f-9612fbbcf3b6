package jp.co.cac.ope.shokken.lab.ui.mypage

import android.content.Context.MODE_PRIVATE
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.InterviewScheduleAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentInterviewScheduleListBinding
import jp.co.cac.ope.shokken.lab.model.InterviewScheduleModel
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.DateUtil


class InterviewScheduleListFragment : Fragment() {

    private var _binding: FragmentInterviewScheduleListBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        Log.d(
            "ActionLog",
            "*** ${this.javaClass.simpleName} ${object {}.javaClass.enclosingMethod.name} start ***"
        )

        val viewModel =
            ViewModelProvider(this).get(InterviewScheduleListViewModel::class.java)

        val sp = requireActivity().getSharedPreferences(
           AppConstants.APP_PREF_NAME,
            MODE_PRIVATE
        )

        _binding = FragmentInterviewScheduleListBinding.inflate(inflater, container, false)

        // タブバー非表示
        val bottomNavigationView =
            requireActivity().findViewById<BottomNavigationView>(R.id.nav_view)
        bottomNavigationView.visibility = View.GONE

        // タイトル設定
        binding.backHeaderBar.headerTitle.text = "予定リスト"

        // API通信で現在予定リスト取得
        getStatus1InterviewSchedules(sp, viewModel)

        // データ取得後画面に反映
        viewModel.connectionComplete.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                setTabStyle(viewModel, binding)

                binding.backHeaderBar.currentCountTextView.text = "${viewModel.currentCount}"
                binding.backHeaderBar.totalCountTextView.text =
                    "/${AppConstants.INTERVIEWSCHEDULE_MAX_COUNT}"

                val itemListContainer: LinearLayout = binding.itemListContainer
                itemListContainer.removeAllViews()

                // 予定リストがない場合
                if (viewModel.currentCount == 0) {
                    binding.seachFromLayout.visibility = View.GONE
                    binding.noitemContainer.visibility = View.VISIBLE
                    binding.itemListContainer.visibility = View.GONE
                    return@observe
                }

                val interviewSchedules = searchCompanyName(viewModel, binding.companyNameSearchInput.text.toString())

                // 予定リストの検索結果がない場合
                if (interviewSchedules.isNullOrEmpty()) {
                    binding.seachFromLayout.visibility = View.VISIBLE
                    binding.noitemContainer.visibility = View.VISIBLE
                    binding.itemListContainer.visibility = View.GONE
                    return@observe
                }

                binding.seachFromLayout.visibility = View.VISIBLE
                binding.noitemContainer.visibility = View.GONE
                binding.itemListContainer.visibility = View.VISIBLE

                var i = 0
                for (model in interviewSchedules) {
                    ++i
                    // TODO 背景色の変更がsetBackgroudColorでうまくいかずページ自体をわける実装にしている
                    // 時間があったら改善したい
                    val itemView = layoutInflater.inflate(
                        if (viewModel.displayStatus == 1) R.layout.part_interview_schedule_list_view else R.layout.part_interview_schedule_list_view2,
                        itemListContainer,
                        false
                    )
                    if (i == interviewSchedules.size) {
                        val lp = itemView.layoutParams
                        val mlp = lp as MarginLayoutParams
                        mlp.setMargins(lp.leftMargin, lp.topMargin, lp.rightMargin, 200)
                        itemView.setLayoutParams(mlp)
                    }

                    val dtTextView: TextView =
                        itemView.findViewById(R.id.dtTextView)
                    dtTextView.text = DateUtil.date2List(model.interview_datetime)

                    var companyTextView: TextView = itemView.findViewById(R.id.companyTextView)
                    companyTextView.text = model.company_name

                    val selectionStatusTextView: TextView =
                        itemView.findViewById(R.id.selectionStatusTextView)
                    if (model.selection_status.isNullOrEmpty()) {
                        selectionStatusTextView.visibility = View.GONE
                    } else {
                        selectionStatusTextView.visibility = View.VISIBLE
                        selectionStatusTextView.text = model.selection_status
                    }

                    // 詳細画面遷移で、ビューをクリックした場合
                    itemView.setOnClickListener {
                        Log.d("ActionLog", "on click itemView. id=${model.interview_schedule_id}")
                        val bundle = Bundle()
                        bundle.putInt("interview_schedule_id", model.interview_schedule_id!!)
                        //findNavController().navigate(R.id.action_navigation_interview_schedule_list_to_interview_schedule_detail, bundle)
                    }

                    // 編集ボタンクリック
                    val edit: ImageView = itemView.findViewById(R.id.iconEdit)
                    edit.setOnClickListener {
                        Log.d("ActionLog", "on click edit. id=${model.interview_schedule_id}")
                        val bundle = Bundle()
                        bundle.putInt("interview_schedule_id", model.interview_schedule_id!!)
                        //findNavController().navigate(R.id.action_navigation_interview_schedule_list_to_interview_schedule_input, bundle)
                    }

                    // 削除ボタンをクリック
                    val trash: ImageView = itemView.findViewById(R.id.iconTrash)
                    trash.setOnClickListener {
                        Log.d("ActionLog", "confrom delete ${model.description()}")
                        viewModel.interviewScheduleId = model.interview_schedule_id
                        CommonDialogFragment.newInstance("削除しますか？", true, "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {

                                    val id = viewModel.interviewScheduleId
                                    Log.d(
                                        "ActionLog",
                                        "${this.javaClass.simpleName} delete id=${id}"
                                    )
                                    if (id != null) {
                                        viewModel.interviewScheduleId = null
                                        Log.d(
                                            "ActionLog",
                                            "${this.javaClass.simpleName} delete id=${id}"
                                        )
                                        deleteInterviewSchedule(sp, id, viewModel)
                                    }
                                }
                                override fun callbackFromDialogCancelButton() {
                                }
                            }, true
                        ).show(parentFragmentManager, "CommonDialogFragment")
                    }

                    // コンテナに追加
                    itemListContainer.addView(itemView)
                }
            }
        }

        // ヘッダの戻るボタンクリック
        binding.backHeaderBar.backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        // 企業名検索
        binding.companyNameSearchInput.doAfterTextChanged { text ->
            viewModel.connectionComplete.postValue(true)
        }

        // 現在の予定リストクリック
        binding.status1TextView.setOnClickListener {
            viewModel.displayStatus = 1
            getStatus1InterviewSchedules(sp, viewModel)
        }

        // 過去の予定リストクリック
        binding.status2TextView.setOnClickListener {
            viewModel.displayStatus = 2
            getStatus2InterviewSchedules(sp, viewModel)
        }

        // 追加ボタンクリック
        binding.addButton.setOnClickListener {
            Log.d("ActionLog", "click newScheduleButton")
            if (viewModel.currentCount >= AppConstants.INTERVIEWSCHEDULE_MAX_COUNT) {
                CommonDialogFragment.newInstance("予定は最大${AppConstants.INTERVIEWSCHEDULE_MAX_COUNT}件まで登録可能です。 \n新しく予定を追加するには不要な予定を削除してください。",
                    true,
                    "OK",
                    object : CommonDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                        }
                        override fun callbackFromDialogCancelButton() {
                        }
                    }).show(parentFragmentManager, "CommonDialogFragment")
                return@setOnClickListener
            }

            //findNavController().navigate(R.id.action_navigation_interview_schedule_list_to_interview_schedule_input)
        }

        Log.d(
            "ActionLog",
            "*** ${this.javaClass.simpleName} ${object {}.javaClass.enclosingMethod.name} end ***"
        )

        val root: View = binding.root
        return root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    // 現在の予定リストを取得
    fun getStatus1InterviewSchedules(
        sp: SharedPreferences,
        viewModel: InterviewScheduleListViewModel
    ) {
        InterviewScheduleAPI.GetStatus1InterviewSchedules(sp,
            parentFragmentManager,
            { total: Int?, result: ArrayList<InterviewScheduleModel>, error: Exception? ->

                if (error != null) {
                    CommonDialogFragment.newInstance("通信に失敗しました。", true, "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }
                            override fun callbackFromDialogCancelButton() {
                            }
                        })
                        .show(parentFragmentManager, "CommonDialogFragment")
                    return@GetStatus1InterviewSchedules
                }
                viewModel.currentCount = total!!
                viewModel.status1_interviewSchedules = result
                viewModel.connectionComplete.postValue(true)
            })
    }

    // 過去の予定リストを取得
    fun getStatus2InterviewSchedules(
        sp: SharedPreferences,
        viewModel: InterviewScheduleListViewModel
    ) {
        InterviewScheduleAPI.GetStatus2InterviewSchedules(sp, parentFragmentManager,
            { total: Int?, result: ArrayList<InterviewScheduleModel>, error: Exception? ->

                if (error != null) {
                    CommonDialogFragment.newInstance("通信に失敗しました。", true, "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }
                            override fun callbackFromDialogCancelButton() {
                            }
                        })
                        .show(parentFragmentManager, "CommonDialogFragment")
                    return@GetStatus2InterviewSchedules
                }
                viewModel.currentCount = total!!
                viewModel.status2_interviewSchedules = result
                viewModel.connectionComplete.postValue(true)
            })
    }

    // 予定を削除
    fun deleteInterviewSchedule(
        sp: SharedPreferences,
        id: Int,
        viewModel: InterviewScheduleListViewModel
    ) {
        InterviewScheduleAPI.delete(sp, id, parentFragmentManager,
            { result: String?, error: Exception? ->

                if (error != null) {
                    CommonDialogFragment.newInstance("通信に失敗しました。", true, "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }
                            override fun callbackFromDialogCancelButton() {
                            }
                        })
                        .show(parentFragmentManager, "CommonDialogFragment")
                    return@delete
                }
                getAllInterviewSchedules(sp, viewModel)
            })
    }

    fun getAllInterviewSchedules(sp: SharedPreferences, viewModel: InterviewScheduleListViewModel) {
        if (viewModel.displayStatus == 1) {
            getStatus1InterviewSchedules(sp, viewModel)
        } else {
            getStatus2InterviewSchedules(sp, viewModel)
        }
    }

    fun setTabStyle(
        viewMode: InterviewScheduleListViewModel,
        binding: FragmentInterviewScheduleListBinding
    ) {
        if (viewMode.displayStatus == 1) {
            binding.status1TextView.setBackgroundResource(R.drawable.border_bottom_color_primary_3dp)
            binding.status2TextView.setBackgroundColor(resources.getColor(R.color.base_white))
        } else {
            binding.status2TextView.setBackgroundResource(R.drawable.border_bottom_color_primary_3dp)
            binding.status1TextView.setBackgroundColor(resources.getColor(R.color.base_white))
        }
    }

    fun searchCompanyName(
        viewModel: InterviewScheduleListViewModel,
        str: String
    ): ArrayList<InterviewScheduleModel> {
        var results = ArrayList<InterviewScheduleModel>()

        val all = if (viewModel.displayStatus == 1) viewModel.status1_interviewSchedules else viewModel.status2_interviewSchedules
        all.forEach { item ->
            if (str.isNullOrEmpty() || item.company_name?.indexOf(str)!! > -1) {
                results.add(item)
            }
        }
        return results
    }

}