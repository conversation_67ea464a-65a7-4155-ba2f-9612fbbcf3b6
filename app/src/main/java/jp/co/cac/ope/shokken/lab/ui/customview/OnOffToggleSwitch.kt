package jp.co.cac.ope.shokken.lab.ui.customview

import android.animation.ValueAnimator
import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import jp.co.cac.ope.shokken.lab.R

class OnOffToggleSwitch(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {
    var isOn = false

    private val switchPadding = 8.0f
    private val textSize = 16.0f * Resources.getSystem().displayMetrics.density

    private val radius get() = height / 2.0f
    private val switchWidth get() = width.toFloat()
    private val switchHeight get() = height.toFloat()

    private val thumbRadius get() = radius - switchPadding
    private val thumbStart get() = radius
    private val thumbEnd get() = switchWidth - radius

    private var switchPosition = 15.5f

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    fun animateSwitch() {
        val start = switchPosition
        val end = if (isOn) thumbEnd else thumbStart

        val animator = ValueAnimator.ofFloat(start, end).apply {
            duration = 200
            addUpdateListener { animation ->
                switchPosition = animation.animatedValue as Float
                invalidate()
            }
        }
        animator.start()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 背景の描画
        paint.color = if (isOn) ContextCompat.getColor(
            context,
            R.color.color_primary
        ) else ContextCompat.getColor(context, R.color.gray_icon)
        paint.style = Paint.Style.FILL
        canvas.drawRoundRect(0.0f, 0.0f, switchWidth, switchHeight, radius, radius, paint)

        // テキストの描画
        paint.color = ContextCompat.getColor(context, R.color.base_white)
        paint.textSize = textSize
        paint.textAlign = Paint.Align.CENTER
        val text = if (isOn) "ON" else "OFF"
        val textX = if (isOn) switchWidth / 3.0f else switchWidth * 2.0f / 3.0f
        val textY = (switchHeight / 2.0f) - ((paint.descent() + paint.ascent()) / 2.0f)
        canvas.drawText(text, textX, textY, paint)

        // 背景色の丸を少し大きく描画
        paint.style = Paint.Style.FILL
        paint.color = if (isOn) ContextCompat.getColor(
            context,
            R.color.color_primary
        ) else ContextCompat.getColor(context, R.color.gray_icon)
        canvas.drawCircle(switchPosition, switchHeight / 2.0f, thumbRadius + 2.0f, paint)

        // 丸の描画
        paint.color = ContextCompat.getColor(context, R.color.base_white)
        canvas.drawCircle(switchPosition, switchHeight / 2.0f, thumbRadius, paint)
    }
}