package jp.co.cac.ope.shokken.lab.ui.analysis

import android.content.Context.MODE_PRIVATE
import android.content.res.Resources
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.core.view.marginBottom
import androidx.core.view.marginTop
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.github.mikephil.charting.data.RadarData
import com.github.mikephil.charting.data.RadarDataSet
import com.github.mikephil.charting.data.RadarEntry
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.SelectionVideoAnalysisAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentAnalysisRecordResultBinding
import jp.co.cac.ope.shokken.lab.ui.modal.BottomAnnounceDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.MemoDialogFragment
import jp.co.cac.ope.shokken.lab.utils.AWSCognitoUtil
import jp.co.cac.ope.shokken.lab.utils.LoginLogoutUtil
import java.io.File

class AnalysisRecordResultFragment : Fragment() {
    private var _binding: FragmentAnalysisRecordResultBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var analysisRecordViewModel: AnalysisRecordViewModel

    private lateinit var player: ExoPlayer
    private lateinit var playerView: PlayerView
    private lateinit var fullPlayerView: PlayerView
    private lateinit var noMovie: TextView
    private lateinit var movieFile: File

    private var isExpanded = false

    private var isPlayerPlaying: Boolean = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.GONE

        val analysisRecordResultViewModel =
            ViewModelProvider(this).get(AnalysisRecordResultViewModel::class.java)

        analysisRecordViewModel =
            ViewModelProvider(requireActivity()).get(AnalysisRecordViewModel::class.java)

        _binding = FragmentAnalysisRecordResultBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val titleTextview: TextView = binding.backHeaderBar.headerTitle
        analysisRecordResultViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        Log.d("ActionLog", analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.description()!!)

        val backButton: ImageButton = binding.backHeaderBar.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        movieFile = File(
            requireContext().getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE),
            analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.movie_file_name
        )
        Log.d("ActionLog", movieFile.absolutePath.toString())

        if (analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.movie_file_name!!.isNotEmpty() && movieFile.exists() && movieFile.isFile) {
            Log.d("ActionLog", "Load movie")

            player = ExoPlayer.Builder(requireActivity()).build().apply {
                playWhenReady = false

                // 再生する動画の パス を指定
                val mediaItem = MediaItem.fromUri(movieFile.toUri())
                setMediaItem(mediaItem)

                prepare()
            }

            var firstController = true
            player.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(state: Int) {
                    when (state) {
                        Player.STATE_READY -> {
                            if (firstController) {
                                firstController = false

                                binding.fullMovieLayout.visibility = View.GONE
                                binding.movieLayout.visibility = View.VISIBLE

                                binding.backward.visibility = View.GONE
                                binding.fullBackward.visibility = View.GONE
                                binding.play.visibility = View.VISIBLE
                                binding.fullPlay.visibility = View.VISIBLE
                                binding.replay.visibility = View.GONE
                                binding.fullReplay.visibility = View.GONE
                                binding.pause.visibility = View.GONE
                                binding.fullPause.visibility = View.GONE
                                binding.forward.visibility = View.GONE
                                binding.fullForward.visibility = View.GONE
                            }
                        }

                        Player.STATE_ENDED -> {
                            isPlayerPlaying = false

                            binding.backward.visibility = View.GONE
                            binding.fullBackward.visibility = View.GONE
                            binding.play.visibility = View.GONE
                            binding.fullPlay.visibility = View.GONE
                            binding.replay.visibility = View.VISIBLE
                            binding.fullReplay.visibility = View.VISIBLE
                            binding.pause.visibility = View.GONE
                            binding.fullPause.visibility = View.GONE
                            binding.forward.visibility = View.GONE
                            binding.fullForward.visibility = View.GONE
                        }
                    }
                }
            })

            playerView = PlayerView(requireContext()).apply {
                id = View.generateViewId()
                layoutParams = ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
                )
                visibility = View.GONE
                useController = false
            }
            binding.movieLayout.addView(playerView, 0)

            ConstraintSet().apply {
                clone(binding.movieLayout)

                connect(
                    playerView.id,
                    ConstraintSet.START,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.START
                )
                connect(
                    playerView.id,
                    ConstraintSet.END,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.END
                )

                connect(
                    playerView.id,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )

                connect(
                    playerView.id,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )

                applyTo(binding.movieLayout)
            }

            fullPlayerView = PlayerView(requireContext()).apply {
                id = View.generateViewId()
                layoutParams = ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
                )
                visibility = View.GONE
                useController = false
            }
            binding.fullMovieLayout.addView(fullPlayerView, 0)

            ConstraintSet().apply {
                clone(binding.fullMovieLayout)

                connect(
                    fullPlayerView.id,
                    ConstraintSet.START,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.START
                )
                connect(
                    fullPlayerView.id,
                    ConstraintSet.END,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.END
                )

                connect(
                    fullPlayerView.id,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )

                connect(
                    fullPlayerView.id,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )

                applyTo(binding.fullMovieLayout)
            }

            // control系
            binding.fullsize.setOnClickListener {
                if (!isExpanded) {
                    binding.fullMovieLayout.visibility = View.VISIBLE
                    binding.movieLayout.visibility = View.GONE

                    playerView.player = null
                    playerView.visibility = View.GONE
                    fullPlayerView.player = player
                    fullPlayerView.visibility = View.VISIBLE

                    isExpanded = true
                }
            }
            binding.minisize.setOnClickListener {
                if (isExpanded) {
                    binding.fullMovieLayout.visibility = View.GONE
                    binding.movieLayout.visibility = View.VISIBLE

                    fullPlayerView.player = null
                    fullPlayerView.visibility = View.GONE
                    playerView.player = player
                    playerView.visibility = View.VISIBLE

                    isExpanded = false
                }
            }
            binding.backward.setOnClickListener {
                backward()
            }
            binding.fullBackward.setOnClickListener {
                backward()
            }
            binding.play.setOnClickListener {
                play()
            }
            binding.fullPlay.setOnClickListener {
                play()
            }
            binding.pause.setOnClickListener {
                pause()
            }
            binding.fullPause.setOnClickListener {
                pause()
            }
            binding.replay.setOnClickListener {
                replay()
            }
            binding.fullReplay.setOnClickListener {
                replay()
            }
            binding.forward.setOnClickListener {
                forward()
            }
            binding.fullForward.setOnClickListener {
                forward()
            }

            fullPlayerView.player = null
            fullPlayerView.visibility = View.GONE
            playerView.player = player
            playerView.visibility = View.VISIBLE
        } else {
            Log.d("ActionLog", "Load no movie Image")

            binding.fullsize.visibility = View.GONE
            binding.minisize.visibility = View.GONE
            binding.controlLayout.visibility = View.GONE
            binding.fullControlLayout.visibility = View.GONE

            val movieLayoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                (100 * Resources.getSystem().displayMetrics.density).toInt()
            ).apply {
                startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                topToBottom = R.id.textView1
                topMargin = (20.0f * Resources.getSystem().displayMetrics.density).toInt()
            }

            binding.movieLayout.layoutParams = movieLayoutParams

            noMovie = TextView(requireContext()).apply {
                id = View.generateViewId()
                layoutParams = ConstraintLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                gravity = Gravity.CENTER
                visibility = View.GONE
                text = "ファイルが見つかりません"
            }
            binding.movieLayout.addView(noMovie, 0)

            ConstraintSet().apply {
                clone(binding.movieLayout)

                connect(
                    noMovie.id,
                    ConstraintSet.START,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.START
                )
                connect(
                    noMovie.id,
                    ConstraintSet.END,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.END
                )

                connect(
                    noMovie.id,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )

                connect(
                    noMovie.id,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )

                applyTo(binding.movieLayout)
            }

            noMovie.visibility = View.VISIBLE
        }

        val sorted =
            analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.expression_analysis!!.sortedBy { it.order }

        val radarLabels = listOf(
            binding.radarLabel1,
            binding.radarLabel2,
            binding.radarLabel3,
            binding.radarLabel4,
            binding.radarLabel5
        )
        val radarLabelScores = listOf(
            binding.radarLabel1Score,
            binding.radarLabel2Score,
            binding.radarLabel3Score,
            binding.radarLabel4Score,
            binding.radarLabel5Score
        )
        val radarLabelButtons = listOf(
            binding.radarLabel1Button,
            binding.radarLabel2Button,
            binding.radarLabel3Button,
            binding.radarLabel4Button,
            binding.radarLabel5Button
        )
        if (sorted != null && sorted.size >= 5) {
            // グラフ基本設定
            binding.radarChart.legend.isEnabled = false
            binding.radarChart.isHighlightPerTapEnabled = false
            binding.radarChart.isRotationEnabled = false
            binding.radarChart.webColorInner =
                ContextCompat.getColor(requireContext(), R.color.base_shadow_30)
            binding.radarChart.webLineWidthInner = 1.5f
            binding.radarChart.webColor =
                ContextCompat.getColor(requireContext(), R.color.base_shadow_20)
            binding.radarChart.webLineWidth = 1.0f
            binding.radarChart.description.isEnabled = false

            // X軸設定
            binding.radarChart.xAxis.setDrawLabels(false)

            // Y軸設定
            binding.radarChart.yAxis.axisMinimum = 0.0f
            binding.radarChart.yAxis.axisMaximum = 100.0f
            binding.radarChart.yAxis.setLabelCount(6, true)
            binding.radarChart.yAxis.setDrawLabels(false)

            var entries: ArrayList<RadarEntry> = arrayListOf<RadarEntry>()

            for ((index, value) in sorted.withIndex()) {
                // 名称
                radarLabels[index].text = value.analysis_item_name

                // スコア
                radarLabelScores[index].text = value.analysis_score.toString()

                // ツールチップ
                radarLabelButtons[index].setOnClickListener {
                    BottomAnnounceDialogFragment.newInstance(
                        value.analysis_item_name + "\n" + value.analysis_description,
                        BottomAnnounceDialogFragment.STYLE_NORMAL,
                        object : BottomAnnounceDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }

                            override fun callbackFromDialogBottomButton(text: String) {
                            }
                        })
                        .show(
                            requireActivity().supportFragmentManager,
                            "BottomAnnounceDialogFragment"
                        )
                }

                entries.add(RadarEntry(value.analysis_score.toFloat()))
            }

            // データセット
            val dataSet = RadarDataSet(entries, "").apply {
                color = ContextCompat.getColor(requireContext(), R.color.color_primary)
                fillColor = ContextCompat.getColor(requireContext(), R.color.color_primary)
                setDrawFilled(true)
                fillAlpha = 26
                lineWidth = 1.0f
                setDrawValues(false)
            }

            // データセットをChartにセット
            binding.radarChart.data = RadarData(dataSet)
            binding.radarChart.invalidate()
        }

        binding.resultFaceText.text =
            analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.expression_evaluation

        binding.resultSpeechText.text =
            analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.voice_evaluation

        val sorted2 =
            analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.voice_analysis!!.sortedBy { it.order }

        val titles = listOf(
            binding.title1,
            binding.title2,
            binding.title3,
            binding.title4,
            binding.title5
        )
        val transcriptionBars = listOf(
            binding.transcriptionBar1,
            binding.transcriptionBar2,
            binding.transcriptionBar3,
            binding.transcriptionBar4,
            binding.transcriptionBar5
        )
        val minLabels = listOf(
            binding.minLabel1,
            binding.minLabel2,
            binding.minLabel3,
            binding.minLabel4,
            binding.minLabel5
        )
        val maxLabels = listOf(
            binding.maxLabel1,
            binding.maxLabel2,
            binding.maxLabel3,
            binding.maxLabel4,
            binding.maxLabel5
        )
        var hintButtons = listOf(
            binding.hintButton1,
            binding.hintButton2,
            binding.hintButton3,
            binding.hintButton4,
            binding.hintButton5
        )
        if (sorted != null && sorted2.size >= 5) {
            for ((index, value) in sorted2.withIndex()) {
                // 名称
                titles[index].text = value.voice_item_name

                // バー
                transcriptionBars[index].voiceAnalysisInfoModel = value
                transcriptionBars[index].invalidate()

                // 小名称
                minLabels[index].text = value.min_status_name

                // 大名称
                maxLabels[index].text = value.max_status_name

                hintButtons[index].setOnClickListener {
                    BottomAnnounceDialogFragment.newInstance(
                        value.voice_item_name + "\n" + value.voice_description,
                        BottomAnnounceDialogFragment.STYLE_NORMAL,
                        object : BottomAnnounceDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }

                            override fun callbackFromDialogBottomButton(text: String) {
                            }
                        })
                        .show(
                            requireActivity().supportFragmentManager,
                            "BottomAnnounceDialogFragment"
                        )
                }
            }
        }

        binding.trainingButton.setOnClickListener {
            // 保存せずそのまま遷移
            findNavController().navigate(
                R.id.action_navigation_analysis_record_result_to_training_top,
                null,
                NavOptions.Builder()
                    .setPopUpTo(
                        R.id.navigation_analysis_record_top,
                        true
                    )
                    .build()
            )
            val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
            navView.visibility = View.VISIBLE
        }

        val thisFragment = this

        binding.memoButton.setOnClickListener {
            MemoDialogFragment.newInstance(
                analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.memo!!,
                object : MemoDialogFragment.CallbackListener {
                    override fun callbackFromDialogCloseButton() {
                    }

                    override fun callbackFromDialogSaveButton(newMemo: String) {
                        analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.memo = newMemo


                        //x-id-tokenが存在するAPIを叩く場合、有効期限切れか判定し、切れている場合はログイン画面に遷移させる
                        if (LoginLogoutUtil.checkLoginExpireAndLogout(
                                sharedPreferences,
                                requireContext(),
                                thisFragment,
                                activity
                            )
                        ) {
                        } else {
                            // 通信して模擬面接コンテンツ一覧を編集する
                            SelectionVideoAnalysisAPI.EditSelectionVideoAnalysisGrade(
                                analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.selection_video_grade_id!!,
                                analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.memo!!,
                                sharedPreferences,
                                parentFragmentManager,
                                { result: Boolean, error: Exception? ->
                                    if (error != null) {
                                        // IDトークン切れ
                                        if (error.message == "Unexpected code 401") {
                                            val awsCognitoUtil = AWSCognitoUtil(requireContext())
                                            awsCognitoUtil.reLogin(
                                                parentFragmentManager,
                                                { userSession ->
                                                    // 新しいトークンを保持
                                                    LoginLogoutUtil.setLoginTokenInfo(
                                                        sharedPreferences.edit(),
                                                        userSession!!
                                                    )

                                                    // 通信して模擬面接コンテンツ一覧を編集する
                                                    SelectionVideoAnalysisAPI.EditSelectionVideoAnalysisGrade(
                                                        analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.selection_video_grade_id!!,
                                                        analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.memo!!,
                                                        sharedPreferences,
                                                        parentFragmentManager,
                                                        { result: Boolean, error: Exception? ->
                                                            if (error != null) {
                                                                CommonDialogFragment.newInstance(
                                                                    "選考動画分析結果更新処理でエラーが発生しました。",
                                                                    true,
                                                                    "OK",
                                                                    object :
                                                                        CommonDialogFragment.CallbackListener {
                                                                        override fun callbackFromDialogCloseButton() {
                                                                        }

                                                                        override fun callbackFromDialogCancelButton() {
                                                                        }
                                                                    }
                                                                ).show(
                                                                    parentFragmentManager,
                                                                    "CommonDialogFragment"
                                                                )
                                                            }
                                                        })
                                                },
                                                { exception ->
                                                    LoginLogoutUtil.safeLogout(
                                                        sharedPreferences,
                                                        requireContext(),
                                                        thisFragment,
                                                        activity
                                                    )
                                                })
                                        } else {
                                            CommonDialogFragment.newInstance(
                                                "選考動画分析結果更新処理でエラーが発生しました。",
                                                true,
                                                "OK",
                                                object : CommonDialogFragment.CallbackListener {
                                                    override fun callbackFromDialogCloseButton() {
                                                    }

                                                    override fun callbackFromDialogCancelButton() {
                                                    }
                                                }
                                            ).show(
                                                parentFragmentManager,
                                                "CommonDialogFragment"
                                            )
                                        }
                                    }
                                })
                        }
                    }
                })
                .show(requireActivity().supportFragmentManager, "MemoDialogFragment")
        }

        return root
    }

    override fun onPause() {
        Log.i("ActionLog", "onPause IN")

        if (analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.movie_file_name!!.isNotEmpty() && movieFile.exists() && movieFile.isFile && player.isPlaying) {
            player.pause()
        }

        Log.i("ActionLog", "onPause OUT")

        super.onPause()
    }

    override fun onResume() {
        super.onResume()

        Log.d("ActionLog", "onResume IN")

        if (analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.movie_file_name!!.isNotEmpty() && movieFile.exists() && movieFile.isFile && isPlayerPlaying) {
            player.play()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        if (analysisRecordViewModel.selectionVideoAnalysisGradeInfo?.movie_file_name!!.isNotEmpty() && movieFile.exists() && movieFile.isFile) {
            player.release()
        }

        _binding = null
    }

    private fun backward() {
        val currentPosition = player.currentPosition
        val seekPosition = currentPosition - 10000

        player.seekTo(maxOf(seekPosition, 0))
    }

    private fun play() {
        binding.backward.visibility = View.VISIBLE
        binding.fullBackward.visibility = View.VISIBLE
        binding.play.visibility = View.GONE
        binding.fullPlay.visibility = View.GONE
        binding.replay.visibility = View.GONE
        binding.fullReplay.visibility = View.GONE
        binding.pause.visibility = View.VISIBLE
        binding.fullPause.visibility = View.VISIBLE
        binding.forward.visibility = View.VISIBLE
        binding.fullForward.visibility = View.VISIBLE

        player.play()
    }

    private fun pause() {
        binding.backward.visibility = View.GONE
        binding.fullBackward.visibility = View.GONE
        binding.play.visibility = View.VISIBLE
        binding.fullPlay.visibility = View.VISIBLE
        binding.replay.visibility = View.GONE
        binding.fullReplay.visibility = View.GONE
        binding.pause.visibility = View.GONE
        binding.fullPause.visibility = View.GONE
        binding.forward.visibility = View.GONE
        binding.fullForward.visibility = View.GONE

        player.pause()
    }

    private fun replay() {
        binding.backward.visibility = View.VISIBLE
        binding.fullBackward.visibility = View.VISIBLE
        binding.play.visibility = View.GONE
        binding.fullPlay.visibility = View.GONE
        binding.replay.visibility = View.GONE
        binding.fullReplay.visibility = View.GONE
        binding.pause.visibility = View.VISIBLE
        binding.fullPause.visibility = View.VISIBLE
        binding.forward.visibility = View.VISIBLE
        binding.fullForward.visibility = View.VISIBLE

        player.seekTo(0)
        player.play()
    }

    private fun forward() {
        val currentPosition = player.currentPosition
        val seekPosition = currentPosition + 10000
        val duration = player.duration

        player.seekTo(minOf(seekPosition, duration))
    }
}