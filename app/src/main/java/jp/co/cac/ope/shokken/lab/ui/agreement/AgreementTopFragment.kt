package jp.co.cac.ope.shokken.lab.ui.agreement

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.activity.CreateNewUserActivity
import jp.co.cac.ope.shokken.lab.activity.HomeActivity
import jp.co.cac.ope.shokken.lab.connects.UserAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentAgreementTopBinding
import jp.co.cac.ope.shokken.lab.model.UserModel
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil

class AgreementTopFragment : Fragment() {
    private var _binding: FragmentAgreementTopBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // レイアウトを全画面に拡張
        activity?.window?.setDecorFitsSystemWindows(true)

        var agreementTopViewModel =
            ViewModelProvider(this).get(AgreementTopViewModel::class.java)

        _binding = FragmentAgreementTopBinding.inflate(inflater, container, false)

        val displayMetrics = resources.displayMetrics
        val screenHeightDp = displayMetrics.heightPixels / displayMetrics.density
        if (screenHeightDp <= 640) {
            var topMarginInPx =
                resources.getDimension(R.dimen.agreement_logo_margin_top_h640).toInt()
            var layoutParams = binding.imageView.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = topMarginInPx
            binding.imageView.layoutParams = layoutParams

            topMarginInPx =
                resources.getDimension(R.dimen.agreement_title_margin_top_h640).toInt()
            layoutParams = binding.titleView.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = topMarginInPx
            binding.titleView.layoutParams = layoutParams

            topMarginInPx =
                resources.getDimension(R.dimen.agreement_description_margin_top_h640).toInt()
            layoutParams = binding.descriptionView.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = topMarginInPx
            binding.descriptionView.layoutParams = layoutParams

            topMarginInPx =
                resources.getDimension(R.dimen.agreement_terms_margin_top_h640).toInt()
            layoutParams = binding.textView6.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = topMarginInPx
            binding.textView6.layoutParams = layoutParams

            topMarginInPx =
                resources.getDimension(R.dimen.agreement_privacy_margin_top_h640).toInt()
            layoutParams = binding.textView7.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = topMarginInPx
            binding.textView7.layoutParams = layoutParams

            topMarginInPx =
                resources.getDimension(R.dimen.agreement_check_area_margin_top_h640).toInt()
            layoutParams = binding.agreeView.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = topMarginInPx
            binding.agreeView.layoutParams = layoutParams

            topMarginInPx =
                resources.getDimension(R.dimen.agreement_next_margin_top_h640).toInt()
            layoutParams = binding.startButton.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = topMarginInPx
            binding.startButton.layoutParams = layoutParams
        }

        binding.viewModel = agreementTopViewModel
        binding.lifecycleOwner = viewLifecycleOwner

        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, Context.MODE_PRIVATE)

        val fixAgreement = sharedPreferences.getBoolean("fixAgreement", false)
        if (fixAgreement) {
            binding.startButton.text = getString(R.string.agreement_start)
        }

        binding.checkbox.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.startButton.setBackgroundResource(R.drawable.round_rectangle_25dp_color_primary)
                binding.startButton.isEnabled = true
            } else {
                binding.startButton.setBackgroundResource(R.drawable.round_rectangle_25dp_gray)
                binding.startButton.isEnabled = false
            }
        }

        agreementTopViewModel.navigateToTermsFragment.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                findNavController().navigate(R.id.action_navigation_agreement_top_to_navigation_agreement_terms)
                agreementTopViewModel.resetToTermsFlag()
            }
        }

        agreementTopViewModel.navigateToPrivacyFragment.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                findNavController().navigate(R.id.action_navigation_agreement_top_to_navigation_agreement_privacy)
                agreementTopViewModel.resetToPrivacyFlag()
            }
        }

        agreementTopViewModel.navigateToQuestionActivity.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                if (binding.checkbox.isChecked) {
                    val sharedPreferences =
                        requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, Context.MODE_PRIVATE)

                    val packageInfo = requireActivity().packageManager.getPackageInfo(
                        requireActivity().packageName,
                        0
                    )
                    val versionName = packageInfo.versionName
                    val versionCode = packageInfo.versionCode

                    val editor = sharedPreferences.edit()
                    editor.putString("versionName", versionName)
                    editor.putInt("versionCode", versionCode)
                    editor.apply()

                    if (fixAgreement) {
                        //DB側の利用規約同意フラグを切り替える
                        UserAPI.recentPolicyAgreed(sharedPreferences,
                            parentFragmentManager,
                            { result: Boolean, error: Exception? ->
                                if (result) {
                                    Log.d("ActionLog", "規約同意フラグ更新成功.")
                                } else {
                                    Log.d("ActionLog", "規約同意フラグ更新失敗.")
                                }
                            })

                        val versionName: String = packageInfo.versionName ?: "1.0"
                        val versionCode: String = (packageInfo.versionCode).toString()
                        //userDefaultsを更新する
                        SharedPreferenceUtil.setAppVersion(editor, versionName)
                        SharedPreferenceUtil.setAppBuildNum(editor, versionCode)

                        val intent = Intent(requireContext(), HomeActivity::class.java)
                        startActivity(intent)
                        activity?.finish()
                        agreementTopViewModel.resetToStartQuestionFlag()
                    } else {
                        SharedPreferenceUtil.setAgrees(editor, true)

                        /**
                         * Previously from QuestionCompleteFragment before navigating to CreateNewUserActivity
                         * it call SharedPreferenceUtil.setAuthModel to set the model
                         */
                        val model = UserModel()
                        model.mode = 1 // New graduate mode | refer to api docs
                        SharedPreferenceUtil.setAuthModel(sharedPreferences.edit(), model)

                        val intent = Intent(requireContext(), CreateNewUserActivity::class.java)
                        startActivity(intent)

                        activity?.finish()
                        agreementTopViewModel.resetToStartQuestionFlag()
                    }
                }

                agreementTopViewModel.resetToStartQuestionFlag()
            }
        }

        val root: View = binding.root

        return root
    }

    override fun onResume() {
        super.onResume()

        var agreementTopViewModel =
            ViewModelProvider(this).get(AgreementTopViewModel::class.java)

        if (agreementTopViewModel.isTerms() && agreementTopViewModel.isPrivacy()) {
            binding.checkbox.isEnabled = true
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}