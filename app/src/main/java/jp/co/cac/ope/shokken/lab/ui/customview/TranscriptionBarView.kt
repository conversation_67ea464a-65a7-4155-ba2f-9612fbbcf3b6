package jp.co.cac.ope.shokken.lab.ui.customview

import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.model.ActualInterviewTranscriptionModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewTranscriptionModel
import jp.co.cac.ope.shokken.lab.model.VoiceAnalysisInfoModel

class TranscriptionBarView(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {

    var actualInterviewTranscription: ActualInterviewTranscriptionModel? = null
    var mockInterviewTranscription: MockInterviewTranscriptionModel? = null
    var voiceAnalysisInfoModel: VoiceAnalysisInfoModel? = null
    private var markerBitmap: Bitmap? = null

    init {
        val drawable = ContextCompat.getDrawable(context, R.drawable.icon_marker)
        val bitmap = Bitmap.createBitmap(drawable?.intrinsicWidth!!, drawable?.intrinsicHeight!!, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        drawable?.setBounds(0, 0, canvas.width, canvas.height)
        drawable?.draw(canvas)

        markerBitmap = bitmap
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        var valueMin: Float = 0.0f
        var valueMax: Float = 0.0f
        var value: Float = 0.0f

        if (actualInterviewTranscription != null) {
            valueMin = actualInterviewTranscription?.talk_speed_appropriate_min!!.toFloat()
            valueMax = actualInterviewTranscription?.talk_speed_appropriate_max!!.toFloat()
            value = actualInterviewTranscription?.talk_speed!!.toFloat()
        } else if (mockInterviewTranscription != null) {
            valueMin = mockInterviewTranscription?.talk_speed_appropriate_min!!.toFloat()
            valueMax = mockInterviewTranscription?.talk_speed_appropriate_max!!.toFloat()
            value = mockInterviewTranscription?.talk_speed!!.toFloat()
        } else if (voiceAnalysisInfoModel != null) {
            val minDisp = voiceAnalysisInfoModel?.min_disp_score!!.toFloat()
            val maxDisp = voiceAnalysisInfoModel?.max_disp_score!!.toFloat()
            valueMin =
                ((voiceAnalysisInfoModel?.min_good_score!!.toFloat() - minDisp) / (maxDisp - minDisp) * 100.0f)
            valueMax =
                ((voiceAnalysisInfoModel?.max_good_score!!.toFloat() - minDisp) / (maxDisp - minDisp) * 100.0f)
            value =
                ((voiceAnalysisInfoModel?.score!!.toFloat() - minDisp) / (maxDisp - minDisp) * 100.0f)
        }

        val lineWidth = 7.0f * Resources.getSystem().displayMetrics.density
        val markerWidth = 18.0f * Resources.getSystem().displayMetrics.density
        val barWidth = width - lineWidth - markerWidth
        val offsetX = (lineWidth / 2.0f) + (markerWidth / 2.0f)
        val barY = height - (lineWidth / 2.0f)

        val paint = Paint().apply {
            isAntiAlias = true
            strokeWidth = lineWidth
            strokeCap = Paint.Cap.ROUND
        }

        paint.color = ContextCompat.getColor(context, R.color.gray_grayout)
        canvas.drawLine(offsetX, barY, offsetX + barWidth, barY, paint)

        paint.color = ContextCompat.getColor(context, R.color.color_primary_40)
        canvas.drawLine(
            offsetX + (barWidth * (valueMin / 100.0f)), barY,
            offsetX + (barWidth * (valueMax / 100.0f)), barY,
            paint
        )

        markerBitmap?.let { bitmap ->
            val markerX = offsetX + (barWidth * (value / 100.0f)) - (markerWidth / 2.0f)
            val markerY = 0.0f

            val ratio = markerWidth.toFloat() / markerBitmap?.width!!.toFloat()

            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, markerWidth.toInt(), (markerBitmap?.height!! * ratio).toInt(), true)
            canvas.drawBitmap(scaledBitmap, markerX, markerY, null)
        }
    }
}