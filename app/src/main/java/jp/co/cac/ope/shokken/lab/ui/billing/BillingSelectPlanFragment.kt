package jp.co.cac.ope.shokken.lab.ui.billing

import android.content.Context
import android.content.Context.MODE_PRIVATE
import android.content.Intent
import android.content.SharedPreferences
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.graphics.Typeface
import android.view.Gravity
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.view.children
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
//import com.android.billingclient.api.AcknowledgePurchaseParams
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.activity.HomeActivity
import jp.co.cac.ope.shokken.lab.databinding.FragmentBillingSelectPlanBinding
import jp.co.cac.ope.shokken.lab.utils.BillingUtil
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil
/*import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.PendingPurchasesParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesResponseListener
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams*/
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.connects.UserAPI
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment

class BillingSelectPlanFragment : Fragment() {
    private var _binding: FragmentBillingSelectPlanBinding? = null
    private val binding get() = _binding!!

    /*private lateinit var billingUtil: BillingUtil
    private lateinit var billingClient: BillingClient*/

    //追加されたラジオボタンを保存するリスト
    private val radioButtons = mutableListOf<RadioButton>()

    //プラン要素を追加する親要素
    private lateinit var parentEle: LinearLayout

    //商品情報の保存用
    //private var selectedProductDetails: ProductDetails? = null

    //課金したプラン名保存用
    private var planName = "1ヵ月プラン"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentBillingSelectPlanBinding.inflate(inflater, container, false)

        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)
        val editor = sharedPreferences.edit()

        //過去一度でも課金したことがあるか
        val isBilled = SharedPreferenceUtil.isAlreadyBilled(sharedPreferences)

        parentEle = binding.innerView

        // タイトル設定
        binding.backHeaderBar.headerTitle.text = "Premiumプランを管理"
        // ヘッダの戻るボタンクリック
        binding.backHeaderBar.backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }
        //キャンセルボタンクリック
        binding.cancelBtn.setOnClickListener{
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        //課金済か否かでタイトル変更
        if(isBilled){
            binding.title1.text = "支払いプランを選択してください。"
            binding.title2.text = "※プランを変更される場合は、現在適用中プランの次回更新日時経過後に自動更新されます。"

            binding.title1.textSize = 16F
            binding.title1.setTypeface(binding.title1.typeface, Typeface.NORMAL)

            val layoutP = binding.title2.layoutParams as ViewGroup.MarginLayoutParams
            layoutP.setMargins(layoutP.leftMargin, 16.dpToPx(), layoutP.rightMargin, 32.dpToPx())
            binding.title2.layoutParams = layoutP
            binding.title2.textSize = 12F
        }

        //billingClient初期化
        //createBillingClinet(requireContext(), sharedPreferences)
        //商品情報を取得
        //queryProductAndPurchases(requireContext(), isBilled)


        binding.confirmBtn.setOnClickListener{
            //全ラジオボタンをループしてチェックされているものを探す
            radioButtons.forEach{radio ->
                if(radio.isChecked){

                    val selectedData = radio.tag
                    if(selectedData is Pair<*, *>){
                        val offerToken = selectedData.first.toString() ?: ""
                        planName = selectedData.second.toString() ?: ""

                        Log.d("debug", "onCreateView: ${offerToken}")
                        Log.d("debug", "onCreateView: ${planName}")

                        //課金モーダル出す
                        //launchPurchaseFlow(offerToken)
                    }


                }
            }
        }

        return binding.root
    }

    override fun onResume() {
        super.onResume()
        // ボトムナビゲーションを非表示にする
        (activity as? HomeActivity)?.hideBottomNavigationView()
    }

    override fun onStop() {
        super.onStop()
        // ボトムナビゲーションを再表示する
        (activity as? HomeActivity)?.showBottomNavigationView()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        /*if (billingClient.isReady) {
            Log.d("ActionLog", "BillingClient: 接続を終了...")
            billingClient.endConnection()
        }*/
    }



    // dpをpxに変換
    fun Int.dpToPx(): Int {
        return (this * resources.displayMetrics.density).toInt()
    }

    //支払いプランの要素を追加する関数
    private fun addProductSelection(price: String, period: String, periodNum: Int, isBilled: Boolean, offerToken: String) {

        // 全体を囲むFrameLayoutを作成
        val frameLayout = FrameLayout(requireContext())
        val frameLayoutLP = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        frameLayout.setBackgroundResource(R.drawable.round_border_5dp_gray)
        frameLayoutLP.setMargins(24.dpToPx(), 16.dpToPx(), 24.dpToPx(), 0)
        frameLayout.layoutParams = frameLayoutLP

        // ラジオボタンを作成
        val radioButton = RadioButton(requireContext())
        val radioButtonLP = FrameLayout.LayoutParams(30.dpToPx(), 30.dpToPx())
        radioButtonLP.setMargins(20.dpToPx(), 0, 0, 0)
        radioButtonLP.gravity = Gravity.CENTER_VERTICAL
        radioButton.layoutParams = radioButtonLP
        radioButton.tag = Pair(offerToken, period + "プラン")//オファートークン情報とプラン名情報を持たせる
        radioButton.id = periodNum
        radioButtons.add(radioButton)//ラジオボタンリストに保存

        // テキスト部分を囲むLinerlayoutを作成
        val linearLayout = LinearLayout(requireContext())
        linearLayout.layoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
        )
        linearLayout.orientation = LinearLayout.VERTICAL

        // プラン名のTextViewを作成
        val textView1 = TextView(requireContext())
        val textView1LP = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        textView1LP.gravity = Gravity.CENTER_HORIZONTAL
        textView1.text = "${period}プラン"
        textView1.setTextColor(Color.parseColor("#382F2D"))
        textView1.textSize = 14f
        textView1.setTypeface(null, Typeface.BOLD)
        textView1LP.setMargins(0, 8.dpToPx(), 0, 0)
        textView1.layoutParams = textView1LP

        // プラン説明のTextViewを作成
        val textView2 = TextView(requireContext())
        val textView2LP = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        val description = if(isBilled) "1ヶ月毎に${price}のお支払い"
        else "初回登録時1週間無料トライアル\n/以降1ヶ月毎に${price}のお支払い"
        textView2.text = description
        textView2.setTextColor(Color.parseColor("#382F2D"))
        textView2.textSize = 12f
        textView2LP.gravity = Gravity.CENTER_HORIZONTAL
        textView2LP.setMargins(0, 0, 0, 8.dpToPx())
        textView2.layoutParams = textView2LP
        // LinearLayoutにTextViewを追加
        linearLayout.addView(textView1)
        linearLayout.addView(textView2)

        // FrameLayoutにRadioButtonとLinearLayoutを追加
        frameLayout.addView(radioButton)
        frameLayout.addView(linearLayout)
        // 親レイアウトに追加
        requireActivity().runOnUiThread(){
            parentEle.addView(frameLayout, 2)
        }

        // タップ時のラジオボタン排他制御を設定
        frameLayout.setOnClickListener { selectRadioButton(radioButton, frameLayout) }
        radioButton.setOnClickListener { selectRadioButton(radioButton, frameLayout) }
    }

    //ラジオボタンタップ時の処理
    private fun selectRadioButton(selected: RadioButton, parentFrame: FrameLayout) {
        radioButtons.forEach { it.isChecked = it == selected } // 他のラジオボタンのチェックを解除

        // すべてのFrameLayoutの背景を元に戻す
        parentEle.children.forEach {
            if (it is FrameLayout) {  //FrameLayoutの場合にのみ処理を行う
                it.setBackgroundResource(R.drawable.round_border_5dp_gray)
            }
        }

        // 選択されたラジオボタンの親FrameLayoutの背景を変更
        if (selected.isChecked) {
            parentFrame.setBackgroundResource(R.drawable.round_border_5dp_color_primary) // 選択された時の背景
        }
    }


    //billingClient初期化
    /*private fun createBillingClinet(context: Context, sharedPreferences: SharedPreferences) {
        val params =
            PendingPurchasesParams.newBuilder().enableOneTimeProducts().enablePrepaidPlans().build()
        billingClient = BillingClient.newBuilder(context).setListener(
            // 購入完了とキャンセルした場合
            object : PurchasesUpdatedListener {
                override fun onPurchasesUpdated(
                    billingResult: BillingResult, purchases: MutableList<Purchase>?
                ) {
                    Log.d("ActionLog", "購入完了かキャンセルがあります")

                    if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
                        Log.d("Billing", "購入")
                        for (purchase in purchases) {
                            handlePurchase(purchase, sharedPreferences) // 購入を承認する処理
                        }
                    } else if (billingResult.responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
                        Log.d("ActionLog", "ユーザーが購入をキャンセルしました")
                    } else {
                        Log.e("ActionLog", "購入エラー: ${billingResult.debugMessage}")
                    }
                }
            })
            .enablePendingPurchases(params)
            .build()
        Log.d("ActionLog", "BillingClient: オブジェクトを新規作成")
    }

    //billingClientにアクセス
    private fun queryProductAndPurchases(context: Context, isBilled: Boolean) {

        // 接続を開始
        if (!billingClient.isReady) {
            Log.d("ActionLog", "BillingClient: 接続を開始...")
            billingClient.startConnection(object : BillingClientStateListener {
                override fun onBillingSetupFinished(
                    billingResult: BillingResult
                ) {
                    // 接続成功
                    if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                        Log.d(
                            "ActionLog",
                            "${context.packageName}, BillingClient: 接続成功:${billingResult.responseCode}"
                        )
                        if (billingClient.isFeatureSupported(BillingClient.FeatureType.SUBSCRIPTIONS).responseCode == BillingClient.BillingResponseCode.OK) {
                            Log.d("ActionLog", "定期購入OK:${billingResult.responseCode}")
                            queryProductDetailsAsync(isBilled)
                        }
                    }

                }

                override fun onBillingServiceDisconnected() {
                    Log.d("ActionLog", "BillingClient: 課金サービスへの接続が切れた")
                }
            })
        }
    }


    // 商品情報取得
    fun queryProductDetailsAsync(isBilled: Boolean) {
        val queryProductDetailsParams = createQueryProductDetailsParams()
        billingClient.queryProductDetailsAsync(queryProductDetailsParams) { billingResult, productDetailsList ->

            // 成功でない
            if (billingResult.responseCode != BillingClient.BillingResponseCode.OK) {
                Log.d(
                    "ActionLog", "商品詳細情報取得失敗(${billingResult.responseCode})"
                )
                CommonDialogFragment.newInstance("通信に失敗しました。",
                    true,
                    "OK",
                    object : CommonDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                        }

                        override fun callbackFromDialogCancelButton() {
                        }
                    }).show(parentFragmentManager, "CommonDialogFragment")
                return@queryProductDetailsAsync
            }
            Log.d(
                "ActionLog",
                "新API　商品詳細情報取得成功(${billingResult.responseCode}), size=${productDetailsList.size}"
            )

            //商品情報を保存しておく
            selectedProductDetails = productDetailsList.firstOrNull()

            //有料プランごとにループ
            for (p in productDetailsList) {

                //プラン月の順にソート
                val sorted = p.subscriptionOfferDetails?.sortedByDescending{convertIsoPeriodToInt(it.pricingPhases.pricingPhaseList[0].billingPeriod)}

                sorted?.forEach{ offer ->

                    val offerId = offer.offerId.toString()
                    val isTrial = offerId.contains("traial", ignoreCase = true)

                    //支払いプラン要素を追加//無料トライアル以外の場合に表示
                    if(!isTrial){
                        offer.pricingPhases.pricingPhaseList.forEach(){productInfo ->
                            val periodStr = convertIsoPeriodToString(productInfo.billingPeriod)
                            val periodNum = convertIsoPeriodToInt(productInfo.billingPeriod)

                            addProductSelection(productInfo.formattedPrice.toString(), periodStr, periodNum, isBilled, offer.offerToken)
                        }
                    }
                }
            }
        }

    }


    // 定期購入商品取得用パラメータ作成
    private fun createQueryProductDetailsParams(): QueryProductDetailsParams {

        val productList: MutableList<QueryProductDetailsParams.Product> = arrayListOf()
        for (product in AppConstants.PAY_PLAN_PRODUCT_ID_LIST) {
            productList.add(
                QueryProductDetailsParams.Product.newBuilder().setProductId(product)
                    .setProductType(BillingClient.ProductType.SUBS).build()
            )
        }

        val result = QueryProductDetailsParams.newBuilder().setProductList(
            productList
        ).build()
        return result
    }*/

    //ISO8601の期間フォーマットを日本語に変換する
    fun convertIsoPeriodToString(isoPeriod: String): String {
        val regex = """P(?:(\d+)Y)?(?:(\d+)M)?""".toRegex()
        val matchResult = regex.matchEntire(isoPeriod)

        return if (matchResult != null) {
            val years = matchResult.groupValues[1].toIntOrNull() ?: 0
            val months = matchResult.groupValues[2].toIntOrNull() ?: 0

            val totalMonths = years * 12 + months

            when (totalMonths) {
                1 -> "1ヶ月"
                else -> "${totalMonths}ヶ月"
            }
        } else {
            "1ヶ月"
        }
    }

    //ISO8601の期間フォーマットを数値に変換する
    fun convertIsoPeriodToInt(isoPeriod: String): Int {
        val regex = """P(?:(\d+)Y)?(?:(\d+)M)?""".toRegex()
        val matchResult = regex.matchEntire(isoPeriod)

        return if (matchResult != null) {
            val years = matchResult.groupValues[1].toIntOrNull() ?: 0
            val months = matchResult.groupValues[2].toIntOrNull() ?: 0

            val totalMonths = years * 12 + months

            when (totalMonths) {
                1 -> 1
                else -> totalMonths
            }
        } else {
            1
        }
    }


    //課金モーダル出す処理
    /*private fun launchPurchaseFlow(offerToken: String) {

        //購入フローのパラメータを設定
        val billingFlowParams = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(
                listOf(
                    selectedProductDetails?.let {
                        BillingFlowParams.ProductDetailsParams.newBuilder()
                            .setProductDetails(it)
                            .setOfferToken(offerToken)
                            .build()
                    }
                )
            )
            .build()

        //購入フロー起動
        val billedResult = billingClient.launchBillingFlow(requireActivity(), billingFlowParams)

        // 購入フロー起動結果をログに出力
        if (billedResult.responseCode == BillingClient.BillingResponseCode.OK) {
            Log.d("debug", "購入フロー起動に成功")
        } else {
            Log.e("debug", "購入フロー起動に失敗: ${billedResult.responseCode}")
        }

    }

    private fun handlePurchase(purchase: Purchase, sharedPreferences: SharedPreferences) {
        Log.d("BILLING", "handlePurchase: ")
        // 購入の状態をチェック
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            Log.d("BILLING", "handlePurchase: PURCHASED")
            // まだ消費 or 承認されていないか確認
            if (!purchase.isAcknowledged) {
                Log.d("BILLING", "handlePurchase: 未承認")
                acknowledgePurchase(purchase, sharedPreferences)
            }else{
                Log.d("BILLING", "handlePurchase: 承認済")
            }
        }
    }

    private fun acknowledgePurchase(purchase: Purchase, sharedPreferences: SharedPreferences) {
        val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
            .setPurchaseToken(purchase.purchaseToken)
            .build()

        billingClient.acknowledgePurchase(acknowledgePurchaseParams) { billingResult ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                Log.d("Billing", "購入が承認されました")
                subscriptionApprovedProcess(sharedPreferences)
            } else {
                Log.e("Billing", "購入承認エラー: ${billingResult.debugMessage}")
            }
        }
    }*/

    //課金承認後の処理
    private fun subscriptionApprovedProcess(sharedPreferences: SharedPreferences){
        //ユーザーAPIで課金済フラグをオンにする
        val editReqBody: Map<String, Any> = mapOf(
            "billing_flg_android" to 1
        )
        UserAPI.edit(editReqBody,
            sharedPreferences,
            parentFragmentManager,
            { result: Map<String, Any>, error: Exception? ->
                if (error != null) {
                    CommonDialogFragment.newInstance("通信に失敗しました。", true, "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }
                            override fun callbackFromDialogCancelButton() {
                            }
                        }).show(parentFragmentManager, "CommonDialogFragment")
                }
            })

        val ispaied = SharedPreferenceUtil.isAlreadyBilled(sharedPreferences)

        //課金後チュートリアルを出す//課金後チュートリアルは初回の課金時のみにだす
        if(ispaied){
            //ホーム画面に遷移
            val intent = Intent(requireContext(), HomeActivity::class.java)
            startActivity(intent)
            activity?.finish()
        }else{
            //端末データの課金済フラグをオンにする
            SharedPreferenceUtil.setAlreadyBilledFlg(sharedPreferences.edit(), true)

            val bundle = Bundle().apply{
                putString("planName", planName)
            }
            findNavController().navigate(R.id.action_fragment_billing_select_plan_to_billing_tutorial_1, bundle)
        }
    }
}