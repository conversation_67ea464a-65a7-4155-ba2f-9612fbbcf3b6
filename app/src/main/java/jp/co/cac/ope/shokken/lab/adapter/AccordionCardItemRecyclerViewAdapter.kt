package jp.co.cac.ope.shokken.lab.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.entity.AccordionCardItem

class AccordionCardItemRecyclerViewAdapter(private val items: List<AccordionCardItem>) :
    RecyclerView.Adapter<AccordionCardItemRecyclerViewAdapter.CardViewHolder>() {

    inner class CardViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val titleTextView: TextView = view.findViewById(R.id.headerTitle)
        val arrowImageView: ImageView = view.findViewById(R.id.round_accordion_card_list_view_arrowImageView)
        val detailsRecyclerView: RecyclerView = view.findViewById(R.id.round_accordion_card_list_view_detailsRecyclerView)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CardViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.round_accordion_card_list_view, parent, false)
        return CardViewHolder(view)
    }

    override fun onBindViewHolder(holder: CardViewHolder, position: Int) {
        val item = items[position]

        // ヘッダー部分のデータ設定
        holder.titleTextView.text = item.title
        holder.arrowImageView.setImageResource(
            if (item.isExpanded) R.drawable.ic_arrow_up_color_primary_16dp else R.drawable.ic_arrow_down_color_primary_16dp
        )

        // 詳細リストのアダプターを設定
        holder.detailsRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = AccordionCardDetailItemRecyclerViewAdapter(item.details)
        }

        // 詳細部分の表示/非表示
        holder.detailsRecyclerView.visibility = if (item.isExpanded) View.VISIBLE else View.GONE

        // タップで展開/収縮を切り替え
        holder.itemView.setOnClickListener {
            item.isExpanded = !item.isExpanded
            notifyItemChanged(position)
        }
    }

    override fun getItemCount(): Int = items.size
}