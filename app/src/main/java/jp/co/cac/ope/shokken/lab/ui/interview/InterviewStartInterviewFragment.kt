package jp.co.cac.ope.shokken.lab.ui.interview

import android.Manifest
import android.content.Context.MODE_PRIVATE
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Rect
import android.media.Image
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.GestureDetectorCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.affectiva.vision.Face
import com.affectiva.vision.Frame
import com.affectiva.vision.Frame.Rotation
import com.arthenica.ffmpegkit.FFmpegKit
import com.arthenica.ffmpegkit.FFmpegSession
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.Affectiva.AV.CameraLibrary
import jp.co.cac.ope.shokken.lab.Affectiva.AV.CameraLibrary.CameraLibraryEventListener
import jp.co.cac.ope.shokken.lab.Affectiva.AsyncFrameDetector
import jp.co.cac.ope.shokken.lab.Affectiva.OnDetectorEventListener
import jp.co.cac.ope.shokken.lab.Affectiva.ResultManager.EmotionValuesManager
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.MockInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentInterviewStartInterviewBinding
import jp.co.cac.ope.shokken.lab.model.MockInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewTranscriptionModel
import jp.co.cac.ope.shokken.lab.ui.modal.ActivityIndicatorDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.CustomerIOMockinterviewUtil
import jp.co.cac.ope.shokken.lab.utils.DateUtil
import jp.co.cac.ope.shokken.lab.utils.ImageUtil
import jp.co.cac.ope.shokken.lab.utils.MyselfTextUtil
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil
import org.json.JSONObject
import org.vosk.Recognizer
import org.vosk.android.RecognitionListener
import org.vosk.android.SpeechStreamService
import org.vosk.android.StorageService
import java.io.File
import java.io.FileInputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import java.util.UUID

class InterviewStartInterviewFragment : Fragment(), OnDetectorEventListener,
    CameraLibraryEventListener {

    private var _binding: FragmentInterviewStartInterviewBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var interviewStartInterviewViewModel: InterviewStartInterviewViewModel
    private lateinit var interviewModel: InterviewViewModel

    private val indicator: ActivityIndicatorDialogFragment =
        ActivityIndicatorDialogFragment.newInstance()
    private val redoDialog: CommonDialogFragment =
        CommonDialogFragment.newInstance("自動でスタート画面に戻ります。\n撮影をやり直す際は、再度スタートボタンを押してください。")

    private var mCameraLibrary: CameraLibrary? = null
    private var mSensorRotate: Rotation = Rotation.CW_90
    private var mAsyncDetector: AsyncFrameDetector? = null
    private var mInitialMillis: Long = 0
    private var mTimestamp: Float = -1.0f
    private var mIsHispeedRespnce: Boolean = false
    private var mLastProcssTime: Float = -1.0f
    private var mLastRecieveTime: Float = -1.0f

    private var firstPlayer: ExoPlayer? = null
    private lateinit var lastPlayer: ExoPlayer
    private lateinit var readyingNormalPlayerView: PlayerView
    private lateinit var startingNormalPlayerView: PlayerView
    private lateinit var readyingMySelfPlayerView: PlayerView
    private lateinit var startingMySelfPlayerView: PlayerView
    private var isFirstPlayerPlaying: Boolean = false
    private var isLastPlayerPlaying: Boolean = false

    private lateinit var interviewer_first: String
    private lateinit var interviewer_last: String

    // 音声認識結果
    private var speechIndex: Int = -1
    private var bestTranscription: ArrayList<String> = arrayListOf<String>()
    private var isFinal: ArrayList<Boolean> = arrayListOf<Boolean>()
    private var speechDuration: Long = -1L
    private var speechFinalCount: Int = 0

    // 動画保存
    private lateinit var tempOutputFile: File
    private lateinit var outputFile: File

    // 音声保存
    private var pcmFile: File? = null

    private var readyFlg: Boolean = true
    private var openRestartFlg: Boolean = false
    private var startingFlag: Boolean = false
    private var openScenarioFlg: Boolean = false
    private var autoScrollFlg: Boolean = true
    private var isFileSaveFlg: Boolean = false
    private var firstVideoPlayerLoad: Boolean = false
    private var lastVideoPlayerLoad: Boolean = false

    private var isPreStart: Boolean = false
    private var isStart: Boolean = false

    private val permissionTimer = Handler(Looper.getMainLooper())
    private val permissionRunnable: Runnable = object : Runnable {
        override fun run() {
            permissionTimerUpdate()
            permissionTimer.postDelayed(this, 100L)
        }
    }

    private var interviewTime: Int = 0
    private var countUp: Int = 0
    private val prestartTimer = Handler(Looper.getMainLooper())
    private val prestartRunnable: Runnable = object : Runnable {
        override fun run() {
            prestartTimerUpdate()
            prestartTimer.postDelayed(this, 1000L)
        }
    }
    private val startTimer = Handler(Looper.getMainLooper())
    private val startRunnable: Runnable = object : Runnable {
        override fun run() {
            startTimerUpdate()
            startTimer.postDelayed(this, 1000L)
        }
    }
    private val interviewTimer = Handler(Looper.getMainLooper())
    private val interviewRunnable: Runnable = object : Runnable {
        override fun run() {
            interviewTimerUpdate()
            interviewTimer.postDelayed(this, 1000L)
        }
    }
    private var prestartCount: Int = 3
    private var readyCount: Int = 3
    private var faceDetectTimeStart: Long = 0L
    private var faceDetectTimeEnd: Long = 0L
    private val fileSaveTimer = Handler(Looper.getMainLooper())
    private val fileSaveRunnable: Runnable = object : Runnable {
        override fun run() {
            fileSaveTimerUpdate()
            fileSaveTimer.postDelayed(this, 100L)
        }
    }
    private val afterProcessTimer = Handler(Looper.getMainLooper())
    private val afterProcessRunnable: Runnable = object : Runnable {
        override fun run() {
            afterProcessTimerUpdate()
            afterProcessTimer.postDelayed(this, 1000L)
        }
    }
    private var afterProcessCount: Int = 2

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.GONE

        interviewStartInterviewViewModel =
            ViewModelProvider(this).get(InterviewStartInterviewViewModel::class.java)

        interviewModel =
            ViewModelProvider(requireActivity()).get(InterviewViewModel::class.java)

        _binding = FragmentInterviewStartInterviewBinding.inflate(inflater, container, false)
        val root: View = binding.root

        // 初期値はとりあえず男性
        interviewer_first =
            interviewModel.selectInterview?.interviewer_url_man_first.toString()
        interviewer_last =
            interviewModel.selectInterview?.interviewer_url_man_last.toString()

        // 設定が女性もしくは設定がランダム
        if (SharedPreferenceUtil.getMockInterviewer(sharedPreferences) == 2
            || (SharedPreferenceUtil.getMockInterviewer(sharedPreferences) == 0 && (1..2).random() == 2)
        ) {
            interviewer_first =
                interviewModel.selectInterview?.interviewer_url_woman_first.toString()
            interviewer_last = interviewModel.selectInterview?.interviewer_url_woman_last.toString()
        }

        // 聞くコンテンツの場合は_firstも無いので聞くコンテンツの判定で動画プレイヤーを設定
        if (isListenContents()) {
            firstPlayer = null
            firstVideoPlayerLoad = true
        } else {
            firstPlayer = ExoPlayer.Builder(requireActivity()).build().apply {
                playWhenReady = false

                // 再生する動画の URL を指定
                val mediaItem = MediaItem.fromUri(Uri.parse(interviewer_first))
                setMediaItem(mediaItem)

                prepare()
            }

            firstPlayer?.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(state: Int) {
                    when (state) {
                        Player.STATE_READY -> {
                            firstVideoPlayerLoad = true

                            if (firstVideoPlayerLoad && lastVideoPlayerLoad) {
                                // 通常
                                binding.readyingNormal.startButton.isEnabled = true
                                binding.readyingNormal.startButton.setBackgroundResource(R.drawable.round_rectangle_25dp_color_primary)
                            }
                        }

                        Player.STATE_ENDED -> {
                            isFirstPlayerPlaying = false

                            lastPlayer?.pause()

                            if (startingFlag) {
                                if (openScenarioFlg) {
                                    startingMySelfPlayerView.visibility = View.GONE
                                    lastPlayer?.seekTo(0)
                                    // 先の動画から切り替え
                                    readyingNormalPlayerView.player = null
                                    startingNormalPlayerView.player = null
                                    readyingMySelfPlayerView.player = null
                                    startingMySelfPlayerView.player = lastPlayer
                                    startingMySelfPlayerView.visibility = View.VISIBLE
                                } else {
                                    startingNormalPlayerView.visibility = View.GONE
                                    lastPlayer?.seekTo(0)
                                    // 先の動画から切り替え
                                    readyingNormalPlayerView.player = null
                                    startingNormalPlayerView.player = lastPlayer
                                    readyingMySelfPlayerView.player = null
                                    startingMySelfPlayerView.player = null
                                    startingNormalPlayerView.visibility = View.VISIBLE
                                }
                            } else {
                                if (openScenarioFlg) {
                                    readyingMySelfPlayerView.visibility = View.GONE
                                    lastPlayer?.seekTo(0)
                                    // 先の動画から切り替え
                                    readyingNormalPlayerView.player = null
                                    startingNormalPlayerView.player = null
                                    readyingMySelfPlayerView.player = lastPlayer
                                    startingMySelfPlayerView.player = null
                                    readyingMySelfPlayerView.visibility = View.VISIBLE
                                } else {
                                    readyingNormalPlayerView.visibility = View.GONE
                                    lastPlayer?.seekTo(0)
                                    // 先の動画から切り替え
                                    readyingNormalPlayerView.player = lastPlayer
                                    startingNormalPlayerView.player = null
                                    readyingMySelfPlayerView.player = null
                                    startingMySelfPlayerView.player = null
                                    readyingNormalPlayerView.visibility = View.VISIBLE
                                }
                            }

                            // カウントダウン開始
                            startMockInterview()
                        }
                    }
                }
            })
        }

        lastPlayer = ExoPlayer.Builder(requireActivity()).build().apply {
            playWhenReady = false

            // 再生する動画の URL を指定
            val mediaItem = MediaItem.fromUri(Uri.parse(interviewer_last))
            setMediaItem(mediaItem)

            prepare()
        }

        lastPlayer.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                when (state) {
                    Player.STATE_READY -> {
                        lastVideoPlayerLoad = true

                        if (firstVideoPlayerLoad && lastVideoPlayerLoad) {
                            // 通常
                            binding.readyingNormal.startButton.isEnabled = true
                            binding.readyingNormal.startButton.setBackgroundResource(R.drawable.round_rectangle_25dp_color_primary)
                        }
                    }

                    Player.STATE_ENDED -> {
                        isLastPlayerPlaying = false
                    }
                }
            }
        })

        openScenarioFlg = false

        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED && ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            startCamera(binding.textureView)
        } else {
            requestCameraPermissions(binding.textureView)
            requestAudioPermissions()

            permissionTimer.postDelayed(permissionRunnable, 100L)
        }

        initView()

        setupReadying()
        setupStarting()

        // 最初はReadyingNormal
        readyingNormalPlayerView.visibility = View.GONE
        if (firstPlayer != null) {
            firstPlayer?.seekTo(0)
            readyingNormalPlayerView.player = firstPlayer
            startingNormalPlayerView.player = null
            readyingMySelfPlayerView.player = null
            startingMySelfPlayerView.player = null
        } else {
            lastPlayer?.seekTo(0)
            readyingNormalPlayerView.player = lastPlayer
            startingNormalPlayerView.player = null
            readyingMySelfPlayerView.player = null
            startingMySelfPlayerView.player = null
        }
        readyingNormalPlayerView.visibility = View.VISIBLE

        // 最初の位置はReadyingNormal基準
        binding.readyingNormal.topStatus.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding.readyingNormal.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(this)

                binding.previewLayout.y = binding.readyingNormal.topStatus.y
            }
        })

        return root
    }

    fun permissionTimerUpdate() {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED && ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            Handler(Looper.getMainLooper()).post {
                permissionTimer.removeCallbacks(permissionRunnable)
            }

            Handler(Looper.getMainLooper()).postDelayed({
                startCamera(binding.textureView)
            }, 500)
        }
    }

    override fun onPause() {
        Log.i("ActionLog", "onPause IN")

        if (mCameraLibrary != null) {
            mCameraLibrary!!.closeCamera();
            mCameraLibrary!!.stopBackgroundThread()
            mCameraLibrary!!.SetCameraLibraryEventListener(null)
            //mCameraLibrary = null
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(null);
            if (mAsyncDetector!!.isRunning()) {
                //asyncDetector.stop()
                //asyncDetector.reset()
            }
        }

        if (firstPlayer != null && firstPlayer?.isPlaying!!) {
            firstPlayer?.pause()
        }

        if (lastPlayer.isPlaying) {
            lastPlayer.pause()
        }

        Log.i("ActionLog", "onPause OUT")

        super.onPause()
    }

    override fun onResume() {
        super.onResume()

        Log.d("ActionLog", "onResume IN")

        if (mCameraLibrary != null) {
            mCameraLibrary!!.SetCameraLibraryEventListener(this)
            mCameraLibrary!!.startBackgroundThread()
            mCameraLibrary!!.TryOpenCamera()
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(this);
        }

        if (firstPlayer != null && isFirstPlayerPlaying) {
            firstPlayer?.play()
        }

        if (isLastPlayerPlaying) {
            lastPlayer.play()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        Log.i("ActionLog", "onDestroyView IN")

        if (mCameraLibrary != null) {
            mCameraLibrary!!.closeCamera();
            mCameraLibrary!!.stopBackgroundThread()
            mCameraLibrary!!.SetCameraLibraryEventListener(null)
            mCameraLibrary = null
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(null);
            if (mAsyncDetector!!.isRunning()) {
                mAsyncDetector!!.stop()
                mAsyncDetector!!.reset()
                mAsyncDetector = null
            }
        }

        if (firstPlayer != null) {
            firstPlayer?.release()
        }

        lastPlayer.release()

        Handler(Looper.getMainLooper()).post {
            prestartTimer.removeCallbacks(prestartRunnable)
            startTimer.removeCallbacks(startRunnable)
            interviewTimer.removeCallbacks(interviewRunnable)
            fileSaveTimer.removeCallbacks(fileSaveRunnable)
            afterProcessTimer.removeCallbacks(afterProcessRunnable)
        }

        _binding = null

        Log.i("ActionLog", "onDestroyView OUT")
    }

    override fun onDetach() {
        Log.i("ActionLog", "onDetach IN");

        Log.i("ActionLog", "onDetach OUT");

        super.onDetach();
    }

    // 画面初期表示
    private fun initView() {
        interviewTime =
            DateUtil.string2Minutes(interviewModel.selectInterview?.estimated_time.toString()) * 60

        // 開始、終了のカウント用
        prestartCount = 3
        readyCount = 3
        readyFlg = true
        isFileSaveFlg = false

        openRestartFlg = false
        startingFlag = false
        openScenarioFlg = false

        isPreStart = false
        isStart = false

        autoScrollFlg = true

        // 自己紹介を使わない or 自己紹介が入力されていない
        if (interviewModel.isChecked == true || interviewModel.inputMyselfText.count() <= 0) {
            binding.readyingNormal.mySelfIcon.visibility = View.GONE
            binding.startingNormal.mySelfIcon.visibility = View.GONE
        }
        // 自己紹介を使う or 自己紹介が入っている
        else {
            Log.d("ActionLog", interviewModel.inputMyselfText)
            Log.d(
                "ActionLog",
                MyselfTextUtil.createMyselfTextList(interviewModel.inputMyselfText).toString()
            )

            // 目安時間を設定
            binding.readyingMySelf.myselfScrollView.interviewTime = interviewTime
            binding.startingMySelf.myselfScrollView.interviewTime = interviewTime
            // ルールに基づいて分割したテキストリストを渡しておく
            binding.readyingMySelf.myselfScrollView.textList =
                MyselfTextUtil.createMyselfTextList(interviewModel.inputMyselfText)
            binding.startingMySelf.myselfScrollView.textList =
                MyselfTextUtil.createMyselfTextList(interviewModel.inputMyselfText)
            // テキスト描画
            binding.readyingMySelf.myselfScrollView.reDraw = true
            binding.startingMySelf.myselfScrollView.reDraw = true
        }

        binding.startingNormal.recording.visibility = View.GONE
        binding.startingMySelf.recording.visibility = View.GONE

        binding.startingNormal.buttonSingleLayout.visibility = View.GONE
        binding.startingMySelf.buttonSingleLayout.visibility = View.GONE
        binding.startingNormal.endButton.visibility = View.VISIBLE
        binding.startingMySelf.endButton.visibility = View.VISIBLE
        // 動画保存に関する表示
        binding.startingNormal.prestartText.visibility = View.GONE
    }

    private fun setupReadying() {
        val titleTextview: TextView = binding.readyingNormal.backHeaderBar.headerTitle
        interviewStartInterviewViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        val backButton: ImageButton = binding.readyingNormal.backHeaderBar.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        val mySelfIcon: ImageView = binding.readyingNormal.mySelfIcon
        mySelfIcon.setOnClickListener {
            openScenarioFlg = true

            binding.readyingNormal.constraintLayout.visibility = View.GONE
            binding.startingNormal.constraintLayout.visibility = View.GONE
            binding.readyingMySelf.constraintLayout.visibility = View.VISIBLE
            // 切り替わった位置はreadyingMySelf基準
            binding.readyingMySelf.topStatus.viewTreeObserver.addOnGlobalLayoutListener(
                object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        binding.readyingMySelf.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(
                            this
                        )

                        binding.previewLayout.y = binding.readyingMySelf.topStatus.y
                    }
                })
            readyingMySelfPlayerView.visibility = View.GONE
            if (firstPlayer != null) {
                readyingNormalPlayerView.player = null
                startingNormalPlayerView.player = null
                readyingMySelfPlayerView.player = firstPlayer
                startingMySelfPlayerView.player = null
            } else {
                readyingNormalPlayerView.player = null
                startingNormalPlayerView.player = null
                readyingMySelfPlayerView.player = lastPlayer
                startingMySelfPlayerView.player = null
            }
            readyingMySelfPlayerView.visibility = View.VISIBLE
            binding.startingMySelf.constraintLayout.visibility = View.GONE
        }

        val closeButton: ImageButton = binding.readyingMySelf.closeButton
        closeButton.setOnClickListener {
            openScenarioFlg = false

            binding.readyingNormal.constraintLayout.visibility = View.VISIBLE
            // 切り替わった位置はreadyingNormal基準
            binding.readyingNormal.topStatus.viewTreeObserver.addOnGlobalLayoutListener(
                object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        binding.readyingNormal.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(
                            this
                        )

                        binding.previewLayout.y = binding.readyingNormal.topStatus.y
                    }
                })
            readyingNormalPlayerView.visibility = View.GONE
            if (firstPlayer != null) {
                readyingNormalPlayerView.player = firstPlayer
                startingNormalPlayerView.player = null
                readyingMySelfPlayerView.player = null
                startingMySelfPlayerView.player = null
            } else {
                readyingNormalPlayerView.player = lastPlayer
                startingNormalPlayerView.player = null
                readyingMySelfPlayerView.player = null
                startingMySelfPlayerView.player = null
            }
            readyingNormalPlayerView.visibility = View.VISIBLE
            binding.startingNormal.constraintLayout.visibility = View.GONE
            binding.readyingMySelf.constraintLayout.visibility = View.GONE
            binding.startingMySelf.constraintLayout.visibility = View.GONE
        }

        if (interviewModel.skipMyself) {
            binding.readyingNormal.textView.text = "2/2"
        } else {
            binding.readyingNormal.textView.text = "3/3"
        }

        binding.readyingNormal.timeCountLabel.text = "Ready"
        binding.readyingMySelf.timeCountLabel.text = "Ready"

        binding.readyingNormal.kindLabel.text = interviewModel.selectInterview?.practice_index_name
        binding.readyingMySelf.kindLabel.text = interviewModel.selectInterview?.practice_index_name

        binding.readyingNormal.targetTime.text = String.format(
            Locale.JAPAN,
            "%d分",
            DateUtil.string2Minutes(interviewModel.selectInterview?.estimated_time.toString())
        )
        binding.readyingMySelf.targetTime.text = String.format(
            Locale.JAPAN,
            "%d分",
            DateUtil.string2Minutes(interviewModel.selectInterview?.estimated_time.toString())
        )

        readyingNormalPlayerView = PlayerView(requireContext()).apply {
            id = View.generateViewId()
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
            visibility = View.GONE
            useController = false
        }
        binding.readyingNormal.movieLayout.addView(readyingNormalPlayerView, 0)

        ConstraintSet().apply {
            clone(binding.readyingNormal.movieLayout)

            connect(
                readyingNormalPlayerView.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START
            )
            connect(
                readyingNormalPlayerView.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END
            )

            connect(
                readyingNormalPlayerView.id,
                ConstraintSet.TOP,
                ConstraintSet.PARENT_ID,
                ConstraintSet.TOP
            )

            applyTo(binding.readyingNormal.movieLayout)
        }

        readyingMySelfPlayerView = PlayerView(requireContext()).apply {
            id = View.generateViewId()
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
            visibility = View.GONE
            useController = false
        }
        binding.readyingMySelf.movieLayout.addView(readyingMySelfPlayerView, 0)

        ConstraintSet().apply {
            clone(binding.readyingMySelf.movieLayout)

            connect(
                readyingMySelfPlayerView.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START
            )
            connect(
                readyingMySelfPlayerView.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END
            )

            connect(
                readyingMySelfPlayerView.id,
                ConstraintSet.TOP,
                ConstraintSet.PARENT_ID,
                ConstraintSet.TOP
            )

            applyTo(binding.readyingMySelf.movieLayout)
        }

        // GestureDetector の初期化
        var readingGestureDetector: GestureDetectorCompat =
            GestureDetectorCompat(requireContext(), object : SimpleOnGestureListener() {
                private val SWIPE_X_THRESHOLD = 100
                private val SWIPE_Y_THRESHOLD = 100
                private val SWIPE_VELOCITY_THRESHOLD = 0

                override fun onFling(
                    e1: MotionEvent?,
                    e2: MotionEvent,
                    velocityX: Float,
                    velocityY: Float
                ): Boolean {
                    if (e1 != null && e2 != null) {
                        val deltaX = e2.x - e1.x
                        val deltaY = e2.y - e1.y
                        if (Math.abs(deltaX) > Math.abs(deltaY)) {
                            if (Math.abs(velocityX) > SWIPE_VELOCITY_THRESHOLD) {
                                if (deltaX > SWIPE_X_THRESHOLD) {
                                    Log.d("Gesture", "右にフリック")
                                    //onSwipeRight()
                                } else {
                                    Log.d("Gesture", "左にフリック")
                                    //onSwipeLeft()
                                }
                            }
                        } else {
                            if (Math.abs(velocityY) > SWIPE_VELOCITY_THRESHOLD) {
                                if (deltaY > SWIPE_Y_THRESHOLD) {
                                    Log.d("Gesture", "下にフリック")

                                    if (binding.readyingMySelf.myselfScrollView.textIndex > 0) {
                                        // 前のテキストが表示されるよう移動
                                        binding.readyingMySelf.myselfScrollView.movePoint =
                                            binding.readyingMySelf.myselfScrollView.textTimeWeight[binding.readyingMySelf.myselfScrollView.textIndex - 1]
                                    }
                                } else {
                                    Log.d("Gesture", "上にフリック")

                                    if (binding.readyingMySelf.myselfScrollView.textIndex < binding.readyingMySelf.myselfScrollView.textList.count() - 1) {
                                        // 次のテキストが表示されるよう移動
                                        binding.readyingMySelf.myselfScrollView.movePoint =
                                            binding.readyingMySelf.myselfScrollView.textTimeWeight[binding.readyingMySelf.myselfScrollView.textIndex + 1]
                                    }
                                }
                            }
                        }
                    }
                    return true
                }
            })

        // OnTouchListener を設定
        binding.readyingMySelf.myselfScrollView.setOnTouchListener { _, event ->
            readingGestureDetector.onTouchEvent(event)
            true
        }

        binding.readyingNormal.backMySelfButton.setOnClickListener({
            interviewStartInterviewViewModel.onBackMySelfButtonClick()
        })
        interviewStartInterviewViewModel.navigateToMySelfFragment.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                findNavController().navigate(
                    R.id.action_navigation_interview_start_interview_to_navigation_navigation_interview_my_self,
                    null,
                    NavOptions.Builder()
                        .setPopUpTo(
                            R.id.navigation_interview_my_self,
                            true
                        )
                        .build()
                )
                interviewStartInterviewViewModel.resetBackMySelfButton()
            }
        }

        binding.readyingNormal.startButton.setOnClickListener({
            interviewStartInterviewViewModel.onStartNormalFirstClick()
        })
        interviewStartInterviewViewModel.startNormalFirst.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                // CustomerIOへデータ送信
                CustomerIOMockinterviewUtil.startedShootingMockinterview()

                startingFlag = true

                binding.readyingNormal.constraintLayout.visibility = View.GONE
                binding.startingNormal.constraintLayout.visibility = View.VISIBLE
                // 切り替わった位置はstartingNormal基準
                binding.startingNormal.topStatus.viewTreeObserver.addOnGlobalLayoutListener(
                    object :
                        ViewTreeObserver.OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            binding.startingNormal.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(
                                this
                            )

                            binding.previewLayout.y = binding.startingNormal.topStatus.y
                        }
                    })
                startingNormalPlayerView.visibility = View.GONE
                if (firstPlayer != null) {
                    firstPlayer?.seekTo(0)
                    readyingNormalPlayerView.player = null
                    startingNormalPlayerView.player = firstPlayer
                    readyingMySelfPlayerView.player = null
                    startingMySelfPlayerView.player = null
                } else {
                    lastPlayer?.seekTo(0)
                    readyingNormalPlayerView.player = null
                    startingNormalPlayerView.player = lastPlayer
                    readyingMySelfPlayerView.player = null
                    startingMySelfPlayerView.player = null
                }
                startingNormalPlayerView.visibility = View.VISIBLE
                binding.readyingMySelf.constraintLayout.visibility = View.GONE
                binding.startingMySelf.constraintLayout.visibility = View.GONE

                // カウントダウン開始
                if (firstPlayer != null) {
                    preStartMockInterview()
                } else {
                    startMockInterview()
                }

                interviewStartInterviewViewModel.resetStartNormalFirstButton()
            }
        }

        binding.readyingMySelf.startButton.setOnClickListener({
            interviewStartInterviewViewModel.onStartMySelfFirstClick()
        })
        interviewStartInterviewViewModel.startMySelfFirst.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                // CustomerIOへデータ送信
                CustomerIOMockinterviewUtil.startedShootingMockinterview()

                startingFlag = true

                binding.readyingNormal.constraintLayout.visibility = View.GONE
                binding.startingNormal.constraintLayout.visibility = View.GONE
                binding.readyingMySelf.constraintLayout.visibility = View.GONE
                binding.startingMySelf.constraintLayout.visibility = View.VISIBLE
                // 切り替わり後にSwitchを初期化
                binding.startingMySelf.autoScrollSwitch.viewTreeObserver.addOnGlobalLayoutListener(
                    object :
                        ViewTreeObserver.OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            binding.startingMySelf.autoScrollSwitch.viewTreeObserver.removeOnGlobalLayoutListener(
                                this
                            )

                            binding.startingMySelf.autoScrollSwitch.isOn = autoScrollFlg
                            binding.startingMySelf.autoScrollSwitch.animateSwitch()
                        }
                    })
                // 切り替わった位置はstartingMySelf基準
                binding.startingMySelf.topStatus.viewTreeObserver.addOnGlobalLayoutListener(object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        binding.startingMySelf.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(
                            this
                        )

                        binding.previewLayout.y = binding.startingMySelf.topStatus.y
                    }
                })
                startingMySelfPlayerView.visibility = View.GONE
                if (firstPlayer != null) {
                    firstPlayer?.seekTo(0)
                    readyingNormalPlayerView.player = null
                    startingNormalPlayerView.player = null
                    readyingMySelfPlayerView.player = null
                    startingMySelfPlayerView.player = firstPlayer
                } else {
                    lastPlayer?.seekTo(0)
                    readyingNormalPlayerView.player = null
                    startingNormalPlayerView.player = null
                    readyingMySelfPlayerView.player = null
                    startingMySelfPlayerView.player = lastPlayer
                }
                startingMySelfPlayerView.visibility = View.VISIBLE

                // カウントダウン開始
                if (firstPlayer != null) {
                    preStartMockInterview()
                } else {
                    startMockInterview()
                }

                interviewStartInterviewViewModel.resetStartMySelfFirstButton()
            }
        }
    }

    private fun setupStarting() {
        val titleTextview: TextView = binding.startingNormal.headerBar.headerTitle
        interviewStartInterviewViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
            titleTextview.gravity = Gravity.CENTER
        }

        binding.startingNormal.headerBar.backButton.visibility = View.GONE

        val mySelfIcon: ImageView = binding.startingNormal.mySelfIcon
        mySelfIcon.setOnClickListener {
            openScenarioFlg = true

            binding.readyingNormal.constraintLayout.visibility = View.GONE
            binding.startingNormal.constraintLayout.visibility = View.GONE
            binding.readyingMySelf.constraintLayout.visibility = View.GONE
            binding.startingMySelf.constraintLayout.visibility = View.VISIBLE
            // 切り替わり後にSwitchを初期化
            binding.startingMySelf.autoScrollSwitch.viewTreeObserver.addOnGlobalLayoutListener(
                object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        binding.startingMySelf.autoScrollSwitch.viewTreeObserver.removeOnGlobalLayoutListener(
                            this
                        )

                        binding.startingMySelf.autoScrollSwitch.isOn = autoScrollFlg
                        binding.startingMySelf.autoScrollSwitch.animateSwitch()
                    }
                })
            // 切り替わった位置はstartingMySelf基準
            binding.startingMySelf.topStatus.viewTreeObserver.addOnGlobalLayoutListener(
                object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        binding.startingMySelf.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(
                            this
                        )

                        binding.previewLayout.y = binding.startingMySelf.topStatus.y
                    }
                })
            startingMySelfPlayerView.visibility = View.GONE
            if (isPreStart) {
                readyingNormalPlayerView.player = null
                startingNormalPlayerView.player = null
                readyingMySelfPlayerView.player = null
                startingMySelfPlayerView.player = firstPlayer
            } else if (isStart) {
                readyingNormalPlayerView.player = null
                startingNormalPlayerView.player = null
                readyingMySelfPlayerView.player = null
                startingMySelfPlayerView.player = lastPlayer
            }
            startingMySelfPlayerView.visibility = View.VISIBLE
        }

        val closeButton: ImageButton = binding.startingMySelf.closeButton
        closeButton.setOnClickListener {
            openScenarioFlg = false

            binding.readyingNormal.constraintLayout.visibility = View.GONE
            binding.startingNormal.constraintLayout.visibility = View.VISIBLE
            // 切り替わった位置はstartingNormal基準
            binding.startingNormal.topStatus.viewTreeObserver.addOnGlobalLayoutListener(
                object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        binding.startingNormal.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(
                            this
                        )

                        binding.previewLayout.y = binding.startingNormal.topStatus.y
                    }
                })
            startingNormalPlayerView.visibility = View.GONE
            if (isPreStart) {
                readyingNormalPlayerView.player = null
                startingNormalPlayerView.player = firstPlayer
                readyingMySelfPlayerView.player = null
                startingMySelfPlayerView.player = null
            } else if (isStart) {
                readyingNormalPlayerView.player = null
                startingNormalPlayerView.player = lastPlayer
                readyingMySelfPlayerView.player = null
                startingMySelfPlayerView.player = null
            }
            startingNormalPlayerView.visibility = View.VISIBLE
            binding.readyingMySelf.constraintLayout.visibility = View.GONE
            binding.startingMySelf.constraintLayout.visibility = View.GONE
        }

        binding.startingNormal.seekBar.isVertical = false
        binding.startingNormal.seekBar.seekPoint = 0.0f
        binding.startingNormal.seekBar.invalidate()

        binding.startingMySelf.seekBar.isVertical = true
        binding.startingMySelf.seekBar.seekPoint = 0.0f
        binding.startingMySelf.seekBar.invalidate()

        binding.startingNormal.recording.visibility = View.GONE
        binding.startingMySelf.recording.visibility = View.GONE

        binding.startingNormal.timeCountLabel.text = "00:00"
        binding.startingMySelf.timeCountLabel.text = "00:00"

        binding.startingNormal.kindLabel.text = interviewModel.selectInterview?.practice_index_name
        binding.startingMySelf.kindLabel.text = interviewModel.selectInterview?.practice_index_name

        binding.startingNormal.targetTime.text = String.format(
            Locale.JAPAN,
            "%d分",
            DateUtil.string2Minutes(interviewModel.selectInterview?.estimated_time.toString())
        )
        binding.startingMySelf.targetTime.text = String.format(
            Locale.JAPAN,
            "%d分",
            DateUtil.string2Minutes(interviewModel.selectInterview?.estimated_time.toString())
        )

        startingNormalPlayerView = PlayerView(requireContext()).apply {
            id = View.generateViewId()
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
            visibility = View.GONE
            useController = false
        }
        binding.startingNormal.movieLayout.addView(startingNormalPlayerView, 0)

        ConstraintSet().apply {
            clone(binding.startingNormal.movieLayout)

            connect(
                startingNormalPlayerView.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START
            )
            connect(
                startingNormalPlayerView.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END
            )

            connect(
                startingNormalPlayerView.id,
                ConstraintSet.TOP,
                ConstraintSet.PARENT_ID,
                ConstraintSet.TOP
            )

            applyTo(binding.startingNormal.movieLayout)
        }

        startingMySelfPlayerView = PlayerView(requireContext()).apply {
            id = View.generateViewId()
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
            visibility = View.GONE
            useController = false
        }
        binding.startingMySelf.movieLayout.addView(startingMySelfPlayerView, 0)

        ConstraintSet().apply {
            clone(binding.startingMySelf.movieLayout)

            connect(
                startingMySelfPlayerView.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START
            )
            connect(
                startingMySelfPlayerView.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END
            )

            connect(
                startingMySelfPlayerView.id,
                ConstraintSet.TOP,
                ConstraintSet.PARENT_ID,
                ConstraintSet.TOP
            )

            applyTo(binding.startingMySelf.movieLayout)
        }

        // GestureDetector の初期化
        var startingGestureDetector: GestureDetectorCompat =
            GestureDetectorCompat(requireContext(), object : SimpleOnGestureListener() {
                private val SWIPE_X_THRESHOLD = 100
                private val SWIPE_Y_THRESHOLD = 100
                private val SWIPE_VELOCITY_THRESHOLD = 0

                override fun onFling(
                    e1: MotionEvent?,
                    e2: MotionEvent,
                    velocityX: Float,
                    velocityY: Float
                ): Boolean {
                    if (e1 != null && e2 != null) {
                        val deltaX = e2.x - e1.x
                        val deltaY = e2.y - e1.y
                        if (Math.abs(deltaX) > Math.abs(deltaY)) {
                            if (Math.abs(velocityX) > SWIPE_VELOCITY_THRESHOLD) {
                                if (deltaX > SWIPE_X_THRESHOLD) {
                                    Log.d("Gesture", "右にフリック")
                                    //onSwipeRight()
                                } else {
                                    Log.d("Gesture", "左にフリック")
                                    //onSwipeLeft()
                                }
                            }
                        } else {
                            if (Math.abs(velocityY) > SWIPE_VELOCITY_THRESHOLD) {
                                if (deltaY > SWIPE_Y_THRESHOLD) {
                                    Log.d("Gesture", "下にフリック")

                                    if (!autoScrollFlg && binding.startingMySelf.myselfScrollView.textIndex > 0) {
                                        // 前のテキストが表示されるよう移動
                                        binding.startingMySelf.myselfScrollView.movePoint =
                                            binding.startingMySelf.myselfScrollView.textTimeWeight[binding.startingMySelf.myselfScrollView.textIndex - 1]
                                    }
                                } else {
                                    Log.d("Gesture", "上にフリック")

                                    if (!autoScrollFlg && binding.startingMySelf.myselfScrollView.textIndex < binding.startingMySelf.myselfScrollView.textList.count() - 1) {
                                        // 次のテキストが表示されるよう移動
                                        binding.startingMySelf.myselfScrollView.movePoint =
                                            binding.startingMySelf.myselfScrollView.textTimeWeight[binding.startingMySelf.myselfScrollView.textIndex + 1]
                                    }
                                }
                            }
                        }
                    }
                    return true
                }
            })

        // OnTouchListener を設定
        binding.startingMySelf.myselfScrollView.setOnTouchListener { _, event ->
            startingGestureDetector.onTouchEvent(event)
            true
        }

        binding.startingMySelf.autoScrollSwitch.isOn = autoScrollFlg
        binding.startingMySelf.autoScrollSwitch.setOnClickListener({
            autoScrollFlg = !autoScrollFlg
            binding.startingMySelf.autoScrollSwitch.isOn = autoScrollFlg
            binding.startingMySelf.autoScrollSwitch.animateSwitch()
        })

        binding.startingNormal.redoImage.setOnClickListener({
            redoMockinterview()
        })
        binding.startingMySelf.redoImage.setOnClickListener({
            redoMockinterview()
        })

        binding.startingNormal.redoText.setOnClickListener({
            redoMockinterview()
        })
        binding.startingMySelf.redoText.setOnClickListener({
            redoMockinterview()
        })

        binding.startingNormal.endButton.setOnClickListener({
            stopMockInterview()

            CommonDialogFragment.newInstance(
                "終了しますか？",
                true,
                "結果を見る",
                object : CommonDialogFragment.CallbackListener {
                    override fun callbackFromDialogCloseButton() {
                        // CustomerIOへデータ送信
                        CustomerIOMockinterviewUtil.completedShootingMockinterview()

                        // Make sure we're in the normal layout before showing results
                        openScenarioFlg = false
                        showResult()
                    }

                    override fun callbackFromDialogCancelButton() {
                        // Make sure we're in the normal layout before retrying
                        openScenarioFlg = false
                        redoMockinterview()
                    }
                },
                true,
                "やり直す"
            )
                .show(parentFragmentManager, "CommonDialogFragment")
        })

        binding.startingMySelf.endButton.setOnClickListener({
            stopMockInterview()

            CommonDialogFragment.newInstance(
                "終了しますか？",
                true,
                "結果を見る",
                object : CommonDialogFragment.CallbackListener {
                    override fun callbackFromDialogCloseButton() {
                        // CustomerIOへデータ送信
                        CustomerIOMockinterviewUtil.completedShootingMockinterview()

                        // Make sure we're in the normal layout before showing results
                        openScenarioFlg = false
                        showResult()
                    }

                    override fun callbackFromDialogCancelButton() {
                        // Make sure we're in the normal layout before retrying
                        openScenarioFlg = false
                        redoMockinterview()
                    }
                },
                true,
                "やり直す"
            )
                .show(parentFragmentManager, "CommonDialogFragment")
        })
    }

    private fun isListenContents(): Boolean {
        return (interviewModel.selectInterview?.script_hint_new.isNullOrEmpty() && interviewModel.selectInterview?.script_hint_change.isNullOrEmpty())
    }

    private fun commonStartMockInterview() {
        binding.startingNormal.seekBar.isVertical = false
        binding.startingNormal.seekBar.seekPoint = 0.0f
        binding.startingNormal.seekBar.invalidate()
        binding.startingNormal.countDown.visibility = View.VISIBLE
        binding.startingNormal.countDown.setImageResource(R.drawable.img_count3_w)

        binding.startingMySelf.seekBar.isVertical = true
        binding.startingMySelf.seekBar.seekPoint = 0.0f
        binding.startingMySelf.seekBar.invalidate()
        binding.startingMySelf.countDown.visibility = View.VISIBLE
        binding.startingMySelf.countDown.setImageResource(R.drawable.img_count3_w)

        prestartCount = 3

        // カウントアップ
        countUp = 0

        binding.startingNormal.timeCountLabel.text =
            String.format(Locale.JAPAN, "%02d:%02d", countUp / 60, countUp % 60)
        binding.startingMySelf.timeCountLabel.text =
            String.format(Locale.JAPAN, "%02d:%02d", countUp / 60, countUp % 60)

        binding.startingNormal.buttonSingleLayout.visibility = View.VISIBLE
        binding.startingMySelf.buttonSingleLayout.visibility = View.VISIBLE
        binding.startingNormal.endButton.visibility = View.GONE
        binding.startingMySelf.endButton.visibility = View.GONE
        // 動画保存に関する表示
        binding.startingNormal.prestartText.visibility = View.VISIBLE

        autoScrollFlg = true
    }

    private fun preStartMockInterview() {
        isPreStart = true
        isStart = false

        commonStartMockInterview()

        prestartTimer.postDelayed(prestartRunnable, 1000L)
    }

    private fun startMockInterview() {
        readyFlg = false

        isPreStart = false
        isStart = true

        commonStartMockInterview()

        startTimer.postDelayed(startRunnable, 1000L)
    }

    private fun stopMockInterview() {
        fun handleFFmpegError(session: FFmpegSession) {
            Log.e("SpeechLog", "変換失敗: ${session.failStackTrace}")

            session.allLogs.forEach { log ->
                Log.d("SpeechLog", log.message)
            }

            // 標準出力 (stdout)
            val output = session.output
            Log.d("SpeechLog", "Output: $output")

            // 標準エラー (stderr)
            val failStackTrace = session.failStackTrace
            Log.e("SpeechLog", "Fail: $failStackTrace")

            if (pcmFile != null && pcmFile?.exists()!! && pcmFile?.isFile!!) {
                pcmFile?.delete()
            }

            speechIndex = 0
            bestTranscription = arrayListOf<String>()
            isFinal = arrayListOf<Boolean>()
            bestTranscription.add("音声認識処理が失敗しました")
            isFinal.add(true)

            isFileSaveFlg = false
        }
        Handler(Looper.getMainLooper()).post {
            interviewTimer.removeCallbacks(interviewRunnable)
        }

        // 録画停止
        mCameraLibrary!!.closeCamera();
        mCameraLibrary!!.stopBackgroundThread()
        mCameraLibrary!!.SetCameraLibraryEventListener(null)
        // ファイル保存後にフラグ下げ
        tempOutputFile.let { file ->
            Log.d("ActionLog", "OutputFile: " + tempOutputFile.absolutePath + " DONE")

            FFmpegKit.executeAsync("-i ${tempOutputFile.absolutePath} -vf hflip ${outputFile.absolutePath}") { session ->
                val returnCode = session.returnCode
                if (returnCode.isValueSuccess) {
                    // 変換したので削除
                    if (tempOutputFile.exists() && tempOutputFile.isFile) {
                        tempOutputFile.delete()
                    }

                    // 聞くコンテンツの場合は文字認識を使わないが一応初期化と初期値を入れておく
                    if (isListenContents()) {
                        bestTranscription = arrayListOf<String>()
                        isFinal = arrayListOf<Boolean>()
                        bestTranscription.add("")
                        isFinal.add(true)
                        speechDuration = 0L

                        isFileSaveFlg = false

                        mCameraLibrary!!.SetCameraLibraryEventListener(this)
                        mCameraLibrary!!.startBackgroundThread()
                        mCameraLibrary!!.TryOpenCamera()
                    } else {
                        Log.d("SpeechLog", "Input: ${outputFile.absolutePath}")
                        Log.d("SpeechLog", "Output: ${pcmFile?.absolutePath}")

                        FFmpegKit.executeAsync("-i ${outputFile.absolutePath} -vn -acodec pcm_s16le -ar 16000 -ac 1 ${pcmFile?.absolutePath}") { session ->
                            val returnCode = session.returnCode
                            if (returnCode.isValueSuccess) {
                                Log.d("SpeechLog", "変換成功！")

                                StorageService.unpack(requireContext(), "model-small-ja", "model",
                                    { model ->
                                        val recognizer: Recognizer = Recognizer(model, 16000.0f)

                                        val ais: FileInputStream = pcmFile?.inputStream()!!
                                        //if (ais.skip(44L) != 44L)

                                        var speechStreamService: SpeechStreamService =
                                            SpeechStreamService(recognizer, ais, 16000.0f);
                                        speechStreamService.start(
                                            object : RecognitionListener {
                                                override fun onResult(hypothesis: String?) {
                                                    hypothesis?.let {
                                                        val jsonObj = JSONObject(it)
                                                        val text = jsonObj.optString("text", "")

                                                        Log.d("SpeechLog", "onResult: ${text}")

                                                        bestTranscription[speechIndex] = text
                                                        isFinal[speechIndex] = true;

                                                        speechIndex += 1
                                                        bestTranscription.add("")
                                                        isFinal.add(false)
                                                    }
                                                }

                                                override fun onPartialResult(hypothesis: String?) {
                                                    Log.d(
                                                        "SpeechLog",
                                                        "onPartialResult: ${hypothesis}"
                                                    )
                                                }

                                                override fun onFinalResult(hypothesis: String?) {
                                                    hypothesis?.let {
                                                        val jsonObj = JSONObject(it)
                                                        val text = jsonObj.optString("text", "")

                                                        Log.d("SpeechLog", "onFinalResult: ${text}")

                                                        bestTranscription[speechIndex] = text
                                                        isFinal[speechIndex] = true;

                                                        isFileSaveFlg = false

                                                        Log.d(
                                                            "SpeechLog",
                                                            "onFinalResult DONE: ${bestTranscription}"
                                                        )
                                                    }
                                                }

                                                override fun onError(exception: Exception?) {
                                                    Log.d("SpeechLog", "onError: ${exception}")
                                                }

                                                override fun onTimeout() {
                                                    Log.d("SpeechLog", "onTimeout")
                                                }
                                            })
                                    },
                                    { exception ->
                                        Log.e(
                                            "SpeechLog",
                                            "Failed to unpack the model: ${exception.message}"
                                        )
                                    }
                                )
                            } else {
                                handleFFmpegError(session)
                            }
                        }
                    }
                } else {
                    handleFFmpegError(session)
                }
            }
        }
        // 停止時点の経過時間を保持
        if (speechDuration != -1L) {
            speechDuration = System.currentTimeMillis() - speechDuration
        }
        // 経過時間が記録されていない場合は0秒としておく
        else {
            speechDuration = 0L
        }

        readyFlg = true

        isPreStart = false
        isStart = false

        binding.readyingNormal.countDown.visibility = View.GONE
        binding.startingNormal.countDown.visibility = View.GONE
        binding.readyingMySelf.countDown.visibility = View.GONE
        binding.startingMySelf.countDown.visibility = View.GONE
        // スタートに備えて
        prestartCount = 3
        readyCount = 3

        // CustomerIO用に終了時のタイムスタンプを保存
        faceDetectTimeEnd = System.currentTimeMillis()

        lastPlayer.pause()

        binding.startingNormal.buttonSingleLayout.visibility = View.VISIBLE
        binding.startingMySelf.buttonSingleLayout.visibility = View.VISIBLE
        binding.startingNormal.endButton.visibility = View.GONE
        binding.startingMySelf.endButton.visibility = View.GONE

        binding.startingMySelf.recording.visibility = View.GONE
        binding.startingNormal.recording.visibility = View.GONE
    }

    private fun redoMockinterview() {
        Log.d("ActionLog", "functionName: redoMockinterview, message: やり直す")

        Handler(Looper.getMainLooper()).post {
            prestartTimer.removeCallbacks(prestartRunnable)
            startTimer.removeCallbacks(startRunnable)
        }

        // Make sure we're not in the script/scenario layout
        startingFlag = false
        openScenarioFlg = false

        isPreStart = false
        isStart = true

        // Reset all layout visibilities to ensure proper state
        binding.startingMySelf.constraintLayout.visibility = View.GONE
        binding.readyingMySelf.constraintLayout.visibility = View.GONE
        binding.startingNormal.constraintLayout.visibility = View.GONE
        binding.readyingNormal.constraintLayout.visibility = View.VISIBLE

        // 最初の位置はReadyingNormal基準
        binding.readyingNormal.topStatus.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding.readyingNormal.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(this)

                binding.previewLayout.y = binding.readyingNormal.topStatus.y
            }
        })
        // 後処理
        readyingNormalPlayerView.visibility = View.GONE
        if (firstPlayer != null) {
            firstPlayer?.pause()
            firstPlayer?.seekTo(0)

            // 先の動画に切り替え
            readyingNormalPlayerView.player = firstPlayer
            startingNormalPlayerView.player = null
            readyingMySelfPlayerView.player = null
            startingMySelfPlayerView.player = null

            lastPlayer.pause()
            lastPlayer.seekTo(0)
        } else {
            lastPlayer?.pause()
            firstPlayer?.seekTo(0)

            // 先の動画に切り替え
            readyingNormalPlayerView.player = lastPlayer
            startingNormalPlayerView.player = null
            readyingMySelfPlayerView.player = null
            startingMySelfPlayerView.player = null
        }
        readyingNormalPlayerView.visibility = View.VISIBLE
        binding.startingNormal.constraintLayout.visibility = View.GONE
        binding.readyingMySelf.constraintLayout.visibility = View.GONE
        binding.startingMySelf.constraintLayout.visibility = View.GONE

        // CustomerIOへデータ送信
        // 解析結果を持ってないのでresultは送らない
        CustomerIOMockinterviewUtil.completedMockinterview(
            (faceDetectTimeEnd - faceDetectTimeStart).toDouble(),
            null
        )
        // 2秒後にリセット処理
        afterProcessCount = 2
        redoDialog.show(requireActivity().supportFragmentManager, "CommonDialogFragment")
        afterProcessTimer.postDelayed(afterProcessRunnable, 1000L)
    }

    private fun showResult() {
        Log.d("ActionLog", "functionName showResult, message: 結果を見る")

        indicator.show(parentFragmentManager, "ActivityIndicatorDialogFragment")

        // Make sure we're not in the script/scenario layout when showing results
        if (openScenarioFlg) {
            openScenarioFlg = false

            // Reset visibility states to ensure proper navigation
            binding.startingMySelf.constraintLayout.visibility = View.GONE
            binding.readyingMySelf.constraintLayout.visibility = View.GONE
            binding.startingNormal.constraintLayout.visibility = View.VISIBLE
            binding.readyingNormal.constraintLayout.visibility = View.GONE
        }

        // 音声認識終了のデッドロック回避カウント
        speechFinalCount = 0
        fileSaveTimer.postDelayed(fileSaveRunnable, 100L)
    }

    private fun prestartTimerUpdate() {
        prestartCount -= 1

        Log.d("ActionLog", "prestartTimerUpdate" + prestartCount.toString())

        when (prestartCount) {
            2 -> {
                binding.startingNormal.countDown.visibility = View.VISIBLE
                binding.startingNormal.countDown.setImageResource(R.drawable.img_count2_w)
                binding.startingMySelf.countDown.visibility = View.VISIBLE
                binding.startingMySelf.countDown.setImageResource(R.drawable.img_count2_w)
            }

            1 -> {
                binding.startingNormal.countDown.visibility = View.VISIBLE
                binding.startingNormal.countDown.setImageResource(R.drawable.img_count1_w)
                binding.startingMySelf.countDown.visibility = View.VISIBLE
                binding.startingMySelf.countDown.setImageResource(R.drawable.img_count1_w)
            }

            0 -> {
                Handler(Looper.getMainLooper()).post {
                    prestartTimer.removeCallbacks(prestartRunnable)
                }

                binding.startingNormal.countDown.visibility = View.GONE
                binding.startingMySelf.countDown.visibility = View.GONE

                // スタートに備えて
                prestartCount = 3

                // 面接官動画再生開始
                isFirstPlayerPlaying = true
                firstPlayer?.play()
            }
        }
    }

    private fun startTimerUpdate() {
        readyCount -= 1

        Log.d("ActionLog", "startTimerUpdate: " + readyCount.toString())

        when (readyCount) {
            2 -> {
                binding.startingNormal.countDown.visibility = View.VISIBLE
                binding.startingNormal.countDown.setImageResource(R.drawable.img_count2_w)
                binding.startingMySelf.countDown.visibility = View.VISIBLE
                binding.startingMySelf.countDown.setImageResource(R.drawable.img_count2_w)
            }

            1 -> {
                binding.startingNormal.countDown.visibility = View.VISIBLE
                binding.startingNormal.countDown.setImageResource(R.drawable.img_count1_w)
                binding.startingMySelf.countDown.visibility = View.VISIBLE
                binding.startingMySelf.countDown.setImageResource(R.drawable.img_count1_w)
            }

            0 -> {
                Handler(Looper.getMainLooper()).post {
                    startTimer.removeCallbacks(startRunnable)
                }

                binding.startingNormal.buttonSingleLayout.visibility = View.GONE
                binding.startingMySelf.buttonSingleLayout.visibility = View.GONE
                binding.startingNormal.endButton.visibility = View.VISIBLE
                binding.startingMySelf.endButton.visibility = View.VISIBLE
                // 動画保存に関する注意非表示
                binding.startingNormal.prestartText.visibility = View.GONE

                binding.startingNormal.countDown.visibility = View.GONE
                binding.startingMySelf.countDown.visibility = View.GONE

                // スタートのあとに備えて
                readyCount = 3

                binding.startingNormal.seekBar.isVertical = false
                binding.startingNormal.seekBar.seekPoint = 0.0f
                binding.startingNormal.seekBar.invalidate()

                binding.startingMySelf.seekBar.isVertical = true
                binding.startingMySelf.seekBar.seekPoint = 0.0f
                binding.startingMySelf.seekBar.invalidate()

                binding.startingMySelf.myselfScrollView.movePoint = 0.0f
                binding.startingMySelf.myselfScrollView.invalidate()

                interviewTimer.postDelayed(interviewRunnable, 1000L)

                // Affdex用のタイムスタンプをリセット
                faceDetectTimeStart = System.currentTimeMillis()
                faceDetectTimeEnd = System.currentTimeMillis()
                // 保持している認識結果リストをリセット
                interviewModel.faceDetectData = arrayListOf<MutableMap<String, Any>>()

                // 面接官動画再生開始
                isLastPlayerPlaying = false
                lastPlayer.play()

                // 録画開始
                mCameraLibrary?.startRecordingVideo()

                // 聞くコンテンツの場合は文字認識を使わないが一応初期化と初期値を入れておく
                if (isListenContents()) {
                    // 音声認識用のオーディオ設定
                    speechIndex = 0
                    bestTranscription = arrayListOf<String>()
                    isFinal = arrayListOf<Boolean>()
                    bestTranscription.add("")
                    isFinal.add(true)
                    speechDuration = -1L
                } else {
                    // 音声認識用のオーディオ設定
                    speechIndex = 0
                    bestTranscription = arrayListOf<String>()
                    isFinal = arrayListOf<Boolean>()
                    bestTranscription.add("")
                    isFinal.add(false)
                    speechDuration = 0L
                }

                isFileSaveFlg = true

                binding.startingNormal.recording.visibility = View.VISIBLE
                binding.startingMySelf.recording.visibility = View.VISIBLE
            }
        }
    }

    private fun interviewTimerUpdate() {
        countUp += 1

        binding.startingNormal.timeCountLabel.text =
            String.format(Locale.JAPAN, "%02d:%02d", countUp / 60, countUp % 60)
        binding.startingMySelf.timeCountLabel.text =
            String.format(Locale.JAPAN, "%02d:%02d", countUp / 60, countUp % 60)

        if (interviewTime > countUp) {
            binding.startingNormal.seekBar.seekPoint = countUp.toFloat() / interviewTime.toFloat()
            binding.startingMySelf.seekBar.seekPoint = countUp.toFloat() / interviewTime.toFloat()
        } else {
            binding.startingNormal.seekBar.seekPoint = 1.0f
            binding.startingMySelf.seekBar.seekPoint = 1.0f
        }
        binding.startingNormal.seekBar.invalidate()
        binding.startingMySelf.seekBar.invalidate()

        Log.d("ActionLog", "seekPoint: " + binding.startingNormal.seekBar.seekPoint.toString())
        Log.d("ActionLog", "seekPoint: " + binding.startingMySelf.seekBar.seekPoint.toString())

        // オートスクロールが有効な場合は移動点を自動更新
        if (autoScrollFlg) {
            binding.startingMySelf.myselfScrollView.movePoint =
                countUp.toFloat() / interviewTime.toFloat()
        }

        // 10秒程度余力を持つ
        if (countUp - interviewTime >= AppConstants.MOCK_INTERVIEW_OVER_TIME) {
            stopMockInterview()

            CommonDialogFragment.newInstance("制限時間を経過したため自動的に終了します。",
                true,
                "結果を見る",
                object : CommonDialogFragment.CallbackListener {
                    override fun callbackFromDialogCloseButton() {
                        showResult()
                    }

                    override fun callbackFromDialogCancelButton() {
                    }
                }).show(parentFragmentManager, "CommonDialogFragment")
        }
    }

    private fun afterProcessTimerUpdate() {
        afterProcessCount -= 1

        // ファイルが保存され、指定時間経過した場合
        if (!isFileSaveFlg && afterProcessCount <= 0) {
            Handler(Looper.getMainLooper()).post {
                afterProcessTimer.removeCallbacks(afterProcessRunnable)
            }

            // 保存した動画データがある場合はやり直しの場合使わないので削除
            if (tempOutputFile.exists() && tempOutputFile.isFile) {
                tempOutputFile.delete()
            }

            if (outputFile.exists() && outputFile.isFile) {
                outputFile.delete()
            }

            if (pcmFile != null && pcmFile?.exists()!! && pcmFile?.isFile!!) {
                pcmFile?.delete()
            }

            mCameraLibrary!!.SetCameraLibraryEventListener(this)
            mCameraLibrary!!.startBackgroundThread()
            mCameraLibrary!!.TryOpenCamera()

            redoDialog.dismiss()

            // 画面のリセット処理
            initView()
        }
    }

    private fun fileSaveTimerUpdate() {
        speechFinalCount += 1

        // すべての音声認識タスクが終了している場合はカウントを最大にする
        var isAllFinal: Boolean = true
        isFinal.forEach { item ->
            if (!item) {
                isAllFinal = false
            }
        }

        if (isAllFinal) {
            speechFinalCount = AppConstants.SPEECH_RECOGNIZER_FINAL_WAIT_COUNT
        }

        // ファイルの保存と音声認識の完了を待つ
        if (!isFileSaveFlg && speechFinalCount >= AppConstants.SPEECH_RECOGNIZER_FINAL_WAIT_COUNT) {
            Handler(Looper.getMainLooper()).post {
                fileSaveTimer.removeCallbacks(fileSaveRunnable)
            }

            if (pcmFile != null && pcmFile?.exists()!! && pcmFile?.isFile!!) {
                pcmFile?.delete()
            }

            indicator.dismissWithAnimation()

            // 解析情報取得
            getExpressionAnalysisResult({
                Handler(Looper.getMainLooper()).post {
                    // 結果画面へ
                    findNavController().popBackStack()
                    findNavController().navigate(R.id.action_navigation_interview_face_adjust_to_navigation_interview_result_interview)
                }
            }, {
                mCameraLibrary!!.SetCameraLibraryEventListener(this)
                mCameraLibrary!!.startBackgroundThread()
                mCameraLibrary!!.TryOpenCamera()
            })
        }
    }

    private fun getExpressionAnalysisResult(
        successCallback: () -> Unit,
        failureCallback: () -> Unit
    ) {
        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        // 項番10, 11, 12
        // ・script_hint_newとscript_hint_changeが両方null(聞くコンテンツ)の場合は文字起こし分析結果取得APIは叩かない
        if (isListenContents()) {
            // 文字起こし分析結果取得APIは叩かない

            // 文字起こし分析結果をnilで保持
            interviewModel.mockInterviewTranscription = null

            // 表情分析結果取得
            MockInterviewAPI.GetExpressionAnalysisResult(
                interviewModel.selectInterview?.practice_index_id!!,
                interviewModel.faceDetectData!!,
                sharedPreferences,
                parentFragmentManager,
                { result: MockInterviewGradeInfoModel, error: Exception? ->
                    if (error != null) {
                        CommonDialogFragment.newInstance(
                            "模擬面接設定更新処理でエラーが発生しました。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                    failureCallback()
                                }

                                override fun callbackFromDialogCancelButton() {
                                }
                            }
                        ).show(
                            parentFragmentManager,
                            "CommonDialogFragment"
                        )
                    } else {
                        Log.d(
                            "ActionLog",
                            "functionName: getExpressionAnalysisResult, message: 模擬面解析結果:${result.description()}"
                        )

                        // CustomerIOへデータ送信
                        CustomerIOMockinterviewUtil.completedMockinterview(
                            (faceDetectTimeEnd - faceDetectTimeStart).toDouble(),
                            result
                        )

                        interviewModel.mockInterviewGradeInfo = result
                        interviewModel.mockInterviewGradeInfo?.mock_interview_contents_id =
                            interviewModel.selectInterview!!.mock_interview_contents_id
                        // 保存した動画パス追加
                        interviewModel.mockInterviewGradeInfo?.movie_file_name = outputFile.name
                        successCallback()
                    }
                })
        } else {
            val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
            formatter.timeZone = TimeZone.getTimeZone("UTC")
            formatter.applyPattern("HH:mm:ss")

            var interview_script = interviewModel.inputMyselfText
            // ・練習前に「今回は台本を利用しない」にチェックが入っている場合 空 にする
            if (interviewModel.isChecked) {
                interview_script = ""
            }

            // 音声認識結果を結合する
            val allBestTranscription = StringBuilder("")
            bestTranscription.forEach { item ->
                allBestTranscription.append(item)
            }

            // 文字起こし分析結果取得
            MockInterviewAPI.GetTranscriptionAnalysisResult(
                allBestTranscription.toString(),
                interview_script,
                formatter.format(Date(speechDuration)),
                sharedPreferences,
                parentFragmentManager,
                { result: MockInterviewTranscriptionModel, error: Exception? ->
                    if (error != null) {
                        CommonDialogFragment.newInstance(
                            "模擬面接結果解析の取得でエラーが発生しました。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                    failureCallback()
                                }

                                override fun callbackFromDialogCancelButton() {
                                }
                            }
                        ).show(
                            parentFragmentManager,
                            "CommonDialogFragment"
                        )
                    } else {
                        // 文字起こし分析結果保持
                        interviewModel.mockInterviewTranscription = result

                        // 表情分析結果取得
                        MockInterviewAPI.GetExpressionAnalysisResult(
                            interviewModel.selectInterview?.practice_index_id!!,
                            interviewModel.faceDetectData!!,
                            sharedPreferences,
                            parentFragmentManager,
                            { result: MockInterviewGradeInfoModel, error: Exception? ->
                                if (error != null) {
                                    CommonDialogFragment.newInstance(
                                        "模擬面接設定更新処理でエラーが発生しました。",
                                        true,
                                        "OK",
                                        object : CommonDialogFragment.CallbackListener {
                                            override fun callbackFromDialogCloseButton() {
                                                failureCallback()
                                            }

                                            override fun callbackFromDialogCancelButton() {
                                            }
                                        }
                                    ).show(
                                        parentFragmentManager,
                                        "CommonDialogFragment"
                                    )
                                } else {
                                    Log.d(
                                        "ActionLog",
                                        "functionName: getExpressionAnalysisResult, message: 模擬面解析結果:${result.description()}"
                                    )

                                    // CustomerIOへデータ送信
                                    CustomerIOMockinterviewUtil.completedMockinterview(
                                        (faceDetectTimeEnd - faceDetectTimeStart).toDouble(),
                                        result
                                    )

                                    interviewModel.mockInterviewGradeInfo = result
                                    interviewModel.mockInterviewGradeInfo?.mock_interview_contents_id =
                                        interviewModel.selectInterview!!.mock_interview_contents_id
                                    // 保存した動画パス追加
                                    interviewModel.mockInterviewGradeInfo?.movie_file_name =
                                        outputFile.name

                                    successCallback()
                                }
                            })
                    }
                })
        }
    }

    private fun requestCameraPermissions(textureView: TextureView) {
        Log.d("ActionLog", "requestCameraPermissions IN")

        val requestPermissionLauncherCamera =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (!isGranted) {
                    Log.d("ActionLog", "カメラの権限が必要です")
                }
            }
        requestPermissionLauncherCamera.launch(Manifest.permission.CAMERA)

        Log.d("ActionLog", "requestCameraPermissions OUT")
    }

    private fun requestAudioPermissions() {
        Log.d("ActionLog", "requestAudioPermissions IN")

        val requestPermissionLauncherAudio =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (!isGranted) {
                    Log.d("ActionLog", "マイクの権限が必要です")
                }
            }
        requestPermissionLauncherAudio.launch(Manifest.permission.RECORD_AUDIO)

        Log.d("ActionLog", "requestAudioPermissions OUT")
    }

    private fun startCamera(textureView: TextureView) {
        Log.d("ActionLog", "startCamera IN")

        // ファイル名生成
        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
        formatter.applyPattern("yyyyMMddHHmmss")
        val timestamp: String = formatter.format(Date())
        val uuid = UUID.randomUUID().toString().replace("-", "")
        val dir: File = requireContext().getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE)
        val fileName = "${timestamp}_${uuid}"
        outputFile = File(dir, fileName + ".mp4")
        tempOutputFile = File(dir, fileName + "_flip.mp4")
        pcmFile = File(dir, fileName + ".wav")

        Log.d("ActionLog", "OutputFile: " + outputFile?.absolutePath)

        mCameraLibrary =
            CameraLibrary(requireActivity(), requireContext(), textureView, tempOutputFile)
        mCameraLibrary!!.SetCameraLibraryEventListener(this)
        mCameraLibrary!!.startBackgroundThread()
        mCameraLibrary!!.TryOpenCamera()

        AsyncFrameDetector.NewInstance(requireContext())
        mAsyncDetector = AsyncFrameDetector.getInstance()
        if (mAsyncDetector != null) {
            mInitialMillis = AsyncFrameDetector.getInitialMillis()
            mAsyncDetector!!.setOnDetectorEventListener(this)
        }

        Log.d("ActionLog", "startCamera OUT")
    }

    override fun OnOpenedCamera() {
        if (mCameraLibrary == null) return

        if (mCameraLibrary!!.mSensorOrientation == 90) {
            // mSensorRotate = Frame.ROTATE.BY_90_CW
            mSensorRotate = Rotation.CW_90
        } else if (mCameraLibrary!!.mSensorOrientation == 270) {
            // mSensorRotate = Frame.ROTATE.BY_90_CCW
            mSensorRotate = Rotation.CW_270
        }

        Log.d("ActionLog", "OnOpenedCamera " + mSensorRotate + "OUT")
    }

    override fun OnUpdatePreview(bytes: ByteArray?, width: Int, height: Int) {
        if (mAsyncDetector == null || !mAsyncDetector!!.isRunning() || mCameraLibrary == null) return

        if (binding.startingNormal.recording.visibility == View.VISIBLE
            || binding.startingMySelf.recording.visibility == View.VISIBLE
        ) {
            Handler(Looper.getMainLooper()).post {
                OnUpdatePreviewSub(bytes, width, height)
            }
        }
    }

    override fun OnUpdate2ndPreview(image: Image, width: Int, height: Int) {
        val ratio: Int = (height.toFloat() / binding.textureView.width.toFloat()).toInt()
        val rotatedBitmap = ImageUtil.rotateAndMirrorBitmap(
            ImageUtil.imageToBitmap(image, width, height, ratio)!!,
            90.0f,
            true
        )
        updatePreview(rotatedBitmap)
    }

    fun updatePreview(bitmap: Bitmap) {
        if (bitmap != null && binding.textureView.isAvailable) {
            val canvas = binding.textureView.lockCanvas()
            canvas?.drawBitmap(
                bitmap,
                null,
                Rect(0, 0, binding.textureView.width, binding.textureView.height),
                null
            )
            binding.textureView.unlockCanvasAndPost(canvas!!)
        }
    }

    private fun OnUpdatePreviewSub(bytes: ByteArray?, width: Int, height: Int) {
        //Log.d("ActionLog", "OnUpdatePreviewSub IN")

        if (mAsyncDetector == null || !mAsyncDetector!!.isRunning() || mCameraLibrary == null) return

        var newtimestamp: Float = 0.0f
        var limit: Float = 0.0f

        if (mIsHispeedRespnce) {
            newtimestamp = (System.currentTimeMillis() - mInitialMillis).toFloat() / 100.0f;
            limit = 0.45f
        } else {
            newtimestamp = (System.currentTimeMillis() - mInitialMillis).toFloat() / 1000.0f;
            limit = 0.045f
        }

        val dur: Float = newtimestamp - mTimestamp;
        if (mTimestamp < 0 || dur > limit) {
            if (mAsyncDetector!!.process(bytes, width, height, mSensorRotate, newtimestamp)) {
                mTimestamp = newtimestamp
            }
        }

        //Log.d("ActionLog", "OnUpdatePreviewSub OUT")
    }

    override fun onImageResults(faces: List<Face?>?, image: Frame?, timeStamp: Float) {
        if (mCameraLibrary == null) return
        Handler(Looper.getMainLooper()).post {
            onImageResultsSub(faces, image, timeStamp)
        }
    }

    private fun onImageResultsSub(faces: List<Face?>?, image: Frame?, timeStamp: Float) {
        //Log.d("ActionLog", "onImageResultsSub IN")

        if (mCameraLibrary == null) return

        val timeStamp: Float = timeStamp / 1000.0f
        mLastRecieveTime = (System.currentTimeMillis() - mInitialMillis).toFloat() / 1000.0f
        mLastProcssTime = mLastRecieveTime - timeStamp

        if (mCameraLibrary == null) return

        var detectList: MutableMap<String, Any> = mutableMapOf<String, Any>()

        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
        formatter.timeZone = TimeZone.getTimeZone("UTC")
        formatter.applyPattern("HH:mm:ss.SSS")
        val timestamp = formatter.format(Date(System.currentTimeMillis() - faceDetectTimeStart))

        detectList["timestamp"] = timestamp
        if (faces.isNullOrEmpty()) {
            detectList["disgust"] = 0.0
            detectList["sadness"] = 0.0
            detectList["engagement"] = 0.0
            detectList["cheek_raise"] = 0.0
            detectList["smile"] = 0.0
            detectList["smirk"] = 0.0
            detectList["brow_furrow"] = 0.0
            detectList["eye_widen"] = 0.0
            detectList["inner_brow_raise"] = 0.0
            detectList["brow_raise"] = 0.0
            detectList["attention"] = 0.0
            detectList["lip_pucker"] = 0.0
            detectList["lip_suck"] = 0.0
            detectList["mouth_open"] = 0.0
            detectList["eye_closure"] = 0.0
            detectList["pitch"] = 0.0
            detectList["roll"] = 0.0

            interviewModel.faceDetectData?.add(detectList)
        } else {
            var values: FloatArray = EmotionValuesManager.GetValues(
                (System.currentTimeMillis() - faceDetectTimeStart).toFloat() / 1000.0f,
                faces[0]
            )

            detectList["disgust"] = values[EmotionValuesManager.VALUEINDEX_DISGUST]
            detectList["sadness"] = values[EmotionValuesManager.VALUEINDEX_SADNESS]
            detectList["engagement"] = values[EmotionValuesManager.VALUEINDEX_ENGAGEMENT]
            detectList["cheek_raise"] = values[EmotionValuesManager.VALUEINDEX_CHEEKRAISE]
            detectList["smile"] = values[EmotionValuesManager.VALUEINDEX_SMILE]
            detectList["smirk"] = values[EmotionValuesManager.VALUEINDEX_SMIRK]
            detectList["brow_furrow"] = values[EmotionValuesManager.VALUEINDEX_BROWFURROW]
            detectList["eye_widen"] = values[EmotionValuesManager.VALUEINDEX_EYEWIDEN]
            detectList["inner_brow_raise"] = values[EmotionValuesManager.VALUEINDEX_INNERBROWRAISE]
            detectList["brow_raise"] = values[EmotionValuesManager.VALUEINDEX_BROWRAISE]
            detectList["attention"] = values[EmotionValuesManager.VALUEINDEX_ATTENTION]
            detectList["lip_pucker"] = values[EmotionValuesManager.VALUEINDEX_LIPPUCKER]
            detectList["lip_suck"] = values[EmotionValuesManager.VALUEINDEX_LIPSUCK]
            detectList["mouth_open"] = values[EmotionValuesManager.VALUEINDEX_MOUTHOPEN]
            detectList["eye_closure"] = values[EmotionValuesManager.VALUEINDEX_EYECLOSURE]
            detectList["pitch"] = values[EmotionValuesManager.VALUEINDEX_PITCH]
            detectList["roll"] = values[EmotionValuesManager.VALUEINDEX_ROLL]
        }

        interviewModel.faceDetectData?.add(detectList)
    }
}