package jp.co.cac.ope.shokken.lab.ui.billing

import android.annotation.SuppressLint
import android.content.Context.MODE_PRIVATE
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.core.view.GestureDetectorCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.google.android.gms.common.util.SharedPreferencesUtils
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.activity.HomeActivity
import jp.co.cac.ope.shokken.lab.databinding.FragmentBillingTutorial3Binding
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil

class BillingTutorial3Fragment : Fragment() {
    private var _binding: FragmentBillingTutorial3Binding? = null
    private val binding get() = _binding!!

    private lateinit var gestureDetector: GestureDetectorCompat

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentBillingTutorial3Binding.inflate(inflater, container, false)

        //高さ〜739 は360✕640のデザインを適用する
        val displayMetrics = resources.displayMetrics
        val screenHeightDp = displayMetrics.heightPixels / displayMetrics.density
        if (screenHeightDp < 740){
            val colorPrimaryArea = binding.colorPrimaryArea
            val colorPrimaryAreaParams = colorPrimaryArea.layoutParams as ViewGroup.MarginLayoutParams
            colorPrimaryAreaParams.topMargin = 33.dpToPx()
            colorPrimaryAreaParams.height = 312.dpToPx()
            colorPrimaryArea.layoutParams = colorPrimaryAreaParams

            val imageView = binding.tutorialSpImage
            val imageViewParams = imageView.layoutParams
            imageViewParams.height = 284.dpToPx()
            imageView.layoutParams = imageViewParams
            imageView.setImageResource(R.drawable.img_android_premium_tutorial2_640)
        }

        //skip
        binding.skipButton.setOnClickListener{
            val intent = Intent(requireContext(), HomeActivity::class.java)
            startActivity(intent)
            activity?.finish()
        }


        val root: View = binding.root

        // GestureDetector の初期化
        gestureDetector =
            GestureDetectorCompat(requireContext(), object : SimpleOnGestureListener() {
                private val SWIPE_X_THRESHOLD = 100
                private val SWIPE_Y_THRESHOLD = 100
                private val SWIPE_VELOCITY_THRESHOLD = 0

                override fun onFling(
                    e1: MotionEvent?,
                    e2: MotionEvent,
                    velocityX: Float,
                    velocityY: Float
                ): Boolean {
                    if (e1 != null && e2 != null) {
                        val deltaX = e2.x - e1.x
                        val deltaY = e2.y - e1.y
                        if (Math.abs(deltaX) > Math.abs(deltaY)) {
                            if (Math.abs(velocityX) > SWIPE_VELOCITY_THRESHOLD) {
                                if (deltaX > SWIPE_X_THRESHOLD) {
                                    Log.d("Gesture", "右にフリック")
                                    requireActivity().onBackPressedDispatcher.onBackPressed()
                                } else {
                                    Log.d("Gesture", "左にフリック")
                                    findNavController().navigate(R.id.action_billing_tutorial_3_to_billing_tutorial_4)
                                }
                            }
                        } else {
                            if (Math.abs(velocityY) > SWIPE_VELOCITY_THRESHOLD) {
                                if (deltaY > SWIPE_Y_THRESHOLD) {
                                    Log.d("Gesture", "下にフリック")
                                } else {
                                    Log.d("Gesture", "上にフリック")
                                }
                            }
                        }
                    }
                    return true
                }
            })

        // OnTouchListener を設定
        root.setOnTouchListener { _, event ->
            gestureDetector.onTouchEvent(event)
            true
        }

        return binding.root
    }

    override fun onResume() {
        super.onResume()
        // ボトムナビゲーションを非表示にする
        (activity as? HomeActivity)?.hideBottomNavigationView()
    }

    override fun onStop() {
        super.onStop()
        // ボトムナビゲーションを再表示する
        (activity as? HomeActivity)?.showBottomNavigationView()
    }

    // dpをpxに変換
    private fun Int.dpToPx(): Int {
        return (this * resources.displayMetrics.density).toInt()
    }
}