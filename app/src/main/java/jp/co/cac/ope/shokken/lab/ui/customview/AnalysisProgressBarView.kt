package jp.co.cac.ope.shokken.lab.ui.customview

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import jp.co.cac.ope.shokken.lab.R

class AnalysisProgressBarView(context: Context, attrs: AttributeSet? = null) :
    View(context, attrs) {

    var progressFlg: Boolean = true
    var kid: Int = 0
    var progress: Int = 0

    var targetProgress: Int = 0
        set(value) {
            if (field != value) {
                field = value
                progress = ((100.0f / 3.0f) * ((if (value > 0) value - 1 else 0))).toInt()
            }
        }

    fun isEnd(): Boolean {
        return if (targetProgress in 0..3) {
            progress == (100.0f / 3.0f * targetProgress).toInt()
        } else true
    }

    private val lineWidth = 10.0f * Resources.getSystem().displayMetrics.density

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val heightF = height.toFloat()
        val widthF = width.toFloat()
        val centerY = heightF / 2.0f
        val topY = (heightF - lineWidth) / 2.0f + (lineWidth / 2.0f)

        val grayPaint = Paint().apply {
            style = Paint.Style.STROKE
            strokeWidth = lineWidth
            color = ContextCompat.getColor(context, R.color.gray_grayout)
            isAntiAlias = true
        }

        val mainPaint = Paint().apply {
            style = Paint.Style.STROKE
            strokeWidth = lineWidth
            color = ContextCompat.getColor(context, R.color.color_primary)
            isAntiAlias = true
        }

        // 灰色線
        canvas.drawLine(
            heightF / 2.0f,
            topY,
            widthF - heightF / 2.0f,
            topY,
            grayPaint
        )

        // 左から右にヒュンと動く
        if (progressFlg) {
            var startProg = ((100.0f / 3.0f) * (targetProgress - 1)).toInt()
            var endProg = ((100.0f / 3.0f) * targetProgress).toInt()

            if (endProg > 0) {
                canvas.drawLine(
                    heightF / 2f,
                    topY,
                    heightF / 2f + (widthF - heightF) * (endProg / 100f),
                    topY,
                    mainPaint
                )
            }

            startProg = ((100.0f / 3.0f) * targetProgress).toInt()
            endProg = ((100.0f / 3.0f) * (targetProgress + 1)).toInt()

            if (kid < 40) {
                val baseKid: Int
                val startX: Int
                val endX: Int

                if (kid < 20) {
                    baseKid = ((kid / 20.0f) * (endProg - startProg) + startProg).toInt()
                    startX = startProg
                    endX = baseKid
                } else {
                    baseKid = (((kid - 20.0f) / 20.0f) * (endProg - startProg) + startProg).toInt()
                    startX = baseKid
                    endX = endProg
                }

                canvas.drawLine(
                    heightF / 2.0f + (widthF - heightF) * (startX / 100.0f),
                    topY,
                    heightF / 2.0f + (widthF - heightF) * (endX / 100.0f),
                    topY,
                    mainPaint
                )
            }
        }
        // 右に伸びていくだけ
        else if (progress > 0) {
            val prog = minOf(progress, ((100.0f / 3.0f) * targetProgress).toInt())
            canvas.drawLine(
                heightF / 2.0f,
                topY,
                heightF / 2.0f + (widthF - heightF) * (prog / 100.0f),
                topY,
                mainPaint
            )
        }

        val circlePaint = Paint().apply {
            style = Paint.Style.FILL
            color = ContextCompat.getColor(context, R.color.base_white)
            isAntiAlias = true
        }

        val strokePaint = Paint().apply {
            style = Paint.Style.STROKE
            strokeWidth = 1.0f * Resources.getSystem().displayMetrics.density
            isAntiAlias = true
        }

        val positions = listOf(
            heightF / 2.0f,
            widthF / 2.0f - widthF / 6.0f + heightF / 2.0f,
            widthF / 2.0f + widthF / 6.0f - heightF / 2.0f,
            widthF - heightF / 2.0f
        )

        for ((i, x) in positions.withIndex()) {
            canvas.drawCircle(x, centerY, centerY - strokePaint.strokeWidth / 2f, circlePaint)
            strokePaint.color = if (targetProgress >= i) ContextCompat.getColor(
                context,
                R.color.color_primary
            ) else ContextCompat.getColor(context, R.color.gray_grayout)
            canvas.drawCircle(x, centerY, centerY - strokePaint.strokeWidth / 2f, strokePaint)
        }
    }
}