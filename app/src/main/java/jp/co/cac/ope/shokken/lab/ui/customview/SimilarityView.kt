package jp.co.cac.ope.shokken.lab.ui.customview

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import jp.co.cac.ope.shokken.lab.R

class SimilarityView(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {
    var scriptSimilarity: Int? = null

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val lineWidth = 7.0f * Resources.getSystem().displayMetrics.density

        val width = width.toFloat()
        val height = height.toFloat()
        val centerX = width / 2.0f
        val centerY = height / 2.0f
        val radius = (width / 2.0f) - (lineWidth / 2.0f)

        val grayPaint = Paint().apply {
            color = ContextCompat.getColor(context, R.color.gray_grayout)
            style = Paint.Style.STROKE
            strokeWidth = lineWidth
            isAntiAlias = true
        }

        val mainPaint = Paint().apply {
            color = ContextCompat.getColor(context, R.color.color_primary)
            style = Paint.Style.STROKE
            strokeWidth = lineWidth
            strokeCap = Paint.Cap.ROUND
            isAntiAlias = true
        }

        val textPaint = Paint().apply {
            color = ContextCompat.getColor(context, R.color.base_text)
            textSize = 16.0f * Resources.getSystem().displayMetrics.density
            typeface = Typeface.DEFAULT_BOLD
            textAlign = Paint.Align.CENTER
            isAntiAlias = true
        }

        val grayTextPaint = Paint().apply {
            color = ContextCompat.getColor(context, R.color.gray_border01)
            textSize = 16.0f * Resources.getSystem().displayMetrics.density
            typeface = Typeface.DEFAULT_BOLD
            textAlign = Paint.Align.CENTER
            isAntiAlias = true
        }

        // 背景
        val oval = RectF(centerX - radius, centerY - radius, centerX + radius, centerY + radius)
        canvas.drawArc(oval, 0.0f, 360.0f, false, grayPaint)

        // メインの線
        if(scriptSimilarity == null) return

        if (scriptSimilarity == 100) {
            // 100%時は円
            canvas.drawArc(oval, 0.0f, 360.0f, false, mainPaint)
        } else if (scriptSimilarity!! > 0) {
            // 100%未満は円弧
            val sweepAngle = 360.0f * (scriptSimilarity!! / 100.0f)
            Log.d("ActionLog", "sweepAngle: " + sweepAngle)
            canvas.drawArc(oval, -90.0f, sweepAngle, false, mainPaint)
        }

        // Score
        val displayText =
            if (scriptSimilarity!! >= 0) scriptSimilarity.toString() else "--"
        val textBounds = Rect()
        if(displayText == "--") {
            grayTextPaint.getTextBounds(displayText, 0, displayText.length, textBounds)
            canvas.drawText(displayText, centerX, centerY + 5.0f * Resources.getSystem().displayMetrics.density, grayTextPaint)
        } else {
            textPaint.getTextBounds(displayText, 0, displayText.length, textBounds)
            canvas.drawText(displayText, centerX, centerY + (textBounds.height() / 2.0f), textPaint)
        }
    }
}