package jp.co.cac.ope.shokken.lab.ui.mypage

/*import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.PendingPurchasesParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesResponseListener
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams*/
import android.content.Context
import android.content.Context.MODE_PRIVATE
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.UserAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentMypageBinding
import jp.co.cac.ope.shokken.lab.model.UserModel
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil


class MyPageFragment : Fragment() {

    private var _binding: FragmentMypageBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    //private lateinit var billingClient: BillingClient

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val viewModel =
            ViewModelProvider(this).get(MyPageViewModel::class.java)

        _binding = FragmentMypageBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val sp = requireActivity().getSharedPreferences(
            AppConstants.APP_PREF_NAME,
            MODE_PRIVATE
        )

        // タブバー表示
        val bottomNavigationView =
            requireActivity().findViewById<BottomNavigationView>(R.id.nav_view)
        bottomNavigationView.visibility = View.VISIBLE

        binding.mainPageHeaderBar.headerTitle.text = "マイページ"

        val isLoggedIn = SharedPreferenceUtil.isLoggedIn(sp)
        /*if (isLoggedIn) {
            createBillingClinet(viewModel, requireContext())
        }*/
        init(isLoggedIn, sp, viewModel, requireContext())

        // ユーザデータ取得後画面に反映
        viewModel.userInfoConnectionComplete.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                // 画面の各値を設定
                bindingUserInfo(viewModel)
            }
        }

        // 課金情報取得後画面に反映
        /*viewModel.premiumInfoConnectionComplete.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                // 画面の各値を設定
                bindingPremiumInfo(viewModel)
            }
        }*/

        // 設定をクリック時
        val settingButton: ImageButton = binding.mainPageHeaderBar.settingButton
        settingButton.setOnClickListener {
            findNavController().navigate(R.id.action_navigation_mypage_to_mypage_setting)
        }

        // Premiumにアップグレードをクリック時
        /*binding.partMypageLoginFree.upgradePremiumButton.setOnClickListener {
            Log.d("ActionLog", "課金画面_課金特典導入_初回1週間無料ユーザー画面への遷移")
            findNavController().navigate(R.id.action_mypage_to_billing_introduction)
        }*/

        // ログインをクリック時
        /*binding.partMypageLoginNot.loginButton.setOnClickListener {
            val app = activity?.application as OPELabApplication
            app.setFromScreen2Login("mypage")

            val intent = Intent(requireContext(), LoginActivity::class.java)
            intent.putExtra("fromScreen", "mypage")
            startActivity(intent)
            activity?.finish()
        }

        // アカウント情報を確認・変更をクリック時
        binding.accountConfirmButton.setOnClickListener {
            Log.d("ActionLog", "click accountConfirmButton")
            findNavController().navigate(R.id.action_navigation_mypage_to_user_confirm)
        }*/

        // 予定リストを見るをクリック時
        /*binding.scheduleListButton.setOnClickListener {
            Log.d("ActionLog", "click scheduleButton")
            findNavController().navigate(R.id.action_navigation_mypage_to_interview_schedule_list)
        }*/

        /*binding.partMypageLoginPlanChange.planCangeButton.setOnClickListener {
            Log.d("ActionLog", "click planCangeButton")
            findNavController().navigate(R.id.action_navigation_mypage_to_fragment_billing_select_plan)
        }

        // プラン解約をクリック時
        binding.partMypageLoginPlanChange.cancelPlanTextView.setOnClickListener {
            Log.d("ActionLog", "click cancelPlan")
            findNavController().navigate(R.id.action_navigation_mypage_to_mypage_setting_help)
        }*/
        return root
    }

    // 初期表示
    fun init(
        isLoggedIn: Boolean, sp: SharedPreferences, viewModel: MyPageViewModel, context: Context
    ) {
        /*binding.partMypageName.iconPremiumMemberImage.visibility = View.GONE
        binding.partMypageLoginFree.mainView.visibility = View.GONE
        binding.partMypageLoginTral.mainView.visibility = View.GONE
        binding.partMypageLoginCurrentPlan.mainView.visibility = View.GONE
        //binding.imgCampaignBg.visibility = View.GONE
        binding.planChangeMessageView.visibility = View.GONE
        binding.partMypageLoginPlanChange.mainView.visibility = View.GONE
        binding.partMypageLoginNot.mainView.visibility = View.GONE*/
        //binding.campaignMargin.visibility = View.GONE

        // 既にログイン
        if (isLoggedIn) {
            getUserInfo(sp, viewModel)
            //queryProductAndPurchases(viewModel, context)
            // 未ログイン
        }/* else {
            binding.partMypageLoginNot.mainView.visibility = View.VISIBLE
        }*/
    }


    /*private fun queryProductAndPurchases(viewModel: MyPageViewModel, context: Context) {

        // 接続を開始
        if (!billingClient.isReady) {
            Log.d("ActionLog", "BillingClient: 接続を開始...")
            billingClient.startConnection(object : BillingClientStateListener {
                override fun onBillingSetupFinished(
                    billingResult: BillingResult
                ) {
                    // 接続成功
                    if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                        Log.d(
                            "ActionLog",
                            "${context.packageName}, BillingClient: 接続成功:${billingResult.responseCode}"
                        )
                        if (billingClient.isFeatureSupported(BillingClient.FeatureType.SUBSCRIPTIONS).responseCode == BillingClient.BillingResponseCode.OK) {
                            Log.d("ActionLog", "定期購入OK:${billingResult.responseCode}")
                            queryProductDetailsAsync(viewModel)
                        }
                    }

                }

                override fun onBillingServiceDisconnected() {
                    Log.d("ActionLog", "BillingClient: 課金サービスへの接続が切れた")
                }
            })
        }
    }*/

    /*fun bindingPremiumInfo(viewModel: MyPageViewModel) {

        // 課金してない場合
        if (!viewModel.productModel.isPurchased) {
            Log.d("ActionLog", "未課金ユーザー")
            binding.partMypageLoginFree.mainView.visibility = View.VISIBLE
            return
        }
        Log.d("ActionLog", "課金ユーザー")

        // 共通
        binding.partMypageName.iconPremiumMemberImage.visibility = View.VISIBLE
        binding.partMypageLoginCurrentPlan.mainView.visibility = View.VISIBLE
        binding.partMypageLoginPlanChange.mainView.visibility = View.VISIBLE

        // 自動更新しない設定の場合
        if (!viewModel.productModel.willAutoRenew) {
            binding.partMypageLoginPlanChange.cancelPlanTextView.visibility = View.GONE
            binding.partMypageLoginCurrentPlan.nextUpdateTextView.visibility = View.GONE
        } else {
            binding.partMypageLoginCurrentPlan.nextUpdateTextView.text = viewModel.productModel.displayNextUpdateDate
        }

        // キャンペンの場合、Android仕様状、無料トライアルより優先
        if (viewModel.productModel.isCampaign) {
            binding.imgCampaignBg.visibility = View.VISIBLE
            binding.campaignMargin.visibility = View.VISIBLE
            return
        }

        // 無料トライアルの場合
        if (viewModel.productModel.isTrailPeriod) {
            binding.partMypageLoginTral.mainView.visibility = View.VISIBLE
            return
        }

        val gradeChangeMessage = viewModel.productModel.getGradeChangeMessage()
        if (gradeChangeMessage != null) {
            binding.planChangeMessageView.visibility = View.VISIBLE
            binding.planChangeMessageView.text = viewModel.productModel.getGradeChangeMessage()
        } else {
            binding.planChangeMessageView.visibility = View.GONE
        }

        // 通常
        binding.campaignMargin.visibility = View.VISIBLE
    }*/

    // ユーザー情報を取得
    fun getUserInfo(
        sp: SharedPreferences,
        viewModel: MyPageViewModel
    ) {
        UserAPI.getDetail(
            sp, parentFragmentManager,
            { result: UserModel, error: Exception? ->

                if (error != null) {
                    CommonDialogFragment.newInstance(
                        "通信に失敗しました。", true, "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }

                            override fun callbackFromDialogCancelButton() {
                            }
                        })
                        .show(parentFragmentManager, "CommonDialogFragment")
                    return@getDetail
                }
                viewModel.userModel = result
                viewModel.userInfoConnectionComplete.postValue(true)
            })
    }

    fun bindingUserInfo(viewModel: MyPageViewModel) {
        if (!viewModel.userModel.user_name.isNullOrEmpty()) {
            //binding.partMypageName.nameTextView.text = "${viewModel.userModel.user_name}"
        }
        binding.emailTxt.text =
            if (!viewModel.userModel.email.isNullOrBlank()) viewModel.userModel.email else "-"

        binding.expireTxt.text = "Expires on ${viewModel.userModel.getActivationEndDateDisplay()}"
    }


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null

        //ログイン時のみbillingClientを初期化しているため、billingClientはログイン時のみ接続解除する
        val sp = requireActivity().getSharedPreferences(
            AppConstants.APP_PREF_NAME,
            MODE_PRIVATE
        )
        /*val isLoggedIn = SharedPreferenceUtil.isLoggedIn(sp)
        if(isLoggedIn){
            closeBillingClient()
        }*/
    }

    //======================
    // 課金関連
    //======================

    // Billing disabled

    /*fun createBillingClinet(viewModel: MyPageViewModel, context: Context) {
        val params =
            PendingPurchasesParams.newBuilder().enableOneTimeProducts().enablePrepaidPlans().build()
        billingClient = BillingClient.newBuilder(context).setListener(
            // 購入完了とキャンセルした場合
            object : PurchasesUpdatedListener {
                override fun onPurchasesUpdated(
                    billingResult: BillingResult, purchases: MutableList<Purchase>?
                ) {

                    // 購入完了
                    if (billingResult.responseCode == BillingClient.BillingResponseCode.OK
                        && !purchases.isNullOrEmpty()
                    ) {
                        Log.d("ActionLog", "購入完了")

                        for (purchase in purchases) {

                            Log.d("ActionLog", "===購入完了後===")
                            purchase.printLog()
                            if (!purchase.isAcknowledged) {

                            }
                        }
                    } else if (billingResult.responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
                        Log.d("ActionLog", "購入キャンセル")
                    } else {
                        Log.d("ActionLog", "その他")
                    }
                    //queryPurchasesAsync(viewModel)
                }
            })
            .enablePendingPurchases(params)
            .build()
        Log.d("ActionLog", "BillingClient: オブジェクトを新規作成")
    }

    fun purched(offerToken: String, productDetails: ProductDetails) {
        BillingFlowParams.ProductDetailsParams.newBuilder().setProductDetails(productDetails)
            .setOfferToken(offerToken).build()
    }

    // 接続を終了
    fun closeBillingClient() {

        if (billingClient.isReady) {
            Log.d("ActionLog", "BillingClient: 接続を終了...")
            billingClient.endConnection()
        }
    }

    // 商品情報取得
    fun queryProductDetailsAsync(
        viewModel: MyPageViewModel,
    ) {
        val queryProductDetailsParams = createQueryProductDetailsParams()
        billingClient.queryProductDetailsAsync(queryProductDetailsParams) { billingResult, productDetailsList ->

            // 成功でない
            if (billingResult.responseCode != BillingClient.BillingResponseCode.OK) {
                Log.d(
                    "ActionLog", "商品詳細情報取得失敗(${billingResult.responseCode})"
                )
                CommonDialogFragment.newInstance("通信に失敗しました。",
                    true,
                    "OK",
                    object : CommonDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                        }

                        override fun callbackFromDialogCancelButton() {
                        }
                    }).show(parentFragmentManager, "CommonDialogFragment")
                return@queryProductDetailsAsync
            }
            Log.d(
                "ActionLog",
                "新API　商品詳細情報取得成功(${billingResult.responseCode}), size=${productDetailsList.size}"
            )

            for (p in productDetailsList) {
                p.printLog()
            }

            viewModel.productDetailsList = productDetailsList

            // 購入情報を取得
            queryPurchasesAsync(viewModel)
        }

    }

    private fun queryAcknowledgePurchase(purchase: Purchase, viewModel: MyPageViewModel) {
        Log.d(
            "ActionLog",
            "承認処理中(orderId=${purchase.orderId}, purchaseTime=${
                DateUtil.localDateTime2String(
                    purchase.purchaseTime,
                    "yyyy/MM/dd HH:mm"
                )
            })..."
        )
        val acknowledgePurchaseParams = AcknowledgePurchaseParams
            .newBuilder()
            .setPurchaseToken(purchase.purchaseToken)
            .build()
        billingClient.acknowledgePurchase(
            acknowledgePurchaseParams
        ) { billingResult2 ->
            if (billingResult2.responseCode == BillingClient.BillingResponseCode.OK &&
                purchase.purchaseState == Purchase.PurchaseState.PURCHASED
            ) {
                Log.d(
                    "ActionLog",
                    "承認完了(orderId=${purchase.orderId}, purchaseTime=${
                        DateUtil.localDateTime2String(
                            purchase.purchaseTime,
                            "yyyy/MM/dd HH:mm"
                        )
                    })"
                )
                queryPurchasesAsync(viewModel)
            }
        }
    }

    // 購入情報取得
    private fun queryPurchasesAsync(viewModel: MyPageViewModel) {
        Log.d("ActionLog", "購入情報取得開始...")
        billingClient.queryPurchasesAsync(QueryPurchasesParams.newBuilder()
            .setProductType(BillingClient.ProductType.SUBS).build(),
            object : PurchasesResponseListener {
                override fun onQueryPurchasesResponse(
                    billingResult: BillingResult, purchaseList: MutableList<Purchase>
                ) {
                    // 失敗
                    if (billingResult.responseCode != BillingClient.BillingResponseCode.OK) {
                        Log.d("ActionLog", "購入情報取得失敗(${billingResult.responseCode})")
                        CommonDialogFragment.newInstance("通信に失敗しました。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                }

                                override fun callbackFromDialogCancelButton() {
                                }
                            }).show(parentFragmentManager, "CommonDialogFragment")
                        return
                    }

                    Log.d(
                        "ActionLog",
                        "購入情報取得成功(${billingResult.responseCode}), size=${purchaseList.size}"
                    )

                    viewModel.productModel.setDatas(viewModel.productDetailsList, purchaseList)
                    Log.d("ActionLog", "${viewModel.productModel.description()}")
                    viewModel.premiumInfoConnectionComplete.postValue(true)

                    for (p in purchaseList) {
                        p.printLog()
                        if (p.purchaseState == Purchase.PurchaseState.PURCHASED && !p.isAcknowledged) {
                            Log.d(
                                "ActionLog",
                                "未承認購入があるため、承認処理で中断。orderId=${p.orderId}, purchaseTime=${
                                    DateUtil.localDateTime2String(
                                        p.purchaseTime,
                                        "yyyy/MM/dd HH:mm"
                                    )
                                }"
                            )
                            queryAcknowledgePurchase(p, viewModel)
                            return
                        }
                    }
                }
            })
    }

    // 定期購入商品取得用パラメータ作成
    fun createQueryProductDetailsParams(): QueryProductDetailsParams {

        val productList: MutableList<QueryProductDetailsParams.Product> = arrayListOf()
        for (product in AppConstants.PAY_PLAN_PRODUCT_ID_LIST) {
            productList.add(
                QueryProductDetailsParams.Product.newBuilder().setProductId(product)
                    .setProductType(BillingClient.ProductType.SUBS).build()
            )
        }

        val result = QueryProductDetailsParams.newBuilder().setProductList(
            productList
        ).build()
        return result
    }

    fun ProductDetails.printLog() {
        Log.d("ActionLog", "ProductDetails")
        Log.d("ActionLog", "  productId   : ${this.productId}")
        Log.d("ActionLog", "  productType : ${this.productType}")
        Log.d("ActionLog", "  title       : ${this.title}")
        Log.d("ActionLog", "  name        : ${this.name}")
        Log.d("ActionLog", "  description : ${this.description}")
        this.subscriptionOfferDetails?.forEachIndexed { index, offer ->
            Log.d("ActionLog", "  subscriptionOfferDetails")
            Log.d("ActionLog", "    index      : $index")
            Log.d("ActionLog", "      offerId    : ${offer.offerId}")
            Log.d("ActionLog", "      offerToken : ${offer.offerToken}")
            Log.d("ActionLog", "      offerTags  : ${offer.offerTags}")
            offer.pricingPhases.pricingPhaseList.forEachIndexed { index, list ->
                Log.d("ActionLog", "    pricingPhases")
                Log.d("ActionLog", "      index      : $index")
                Log.d("ActionLog", "        formattedPrice    : ${list.formattedPrice}")
                Log.d("ActionLog", "        priceCurrencyCode : ${list.priceCurrencyCode}")
                Log.d("ActionLog", "        priceAmountMicros : ${list.priceAmountMicros}")
                Log.d("ActionLog", "        billingPeriod     : ${list.billingPeriod}")
                Log.d("ActionLog", "        billingCycleCount : ${list.billingCycleCount}")
                Log.d("ActionLog", "        recurrenceMode    : ${list.recurrenceMode}")
            }
            offer.installmentPlanDetails?.let { plans ->
                Log.d("ActionLog", "    installmentPlanDetails")
                Log.d(
                    "ActionLog",
                    "      subsequentInstallmentPlanCommitmentPaymentsCount : ${plans.subsequentInstallmentPlanCommitmentPaymentsCount}"
                )
                Log.d(
                    "ActionLog",
                    "      installmentPlanCommitmentPaymentsCount           : ${plans.installmentPlanCommitmentPaymentsCount}"
                )
            }
        }
        this.oneTimePurchaseOfferDetails?.let { details ->
            Log.d("ActionLog", "  oneTimePurchaseOfferDetails")
            Log.d("ActionLog", "    formattedPrice : ${details.formattedPrice}")
            Log.d("ActionLog", "    priceAmountMicros : ${details.priceAmountMicros}")
            Log.d("ActionLog", "    priceCurrencyCode : ${details.priceCurrencyCode}")
        }
    }

    fun Purchase.printLog() {
        Log.d("ActionLog", "Purchase")
        Log.d("ActionLog", "  products           : ${this.products}")
        Log.d("ActionLog", "  purchaseToken      : ${this.purchaseToken}")
        Log.d("ActionLog", "  accountIdentifiers : ${this.accountIdentifiers}")
        Log.d("ActionLog", "  developerPayload   : ${this.developerPayload}")
        Log.d("ActionLog", "  isAcknowledged     : ${this.isAcknowledged}")
        Log.d("ActionLog", "  isAutoRenewing     : ${this.isAutoRenewing}")
        Log.d("ActionLog", "  orderId            : ${this.orderId}")
        Log.d("ActionLog", "  originalJson       : ${this.originalJson}")
        Log.d("ActionLog", "  packageName        : ${this.packageName}")
        Log.d("ActionLog", "  purchaseState      : ${this.purchaseState}")
        Log.d("ActionLog", "  purchaseTime       : ${this.purchaseTime}")
        Log.d("ActionLog", "  purchaseTime       : ${DateUtil.localDateTime2String(
            this.purchaseTime,
            "yyyy/MM/dd HH:mm"
        )}")
        Log.d("ActionLog", "  quantity           : ${this.quantity}")
        Log.d("ActionLog", "  signature          : ${this.signature}")
    }*/
}