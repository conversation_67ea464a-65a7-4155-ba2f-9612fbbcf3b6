package jp.co.cac.ope.shokken.lab.ui.actual_interview

import android.Manifest
import android.content.Context.MODE_PRIVATE
import android.content.pm.PackageManager
import android.content.res.Resources
import android.graphics.Rect
import android.media.Image
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ImageButton
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.view.GestureDetectorCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.affectiva.vision.Face
import com.affectiva.vision.Frame
import com.affectiva.vision.Frame.Rotation
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.Affectiva.AV.CameraLibrary
import jp.co.cac.ope.shokken.lab.Affectiva.AV.CameraLibrary.CameraLibraryEventListener
import jp.co.cac.ope.shokken.lab.Affectiva.AsyncFrameDetector
import jp.co.cac.ope.shokken.lab.Affectiva.OnDetectorEventListener
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.UserAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentActualInterviewFaceAdjustBinding
import jp.co.cac.ope.shokken.lab.model.UserModel
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.TrainingAnnounceDialogFragment

class ActualInterviewFaceAdjustFragment : Fragment(), OnDetectorEventListener,
    CameraLibraryEventListener {

    private var _binding: FragmentActualInterviewFaceAdjustBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var gestureDetector: GestureDetectorCompat

    private lateinit var actualInterviewModel: ActualInterviewViewModel

    private var mCameraLibrary: CameraLibrary? = null
    private var mSensorRotate: Rotation = Rotation.CW_90
    private var mAsyncDetector: AsyncFrameDetector? = null
    private var mInitialMillis: Long = 0
    private var mTimestamp: Float = -1.0f
    private var mIsHispeedRespnce: Boolean = false
    private var mLastProcssTime: Float = -1.0f
    private var mLastRecieveTime: Float = -1.0f

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.GONE

        val actualInterviewFaceAdjustViewModel =
            ViewModelProvider(this).get(ActualInterviewFaceAdjustViewModel::class.java)

        actualInterviewModel =
            ViewModelProvider(requireActivity()).get(ActualInterviewViewModel::class.java)

        _binding = FragmentActualInterviewFaceAdjustBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val titleTextview: TextView = binding.headerTitle
        actualInterviewFaceAdjustViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        val backButton: ImageButton = binding.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        if (actualInterviewModel.skipMyself) {
            binding.textView.text = "1/2"
            binding.progressBar3.visibility = View.GONE
            binding.progressBar2.visibility = View.VISIBLE
        } else {
            binding.textView.text = "2/3"
            binding.progressBar3.visibility = View.VISIBLE
            binding.progressBar2.visibility = View.GONE
        }

        // 通信して取得する
        UserAPI.getDetail(
            null,
            sharedPreferences,
            parentFragmentManager,
            { result: UserModel, error: Exception? ->
                if (error != null) {
                    CommonDialogFragment.newInstance(
                        "情報の取得でエラーが発生しました。",
                        true,
                        "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }

                            override fun callbackFromDialogCancelButton() {
                            }
                        }
                    ).show(
                        parentFragmentManager,
                        "CommonDialogFragment"
                    )
                } else {
                    if (result.disp_face_guide_mi == null || result.disp_face_guide_mi == 1) {
                        // モーダルを表示
                        TrainingAnnounceDialogFragment.newInstance(
                            "枠が黄色になるまで枠内に顔を合わせましょう。\n" +
                                    "顔(眉毛～顎)が髪の毛、マスク、サングラス、帽子などで隠れないようにしてください。",
                            R.drawable.img_camera_focus,
                            object : TrainingAnnounceDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton(isChecked: Boolean) {
                                    var params: MutableMap<String, Any> =
                                        mutableMapOf<String, Any>()
                                    // 顔位置合わせ説明モーダルの表示非表示登録
                                    params["disp_face_guide_mi"] = if (isChecked) 0 else 1

                                    // 通信して取得する
                                    // 通信してユーザーを編集する
                                    UserAPI.edit(params, sharedPreferences, parentFragmentManager,
                                        { result: Map<String, Any>, error: Exception? ->
                                            if (error != null) {
                                                CommonDialogFragment.newInstance(
                                                    "情報の登録でエラーが発生しました。",
                                                    true,
                                                    "OK",
                                                    object : CommonDialogFragment.CallbackListener {
                                                        override fun callbackFromDialogCloseButton() {
                                                        }

                                                        override fun callbackFromDialogCancelButton() {
                                                        }
                                                    }
                                                ).show(
                                                    parentFragmentManager,
                                                    "CommonDialogFragment"
                                                )
                                            }
                                        })
                                }
                            }).show(
                            requireActivity().supportFragmentManager,
                            "TrainingAnnounceDialogFragment"
                        )
                    }
                }
            })

        binding.nextButton.setOnClickListener({
            actualInterviewFaceAdjustViewModel.onNextButtonClick()
        })
        actualInterviewFaceAdjustViewModel.navigateToNextFragment.observe(viewLifecycleOwner) { shouldNavigate ->
            if(!shouldNavigate) return@observe
            findNavController().navigate(R.id.action_navigation_actual_interview_face_adjust_to_navigation_actual_interview_start)
            actualInterviewFaceAdjustViewModel.resetNextButton()
        }

        // プレビューのアスペクト比を計算し反映
        binding.nextButton.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding.nextButton.viewTreeObserver.removeOnGlobalLayoutListener(this)

                val locationButton = IntArray(2)
                binding.nextButton.getLocationOnScreen(locationButton)
                val nextButtonX = locationButton[0] // X座標
                val nextButtonY = locationButton[1] // Y座標

                val locationBar = IntArray(2)
                binding.currentBarBaseView.getLocationOnScreen(locationBar)
                val barX = locationBar[0] // X座標
                val barY = locationBar[1] // Y座標

                val displayWidth = Resources.getSystem().displayMetrics.widthPixels
                val viewHeight =
                    nextButtonY - (barY + binding.currentBarBaseView.height) - (40 * Resources.getSystem().displayMetrics.density)

                val scaleX: Float = displayWidth.toFloat() / CameraLibrary.PREVIEW_HEIGHT.toFloat()
                val scaleY: Float = viewHeight.toFloat() / CameraLibrary.PREVIEW_WIDTH.toFloat()

                if (CameraLibrary.PREVIEW_WIDTH.toFloat() * scaleX < viewHeight) {
                    binding.textureView.updateLayoutParams {
                        width = (CameraLibrary.PREVIEW_HEIGHT.toFloat() * scaleX).toInt()
                        height = (CameraLibrary.PREVIEW_WIDTH.toFloat() * scaleX).toInt()
                    }
                } else {
                    binding.textureView.updateLayoutParams {
                        width = (CameraLibrary.PREVIEW_HEIGHT.toFloat() * scaleY).toInt()
                        height = (CameraLibrary.PREVIEW_WIDTH.toFloat() * scaleY).toInt()
                    }
                }
            }
        })

        // GestureDetector の初期化
        gestureDetector =
            GestureDetectorCompat(requireContext(), object : SimpleOnGestureListener() {
                private val SWIPE_X_THRESHOLD = 100
                private val SWIPE_Y_THRESHOLD = 100
                private val SWIPE_VELOCITY_THRESHOLD = 0

                override fun onFling(
                    e1: MotionEvent?,
                    e2: MotionEvent,
                    velocityX: Float,
                    velocityY: Float
                ): Boolean {
                    if (e1 != null && e2 != null) {
                        val deltaX = e2.x - e1.x
                        val deltaY = e2.y - e1.y
                        if (Math.abs(deltaX) > Math.abs(deltaY)) {
                            if (Math.abs(velocityX) > SWIPE_VELOCITY_THRESHOLD) {
                                if (deltaX > SWIPE_X_THRESHOLD) {
                                    Log.d("Gesture", "右にフリック")
                                    onSwipeRight()
                                } else {
                                    Log.d("Gesture", "左にフリック")
                                    //onSwipeLeft()
                                }
                            }
                        } else {
                            if (Math.abs(velocityY) > SWIPE_VELOCITY_THRESHOLD) {
                                if (deltaY > SWIPE_Y_THRESHOLD) {
                                    Log.d("Gesture", "下にフリック")
                                } else {
                                    Log.d("Gesture", "上にフリック")
                                }
                            }
                        }
                    }
                    return true
                }
            })

        // OnTouchListener を設定
        root.setOnTouchListener { _, event ->
            gestureDetector.onTouchEvent(event)
            true
        }

        if (ContextCompat.checkSelfPermission(
                requireContext(), Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            startCamera()
        } else {
            requestCameraPermissions()
        }

        return root
    }

    override fun onResume() {
        super.onResume();
        Log.d("ActionLog", "onResume IN")

        if (mCameraLibrary != null) {
            mCameraLibrary!!.SetCameraLibraryEventListener(this)
            mCameraLibrary!!.startBackgroundThread()
            mCameraLibrary!!.TryOpenCamera()
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(this);
        }

        Log.d("ActionLog", "onResume OUT")
    }

    override fun onPause() {
        Log.i("ActionLog", "onPause IN");

        if (mCameraLibrary != null) {
            mCameraLibrary!!.closeCamera();
            mCameraLibrary!!.stopBackgroundThread()
            mCameraLibrary!!.SetCameraLibraryEventListener(null)
            //mCameraLibrary = null
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(null);
            if (mAsyncDetector!!.isRunning()) {
                //asyncDetector.stop()
                //asyncDetector.reset()
            }
        }

        Log.i("ActionLog", "onPause OUT")
        super.onPause()
    }

    override fun onDestroyView() {
        super.onDestroyView();

        Log.i("ActionLog", "onDestroyView IN")

        if (mCameraLibrary != null) {
            mCameraLibrary!!.closeCamera();
            mCameraLibrary!!.stopBackgroundThread()
            mCameraLibrary!!.SetCameraLibraryEventListener(null)
            mCameraLibrary = null
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(null);
            if (mAsyncDetector!!.isRunning()) {
                mAsyncDetector!!.stop()
                mAsyncDetector!!.reset()
                mAsyncDetector = null
            }
        }

        _binding = null

        Log.i("ActionLog", "onDestroyView OUT")
    }

    override fun onDetach() {
        Log.i("ActionLog", "onDetach IN");

        Log.i("ActionLog", "onDetach OUT");

        super.onDetach();
    }

    private fun requestCameraPermissions() {
        Log.d("ActionLog", "requestPermissionsrequestCameraPermissions IN")

        val requestPermissionLauncherCamera =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (isGranted) {
                    Handler(Looper.getMainLooper()).postDelayed({
                        startCamera()
                    }, 500)
                } else {
                    Log.d("ActionLog", "カメラの権限が必要です")
                }
            }
        requestPermissionLauncherCamera.launch(Manifest.permission.CAMERA)

        Log.d("ActionLog", "requestCameraPermissions OUT")
    }

    private fun startCamera() {
        Log.d("ActionLog", "startCamera IN")

        mCameraLibrary =
            CameraLibrary(requireActivity(), requireContext(), binding.textureView, null)
        mCameraLibrary!!.SetCameraLibraryEventListener(this)
        mCameraLibrary!!.startBackgroundThread()
        mCameraLibrary!!.TryOpenCamera()

        AsyncFrameDetector.NewInstance(requireContext())
        mAsyncDetector = AsyncFrameDetector.getInstance()
        if (mAsyncDetector != null) {
            mInitialMillis = AsyncFrameDetector.getInitialMillis()
            mAsyncDetector!!.setOnDetectorEventListener(this)
        }

        Log.d("ActionLog", "startCamera OUT")
    }

    override fun OnOpenedCamera() {
        if (mCameraLibrary == null) return

        if (mCameraLibrary!!.mSensorOrientation == 90) {
            // mSensorRotate = Frame.ROTATE.BY_90_CW
            mSensorRotate = Rotation.CW_90
        } else if (mCameraLibrary!!.mSensorOrientation == 270) {
            // mSensorRotate = Frame.ROTATE.BY_90_CCW
            mSensorRotate = Rotation.CW_270
        }

        Log.d("ActionLog", "OnOpenedCamera " + mSensorRotate + "OUT")
    }

    override fun OnUpdatePreview(bytes: ByteArray?, width: Int, height: Int) {
        if (mAsyncDetector == null || !mAsyncDetector!!.isRunning() || mCameraLibrary == null) return

        Handler(Looper.getMainLooper()).post {
            OnUpdatePreviewSub(bytes, width, height)
        }
    }

    override fun OnUpdate2ndPreview(image: Image, width: Int, height: Int) {
    }

    private fun OnUpdatePreviewSub(bytes: ByteArray?, width: Int, height: Int) {
        //Log.d("ActionLog", "OnUpdatePreviewSub IN")

        if (mAsyncDetector == null || !mAsyncDetector!!.isRunning() || mCameraLibrary == null) return

        var newtimestamp: Float = 0.0f
        var limit: Float = 0.0f

        if (mIsHispeedRespnce) {
            newtimestamp = (System.currentTimeMillis() - mInitialMillis).toFloat() / 100.0f;
            limit = 0.45f
        } else {
            newtimestamp = (System.currentTimeMillis() - mInitialMillis).toFloat() / 1000.0f;
            limit = 0.045f
        }

        val dur: Float = newtimestamp - mTimestamp;
        if (mTimestamp < 0 || dur > limit) {
            if (mAsyncDetector!!.process(bytes, width, height, mSensorRotate, newtimestamp)) {
                mTimestamp = newtimestamp
            }
        }

        //Log.d("ActionLog", "OnUpdatePreviewSub OUT")
    }

    override fun onImageResults(faces: List<Face?>?, image: Frame?, timeStamp: Float) {
        if (mCameraLibrary == null) return
        Handler(Looper.getMainLooper()).post {
            onImageResultsSub(faces, image, timeStamp)
        }
    }

    private fun onImageResultsSub(faces: List<Face?>?, image: Frame?, timeStamp: Float) {
        //Log.d("ActionLog", "onImageResultsSub IN")

        if (mCameraLibrary == null) return

        val timeStamp: Float = timeStamp / 1000.0f
        mLastRecieveTime = (System.currentTimeMillis() - mInitialMillis).toFloat() / 1000.0f
        mLastProcssTime = mLastRecieveTime - timeStamp

        if (mCameraLibrary == null) return

        if (faces.isNullOrEmpty()) {
            binding.faceTrackingView.isRecognition = false
            binding.faceTrackingView.invalidate()
        } else {
            if (faces[0]?.facePoints?.count() == 4) {
                val facePoints = faces[0]?.facePoints ?: return

                if (facePoints.isEmpty()) return

                var left: Float = Float.MAX_VALUE;
                var right: Float = Float.MIN_VALUE;
                var top: Float = Float.MAX_VALUE;
                var bottom: Float = Float.MIN_VALUE;

                facePoints.values.forEach { point ->
                    if (point.x < left) left = point.x
                    if (point.x > right) right = point.x
                    if (point.y < top) top = point.y
                    if (point.y > bottom) bottom = point.y
                }

                // カメラ画像とプレビューの変換スケール
                val scaleX: Float =
                    binding.textureView.width.toFloat() / image?.getWidth()?.toFloat()!!
                val scaleY: Float =
                    binding.textureView.height.toFloat() / image?.getHeight()?.toFloat()!!

                // 顔の領域をプレビューの座標系に変換
                val faceRegion = Rect(
                    (left * scaleX).toInt(),
                    (top * scaleY).toInt(),
                    (right * scaleX).toInt(),
                    (bottom * scaleY).toInt()
                )

                // プレビュー中央にある枠の領域
                val centerRegion = Rect(
                    binding.faceTrackingView.x.toInt(),
                    binding.faceTrackingView.y.toInt(),
                    (binding.faceTrackingView.x + binding.faceTrackingView.width).toInt(),
                    (binding.faceTrackingView.y + binding.faceTrackingView.height).toInt()
                )

                // 重なっていない場合をチェック
                val noOverlap = faceRegion.right < centerRegion.left ||
                        faceRegion.left > centerRegion.right ||
                        faceRegion.bottom < centerRegion.top ||
                        faceRegion.top > centerRegion.bottom

                // 認識エリアの座標に認識エリアが収まっているか？
                if (!noOverlap) {
                    binding.faceTrackingView.isRecognition = true

                    if (!binding.nextButton.isEnabled) {
                        binding.nextButton.setBackgroundResource(R.drawable.round_rectangle_25dp_color_primary)
                        binding.nextButton.isEnabled = true
                    }
                } else {
                    binding.faceTrackingView.isRecognition = false
                }
                binding.faceTrackingView.invalidate()
            }
        }

        //Log.d("ActionLog", "onImageResults OUT")
    }

    private fun onSwipeRight() {
        requireActivity().onBackPressedDispatcher.onBackPressed()
    }
}