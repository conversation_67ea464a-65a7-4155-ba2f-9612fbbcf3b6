package jp.co.cac.ope.shokken.lab.ui.home

import android.content.Context.MODE_PRIVATE
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.ActualInterviewAPI
import jp.co.cac.ope.shokken.lab.connects.MockInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentInterviewTabBinding
import jp.co.cac.ope.shokken.lab.model.ActualInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewContentModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewGradeModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewTranscriptionModel
import jp.co.cac.ope.shokken.lab.ui.actual_interview.ActualInterviewRecordDetailFragment
import jp.co.cac.ope.shokken.lab.ui.interview.InterviewRecordDetailFragment
import jp.co.cac.ope.shokken.lab.ui.interview.InterviewRecordSelectFragment
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment

class InterviewTabFragment : Fragment() {
    private var _binding: FragmentInterviewTabBinding? = null
    private val binding get() = _binding!!

    private lateinit var adapter: InterviewScoreAdapter
    private var tabType: Int = 0 // 0 for Actual Interview, 1 for Mock Interview

    private var mockInterviewContentModel: MockInterviewContentModel? = null
    private var mockInterviewGradeModel: MockInterviewGradeModel? = null
    private var actualInterviewGradeInfoList: List<ActualInterviewGradeInfoModel> = emptyList()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            tabType = it.getInt(ARG_TAB_TYPE, 0)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentInterviewTabBinding.inflate(inflater, container, false)
        val root: View = binding.root

        binding.viewAllBtn.setOnClickListener {
            if (tabType == TAB_TYPE_ACTUAL) {
                findNavController().navigate(R.id.action_navigation_home_to_navigation_actual_interview_record_select)
            } else {
                if (mockInterviewContentModel != null && mockInterviewGradeModel != null) {
                    findNavController().navigate(
                        R.id.action_navigation_home_to_navigation_interview_record_select,
                        InterviewRecordSelectFragment.args(
                            mockInterviewContentModel!!,
                            mockInterviewGradeModel!!
                        )
                    )
                }
            }
        }

        setupRecyclerView()

        // Load data based on tab type
        if (tabType == TAB_TYPE_ACTUAL) {
            loadActualInterviewGrades()
        } else {
            loadMockInterviewContents()
        }

        return root
    }

    private fun setupRecyclerView() {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)
        adapter = InterviewScoreAdapter(arrayListOf()) { item ->
            when (item) {
                is ActualInterviewGradeInfoModel -> {
                    val list = actualInterviewGradeInfoList.map { it.actual_interview_grade_id }
                    Handler(Looper.getMainLooper()).post {
                        findNavController().navigate(
                            R.id.action_navigation_home_to_navigation_actual_interview_record_detail,
                            ActualInterviewRecordDetailFragment.args(
                                ArrayList(list),
                                item.actual_interview_grade_id
                            )
                        )
                    }
                }

                is MockInterviewGradeInfoModel -> {
                    MockInterviewAPI.GetMockInterviewGradeInfo(
                        item.mock_interview_grade_id,
                        sharedPreferences,
                        parentFragmentManager,
                        { result1: MockInterviewGradeInfoModel, result2: MockInterviewTranscriptionModel, error: Exception? ->
                            if (error != null) {
                                CommonDialogFragment.newInstance(
                                    "模擬面接結果詳細の取得でエラーが発生しました。",
                                    true,
                                    "OK",
                                    object : CommonDialogFragment.CallbackListener {
                                        override fun callbackFromDialogCloseButton() {}
                                        override fun callbackFromDialogCancelButton() {}
                                    }
                                ).show(
                                    parentFragmentManager,
                                    "CommonDialogFragment"
                                )
                            } else {
                                Handler(Looper.getMainLooper()).post {
                                    findNavController().navigate(
                                        R.id.action_navigation_home_to_navigation_interview_record_detail,
                                        InterviewRecordDetailFragment.args(
                                            mockInterviewContentModel!!,
                                            result1,
                                            result2
                                        )
                                    )
                                }
                            }
                        })

                }
            }
        }
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.recyclerView.adapter = adapter
    }

    private fun loadActualInterviewGrades() {
        binding.emptyText.visibility = View.GONE

        val sharedPreferences = requireActivity().getSharedPreferences(
            AppConstants.APP_PREF_NAME,
            MODE_PRIVATE
        )

        ActualInterviewAPI.GetActualInterviewGrade(
            sharedPreferences,
            parentFragmentManager,
            true
        ) { result, error ->
            requireActivity().runOnUiThread {
                if (error != null) {
                    showErrorDialog()
                    return@runOnUiThread
                }

                actualInterviewGradeInfoList = result.getGradeList()
                if (actualInterviewGradeInfoList.isEmpty()) {
                    binding.emptyText.visibility = View.VISIBLE
                    (parentFragment as HomeFragment).setInterviewTabEmptyState(0, true)
                } else {
                    adapter.updateData(
                        ArrayList<Any>(
                            actualInterviewGradeInfoList
                                .reversed()
                                .take(5)
                        )
                    )
                }
            }
        }
    }

    private fun loadMockInterviewContents() {
        binding.emptyText.visibility = View.GONE

        val sharedPreferences = requireActivity().getSharedPreferences(
            AppConstants.APP_PREF_NAME,
            MODE_PRIVATE
        )

        MockInterviewAPI.GetListMockInterviewContent(
            sharedPreferences,
            parentFragmentManager,
            true,
            { result, error ->
                requireActivity().runOnUiThread {
                    if (error != null) {
                        showErrorDialog()
                        return@runOnUiThread
                    }

                    if (result.isEmpty()) {
                        binding.emptyText.visibility = View.VISIBLE
                        (parentFragment as HomeFragment).setInterviewTabEmptyState(1, true)
                        return@runOnUiThread
                    }

                    mockInterviewContentModel = result.first()
                    loadMockInterviewGrades(mockInterviewContentModel!!, sharedPreferences)
                }
            }
        )
    }

    private fun loadMockInterviewGrades(
        content: MockInterviewContentModel,
        sharedPreferences: android.content.SharedPreferences
    ) {
        MockInterviewAPI.GetMockInterviewGrade(
            0,
            content.mock_interview_contents_id,
            content.practice_index_name,
            sharedPreferences,
            parentFragmentManager,
            true,
            { result, _, error ->
                requireActivity().runOnUiThread {

                    if (error != null) {
                        showErrorDialog()
                        return@runOnUiThread
                    }

                    mockInterviewGradeModel = result

                    val gradeList = mockInterviewGradeModel!!.mock_interview_grade_list.filter {
                        it.is_deleted != MockInterviewGradeInfoModel.IS_DELETED_DELETED
                    }

                    if (gradeList.isEmpty()) {
                        binding.emptyText.visibility = View.VISIBLE
                        (parentFragment as HomeFragment).setInterviewTabEmptyState(1, true)
                    } else {
                        adapter.updateData(
                            ArrayList<Any>(
                                gradeList
                                    .reversed()
                                    .take(5)
                            )
                        )
                    }
                }
            }
        )
    }

    private fun showErrorDialog() {
        CommonDialogFragment.newInstance(
            "通信に失敗しました。",
            true,
            "OK",
            object : CommonDialogFragment.CallbackListener {
                override fun callbackFromDialogCloseButton() {}
                override fun callbackFromDialogCancelButton() {}
            }
        ).show(parentFragmentManager, "CommonDialogFragment")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_TAB_TYPE = "tab_type"
        const val TAB_TYPE_ACTUAL = 0
        const val TAB_TYPE_MOCK = 1

        fun newInstance(tabType: Int) = InterviewTabFragment().apply {
            arguments = Bundle().apply {
                putInt(ARG_TAB_TYPE, tabType)
            }
        }
    }
}
