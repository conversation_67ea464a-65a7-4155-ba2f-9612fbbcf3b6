package jp.co.cac.ope.shokken.lab.model

class ActualInterviewContentModel {
    var actual_interview_contents_id: Int = -1
    var actual_interview_contents_name: String = ""
    var actual_interview_contents_code: String = ""
    var practice_index_id: Int = -1
    var practice_index_name: String = ""
    var estimated_time: String = ""
    var disp_mode: Int = 0
    var disp_billing_status: Int = 0
    var disp_by_profile: Int = 0
    var disp_version: Int = 0
    var script_hint_new: String? = null
    var script_hint_change: String? = null
    var script_ex_new: String = ""
    var script_ex_change: String = ""
    var interviewer_url_man_first: String = ""
    var interviewer_url_man_last: String = ""
    var interviewer_url_woman_first: String = ""
    var interviewer_url_woman_last: String = ""
    var order_training: Int = 0
    var order_assignment: Int = 0
    var created_at: String = ""
    var updated_at: String = ""
    var is_deleted: Int = 0
    var deleted_at: String = ""
    var user_id: Int = -1
    var interview_script: String = ""
    var use_script: Int = 0
    var best_score: Int = 0
    var is_implemented: Int = 0

    fun description(): String {
        return "actual_interview_contents_id=$actual_interview_contents_id, actual_interview_contents_name=$actual_interview_contents_name, " +
                "actual_interview_contents_code=$actual_interview_contents_code, actual_interview_contents_code=$actual_interview_contents_code, " +
                "practice_index_id=$practice_index_id, practice_index_name=$practice_index_name, estimated_time=$estimated_time, " +
                "disp_mode=$disp_mode, disp_billing_status=$disp_billing_status, disp_by_profile=$disp_by_profile, disp_version=$disp_version, " +
                "script_hint_new=$script_hint_new, script_hint_change=$script_hint_change, script_ex_new=$script_ex_new, " +
                "script_ex_change=$script_ex_change, interviewer_url_man_first=$interviewer_url_man_first, " +
                "interviewer_url_man_last=$interviewer_url_man_last, interviewer_url_woman_first=$interviewer_url_woman_first, " +
                "interviewer_url_woman_last=$interviewer_url_woman_last, order_training=$order_training, order_assignment=$order_assignment, " +
                "created_at=$created_at, updated_at=$updated_at, is_deleted=$is_deleted, deleted_at=$deleted_at, user_id=$user_id, " +
                "interview_script=$interview_script, use_script=$use_script, best_score=$best_score, is_implemented=$is_implemented"
    }

    companion object {
        const val DISP_MODE_BOTH = 0
        const val DISP_MODE_NEW = 1
        const val DISP_MODE_CHANGE = 2

        const val DISP_BILLING_STATUS_FREE = 0
        const val DISP_BILLING_STATUS_BILLING = 1

        const val DISP_BY_PROFILE_FREE = 0
        const val DISP_BY_PROFILE_DONE = 1

        const val DISP_VERSION_MVP = 1
        const val DISP_VERSION_PRODUCT = 2

        const val USE_SCRIPT_NOT_USE = 0
        const val USE_SCRIPT_USE = 1

        const val IS_IMPLEMENTED_DOING = 0
        const val IS_IMPLEMENTED_DONE = 1

        fun create(params: Map<String, Any>): ActualInterviewContentModel {
            var model = ActualInterviewContentModel()

            if (params.containsKey("actual_interview_contents_id")
                && params["actual_interview_contents_id"] != null
                && (params["actual_interview_contents_id"] as Double).toInt() >= 0
            ) {
                model.actual_interview_contents_id =
                    (params["actual_interview_contents_id"] as Double).toInt()
            }
            if (params.containsKey("actual_interview_contents_name")
                && params["actual_interview_contents_name"] != null
            ) {
                model.actual_interview_contents_name =
                    params["actual_interview_contents_name"] as String
            }
            if (params.containsKey("actual_interview_contents_code")
                && params["actual_interview_contents_code"] != null
            ) {
                model.actual_interview_contents_code =
                    params["actual_interview_contents_code"] as String
            }
            if (params.containsKey("practice_index_id")
                && (params["practice_index_id"] as Double).toInt() >= 0
            ) {
                model.practice_index_id = (params["practice_index_id"] as Double).toInt()
            }
            if (params.containsKey("practice_index_name")
                && params["practice_index_name"] != null
            ) {
                model.practice_index_name = params["practice_index_name"] as String
            }
            if (params.containsKey("estimated_time")
                && params["estimated_time"] != null
            ) {
                model.estimated_time = params["estimated_time"] as String
            }
            if (params.containsKey("disp_mode")
                && params["disp_mode"] != null
                && (params["disp_mode"] as Double).toInt() >= 0
            ) {
                model.disp_mode = (params["disp_mode"] as Double).toInt()
            }
            if (params.containsKey("disp_billing_status")
                && params["disp_billing_status"] != null
                && (params["disp_billing_status"] as Double).toInt() >= 0
            ) {
                model.disp_billing_status = (params["disp_billing_status"] as Double).toInt()
            }
            if (params.containsKey("disp_by_profile")
                && params["disp_by_profile"] != null
                && (params["disp_by_profile"] as Double).toInt() >= 0
            ) {
                model.disp_by_profile = (params["disp_by_profile"] as Double).toInt()
            }
            if (params.containsKey("disp_version")
                && params["disp_version"] != null
                && (params["disp_version"] as Double).toInt() >= 0
            ) {
                model.disp_version = (params["disp_version"] as Double).toInt()
            }
            if (params.containsKey("script_hint_new")
                && params["script_hint_new"] != null
            ) {
                model.script_hint_new = params["script_hint_new"] as String
            }
            if (params.containsKey("script_hint_change")
                && params["script_hint_change"] != null
            ) {
                model.script_hint_change = params["script_hint_change"] as String
            }
            if (params.containsKey("script_ex_new")
                && params["script_ex_new"] != null
            ) {
                model.script_ex_new = params["script_ex_new"] as String
            }
            if (params.containsKey("script_ex_change")
                && params["script_ex_change"] != null
            ) {
                model.script_ex_change = params["script_ex_change"] as String
            }
            if (params.containsKey("interviewer_url_man_first")
                && params["interviewer_url_man_first"] != null
            ) {
                model.interviewer_url_man_first = params["interviewer_url_man_first"] as String
            }
            if (params.containsKey("interviewer_url_man_last")
                && params["interviewer_url_man_last"] != null
            ) {
                model.interviewer_url_man_last = params["interviewer_url_man_last"] as String
            }
            if (params.containsKey("interviewer_url_woman_first")
                && params["interviewer_url_woman_first"] != null
            ) {
                model.interviewer_url_woman_first = params["interviewer_url_woman_first"] as String
            }
            if (params.containsKey("interviewer_url_woman_last")
                && params["interviewer_url_woman_last"] != null
            ) {
                model.interviewer_url_woman_last = params["interviewer_url_woman_last"] as String
            }
            if (params.containsKey("order_training")
                && params["order_training"] != null
                && (params["order_training"] as Double).toInt() >= 0
            ) {
                model.order_training = (params["order_training"] as Double).toInt()
            }
            if (params.containsKey("order_assignment")
                && params["order_assignment"] != null
                && (params["order_assignment"] as Double).toInt() >= 0
            ) {
                model.order_assignment = (params["order_assignment"] as Double).toInt()
            }
            if (params.containsKey("created_at")
                && params["created_at"] != null
            ) {
                model.created_at = params["created_at"] as String
            }
            if (params.containsKey("updated_at")
                && params["updated_at"] != null
            ) {
                model.updated_at = params["updated_at"] as String
            }
            if (params.containsKey("is_deleted")
                && params["is_deleted"] != null
                && (params["is_deleted"] as Double).toInt() >= 0
            ) {
                model.is_deleted = (params["is_deleted"] as Double).toInt()
            }
            if (params.containsKey("deleted_at")
                && params["deleted_at"] != null
            ) {
                model.deleted_at = params["deleted_at"] as String
            }
            if (params.containsKey("user_id")
                && params["user_id"] != null
                && (params["user_id"] as Double).toInt() >= 0
            ) {
                model.user_id = (params["user_id"] as Double).toInt()
            }
            if (params.containsKey("interview_script")
                && params["interview_script"] != null
            ) {
                model.interview_script = params["interview_script"] as String
            }
            if (params.containsKey("use_script")
                && params["use_script"] != null
                && (params["use_script"] as Double).toInt() >= 0
            ) {
                model.use_script = (params["use_script"] as Double).toInt()
            }
            if (params.containsKey("best_score")
                && params["best_score"] != null
                && (params["best_score"] as Double).toInt() >= 0
            ) {
                model.best_score = (params["best_score"] as Double).toInt()
            }
            if (params.containsKey("is_implemented")
                && params["is_implemented"] != null
                && (params["is_implemented"] as Double).toInt() >= 0
            ) {
                model.is_implemented = (params["is_implemented"] as Double).toInt()
            }

            return model
        }
    }
}
