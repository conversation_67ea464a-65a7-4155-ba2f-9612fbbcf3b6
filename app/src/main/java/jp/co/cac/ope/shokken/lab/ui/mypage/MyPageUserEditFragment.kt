package jp.co.cac.ope.shokken.lab.ui.mypage

import android.content.Context.MODE_PRIVATE
import android.graphics.Typeface
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.activity.HomeActivity
import jp.co.cac.ope.shokken.lab.connects.UserAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentMyPageUserEditBinding
import jp.co.cac.ope.shokken.lab.model.UserModel
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.CustomerIOUtil
import jp.co.cac.ope.shokken.lab.utils.DateUtil
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil
import java.util.Calendar
import java.util.Locale

class MyPageUserEditFragment : Fragment() {
    private var _binding: FragmentMyPageUserEditBinding? = null
    private val binding get() = _binding!!

    var genderInput = 0
    var graduationTypeInput = 1

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMyPageUserEditBinding.inflate(inflater, container, false)

        val sp = requireActivity().getSharedPreferences(
           AppConstants.APP_PREF_NAME,
            MODE_PRIVATE
        )

        // タイトル設定
        binding.backHeaderBar.headerTitle.text = "プロフィール編集"
        // ヘッダの戻るボタンクリック
        binding.backHeaderBar.backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        val birthdayStr = arguments?.getString("birthday")!!
        val calendar = Calendar.getInstance()
        val yearCurrent = calendar.get(Calendar.YEAR)
        calendar.add(Calendar.YEAR, -20)
        val year20YearsAgo = calendar.get(Calendar.YEAR)
        val graduationStr = arguments?.getString("graduation_date")!!

        val userName = arguments?.getString("user_name")
        var birthdayY = if(birthdayStr == "") year20YearsAgo else getYear(birthdayStr)
        var birthdayM = if(birthdayStr == "") 1 else getMonth(birthdayStr)
        var birthdayD = if(birthdayStr == "") 1 else getDay(birthdayStr)
        val gender = arguments?.getInt("gender")

        var graduationDateY = if(graduationStr == "") yearCurrent else getYear(graduationStr)
        var graduationDateM = if(graduationStr == "") 1 else getMonth(graduationStr)
        var graduationType = arguments?.getInt("graduation_type")
        var schoolName = arguments?.getString("school_name")

        //ユーザー名
        binding.username.inputTextEmailTextInput.setText(userName)
        //バリデーション
        binding.username.inputTextEmailTextInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if(s.toString().isNotEmpty()){
                    binding.usernameValidation.root.visibility = View.GONE

                    binding.username.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border)
                }else{
                    binding.usernameValidation.text.text = "ユーザー名を入力してください"
                    binding.usernameValidation.root.visibility = View.VISIBLE

                    binding.username.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border_warning)
                }
            }

            override fun afterTextChanged(s: Editable?) {}
        })

        //生年月日
        // 日をはじめに初期化
        val spinnerBirthdayDay = binding.birthdayDay
        var itemsBirthdayDay = DateUtil.makeDays(
            arrayListOf(
                birthdayY,
                birthdayM - 1
            )
        ).toList()
        val customAdapterBirthdayDay = ArrayAdapter<String>(
            requireContext(),
            R.layout.spinner_item,
            R.id.spinnerText,
            itemsBirthdayDay
        )
        spinnerBirthdayDay.adapter = customAdapterBirthdayDay
        val defaultIndexBirthdayDay = itemsBirthdayDay.indexOf(
            String.format(
                Locale.JAPAN,
                "%02d",
                birthdayD
            )
        )
        if (defaultIndexBirthdayDay != -1) {
            spinnerBirthdayDay.setSelection(defaultIndexBirthdayDay)
        }

        val spinnerBirthdayYear = binding.birthdayYear
        val itemsBirthdayYear = DateUtil.makeYearsString().toList()
        val customAdapterBirthdayYear = ArrayAdapter<String>(
            requireContext(),
            R.layout.spinner_item,
            R.id.spinnerText,
            itemsBirthdayYear
        )
        spinnerBirthdayYear.adapter = customAdapterBirthdayYear
        val defaultIndexBirthdayYear =
            itemsBirthdayYear.indexOf(birthdayY.toString())
        if (defaultIndexBirthdayYear != -1) {
            spinnerBirthdayYear.setSelection(defaultIndexBirthdayYear)
        }

        spinnerBirthdayYear.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>,
                    view: View?,
                    position: Int,
                    id: Long
                ) {
                    birthdayY = itemsBirthdayYear[position].toInt()

                    // 年が変わったら日を再計算
                    birthdayD = 1
                    itemsBirthdayDay = DateUtil.makeDays(
                        arrayListOf(
                            birthdayY,
                            birthdayM - 1
                        )
                    ).toList()
                    val customAdapterBirthdayDay = ArrayAdapter<String>(
                        requireContext(),
                        R.layout.spinner_item,
                        R.id.spinnerText,
                        itemsBirthdayDay
                    )
                    spinnerBirthdayDay.adapter = customAdapterBirthdayDay
                    val defaultIndexBirthdayDay = itemsBirthdayDay.indexOf(
                        String.format(
                            Locale.JAPAN,
                            "%02d",
                            birthdayD
                        )
                    )
                    if (defaultIndexBirthdayDay != -1) {
                        spinnerBirthdayDay.setSelection(defaultIndexBirthdayDay)
                    }
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }

        val spinnerBirthdayMonth = binding.birthdayMonth
        val itemsBirthdayMonth = DateUtil.makeMonths().toList()
        val customAdapterBirthdayMonth = ArrayAdapter<String>(
            requireContext(),
            R.layout.spinner_item,
            R.id.spinnerText,
            itemsBirthdayMonth
        )
        spinnerBirthdayMonth.adapter = customAdapterBirthdayMonth
        val defaultIndexBirthdayMonth = itemsBirthdayMonth.indexOf(
            String.format(
                Locale.JAPAN,
                "%02d",
                birthdayM
            )
        )
        if (defaultIndexBirthdayMonth != -1) {
            spinnerBirthdayMonth.setSelection(defaultIndexBirthdayMonth)
        }

        spinnerBirthdayMonth.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>,
                    view: View?,
                    position: Int,
                    id: Long
                ) {
                    birthdayM = itemsBirthdayMonth[position].toInt()

                    // 月が変わったら日を再計算
                    birthdayD = 1
                    itemsBirthdayDay = DateUtil.makeDays(
                        arrayListOf(
                            birthdayY,
                            birthdayM - 1
                        )
                    ).toList()
                    val customAdapterBirthdayDay = ArrayAdapter<String>(
                        requireContext(),
                        R.layout.spinner_item,
                        R.id.spinnerText,
                        itemsBirthdayDay
                    )
                    spinnerBirthdayDay.adapter = customAdapterBirthdayDay
                    val defaultIndexBirthdayDay = itemsBirthdayDay.indexOf(
                        String.format(
                            Locale.JAPAN,
                            "%02d",
                            birthdayD
                        )
                    )
                    if (defaultIndexBirthdayDay != -1) {
                        spinnerBirthdayDay.setSelection(defaultIndexBirthdayDay)
                    }
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }


        //性別
        //性別初期表示
        if(gender == 0){
            otherActive()
        }else if(gender == 1){
            maleActive()
        }else if(gender == 2){
            femaleActive()
        }
        //性別ボタンタップ時
        binding.otherButton.setOnClickListener{
            otherActive()
        }
        binding.maleButton.setOnClickListener{
            maleActive()
        }
        binding.femaleButton.setOnClickListener{
            femaleActive()
        }

        //卒業(予定)年月
        spinnerBirthdayDay.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>,
                    view: View?,
                    position: Int,
                    id: Long
                ) {
                    birthdayD = itemsBirthdayDay[position].toInt()
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }

        if (graduationType == 1) {
            willGraduationActive()
        } else if (graduationType == 2) {
            graduationActive()
        }

        val spinnerGraduationDateYear = binding.graduationDateYear
        val itemsGraduationDateYear = DateUtil.makeYears4GraduationString().toList()
        val customAdapterGraduationDateYear = ArrayAdapter<String>(
            requireContext(),
            R.layout.spinner_item,
            R.id.spinnerText,
            itemsGraduationDateYear
        )
        spinnerGraduationDateYear.adapter = customAdapterGraduationDateYear
        val defaultIndexGraduationDateYear =
            itemsGraduationDateYear.indexOf(graduationDateY.toString())
        if (defaultIndexGraduationDateYear != -1) {
            spinnerGraduationDateYear.setSelection(defaultIndexGraduationDateYear)
        }

        spinnerGraduationDateYear.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>,
                    view: View?,
                    position: Int,
                    id: Long
                ) {
                    graduationDateY =
                        itemsGraduationDateYear[position].toInt()
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }

        val spinnerGraduationDateMonth = binding.graduationDateMonth
        val itemsGraduationDateMonth = DateUtil.makeMonths().toList()
        val customAdapterGraduationDateMonth = ArrayAdapter<String>(
            requireContext(),
            R.layout.spinner_item,
            R.id.spinnerText,
            itemsGraduationDateMonth
        )
        spinnerGraduationDateMonth.adapter = customAdapterGraduationDateMonth
        val defaultIndexGraduationDateMonth =
            itemsGraduationDateMonth.indexOf(graduationDateM.toString())
        if (defaultIndexGraduationDateMonth != -1) {
            spinnerGraduationDateMonth.setSelection(defaultIndexGraduationDateMonth)
        }

        spinnerGraduationDateMonth.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>,
                    view: View?,
                    position: Int,
                    id: Long
                ) {
                    graduationDateM =
                        itemsGraduationDateMonth[position].toInt()
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }

        //ボタンクリック
        binding.willGraduationButton.setOnClickListener{
            willGraduationActive()
        }
        binding.graduationButton.setOnClickListener{
            graduationActive()
        }

        //学校名
        binding.schoolName.inputTextEmailTextInput.setText(schoolName)


        //更新ボタンタップ
        binding.updateBtn.setOnClickListener{
            //バリデーション
            var isError = false
            if(binding.username.inputTextEmailTextInput.text.isNotEmpty()){
                binding.usernameValidation.root.visibility = View.GONE

                binding.username.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border)
            }else{
                binding.usernameValidation.text.text = "ユーザー名を入力してください"
                binding.usernameValidation.root.visibility = View.VISIBLE

                binding.username.constraintLayout.setBackgroundResource(R.drawable.input_text_with_border_warning)
                isError = true
            }

            if(!isError){
                Log.d("debug", "onCreateView: ユーザー編集処理")



                val editReqBody: Map<String, Any> = mapOf(
                    "user_name" to binding.username.inputTextEmailTextInput.text.toString(),
                    "birthday" to formatDate(birthdayY, birthdayM, birthdayD),
                    "gender" to genderInput,
                    "graduation_date" to formatDate(graduationDateY, graduationDateM, null),
                    "graduation_type" to graduationTypeInput,
                    "school_name" to binding.schoolName.inputTextEmailTextInput.text.toString()
                )

                val userModel = UserModel()
                userModel.user_name = binding.username.inputTextEmailTextInput.text as? String
                userModel.birthday = formatDate(birthdayY, birthdayM, birthdayD) as? String
                userModel.gender = genderInput as? Int
                userModel.graduation_date = formatDate(graduationDateY, graduationDateM, null) as? String
                userModel.graduation_type = graduationTypeInput as? Int
                userModel.school_name = binding.schoolName.inputTextEmailTextInput.text as? String

                UserAPI.edit(editReqBody,
                    sp,
                    parentFragmentManager,
                    { result: Map<String, Any>, error: Exception? ->
                        if (error != null) {
                            CommonDialogFragment.newInstance("通信に失敗しました。", true, "OK",
                                object : CommonDialogFragment.CallbackListener {
                                    override fun callbackFromDialogCloseButton() {
                                    }
                                    override fun callbackFromDialogCancelButton() {
                                    }
                                }).show(parentFragmentManager, "CommonDialogFragment")
                        }

                        //SharedPreferenceUtil.setConfiguredProfile(sp.edit(), true)
                        CustomerIOUtil.completed_create_profile(sp, userModel)

                        CommonDialogFragment.newInstance("更新完了しました。", true, "閉じる",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                    Log.d("ActionLog", "${this.javaClass.simpleName} callbackFromCommonDialogCloseButton")
                                    findNavController().navigate(R.id.action_mypage_user_edit_to_mypage)
                                }
                                override fun callbackFromDialogCancelButton() {
                                }
                            }).show(parentFragmentManager, "CommonDialogFragment")
                    })


            }
        }

        return binding.root
    }

    override fun onResume() {
        super.onResume()
        // ボトムナビゲーションを非表示にする
        (activity as? HomeActivity)?.hideBottomNavigationView()
    }

    override fun onStop() {
        super.onStop()
        // ボトムナビゲーションを再表示する
        (activity as? HomeActivity)?.showBottomNavigationView()
    }


    //yyyy-MM-dd形式のデータから年月日の部分を取得する関数
    private fun getYear(dateString: String): Int {
        return dateString.substring(0, 4).toInt()
    }
    private fun getMonth(dateString: String): Int {
        val parts = dateString.split("-")
        return parts[1].toInt().toString().toInt() // 先頭の0を削除
    }
    private fun getDay(dateString: String): Int {
        val parts = dateString.split("-")
        return if (parts.size == 3) parts[2].toInt() else 1
    }

    private fun formatDate(year: Int, month: Int, day: Int?): String {
        // 月と日を2桁にフォーマット
        val formattedMonth = month.toString().padStart(2, '0')

        return if (day == null) {
            // dayがnullの場合はyyyy-MM形式で返す
            "$year-$formattedMonth"
        } else {
            // dayがnullでない場合はyyyy-MM-dd形式で返す
            val formattedDay = day.toString().padStart(2, '0')
            "$year-$formattedMonth-$formattedDay"
        }
    }

    //各性別ボタンをアクティブにする
    private fun maleActive(){
        binding.maleButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_color_primary)
        binding.maleButton.setTextColor(requireActivity().getColor(R.color.base_white))
        binding.maleButton.setTypeface(null, Typeface.BOLD)
        binding.femaleButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
        binding.femaleButton.setTextColor(requireActivity().getColor(R.color.base_text))
        binding.femaleButton.setTypeface(null, Typeface.NORMAL)
        binding.otherButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
        binding.otherButton.setTextColor(requireActivity().getColor(R.color.base_text))
        binding.otherButton.setTypeface(null, Typeface.NORMAL)

        genderInput = 1
    }
    private fun femaleActive(){
        binding.maleButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
        binding.maleButton.setTextColor(requireActivity().getColor(R.color.base_text))
        binding.maleButton.setTypeface(null, Typeface.NORMAL)
        binding.femaleButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_color_primary)
        binding.femaleButton.setTextColor(requireActivity().getColor(R.color.base_white))
        binding.femaleButton.setTypeface(null, Typeface.BOLD)
        binding.otherButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
        binding.otherButton.setTextColor(requireActivity().getColor(R.color.base_text))
        binding.otherButton.setTypeface(null, Typeface.NORMAL)

        genderInput = 2
    }
    private fun otherActive(){
        binding.maleButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
        binding.maleButton.setTextColor(requireActivity().getColor(R.color.base_text))
        binding.maleButton.setTypeface(null, Typeface.NORMAL)
        binding.femaleButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
        binding.femaleButton.setTextColor(requireActivity().getColor(R.color.base_text))
        binding.femaleButton.setTypeface(null, Typeface.NORMAL)
        binding.otherButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_color_primary)
        binding.otherButton.setTextColor(requireActivity().getColor(R.color.base_white))
        binding.otherButton.setTypeface(null, Typeface.BOLD)

        genderInput = 0
    }
    //既卒・卒業予定ボタンをアクティブにする
    private fun willGraduationActive(){
        binding.willGraduationButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_color_primary)
        binding.willGraduationButton.setTextColor(requireActivity().getColor(R.color.base_white))
        binding.willGraduationButton.setTypeface(null, Typeface.BOLD)
        binding.graduationButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
        binding.graduationButton.setTextColor(requireActivity().getColor(R.color.base_text))
        binding.graduationButton.setTypeface(null, Typeface.NORMAL)

        // バリデート解除
        binding.graduationTypeValidation.text.text = ""
        val graduationTypeValidation =
            activity?.findViewById<View>(R.id.graduation_type_validation)
        graduationTypeValidation?.visibility = View.GONE

        graduationTypeInput = 1
    }
    private fun graduationActive(){
        binding.willGraduationButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_white_with_gray_line)
        binding.willGraduationButton.setTextColor(requireActivity().getColor(R.color.base_text))
        binding.willGraduationButton.setTypeface(null, Typeface.NORMAL)
        binding.graduationButton.background =
            requireActivity().getDrawable(R.drawable.round_rectangle_5dp_color_primary)
        binding.graduationButton.setTextColor(requireActivity().getColor(R.color.base_white))
        binding.graduationButton.setTypeface(null, Typeface.BOLD)

        // バリデート解除
        binding.graduationTypeValidation.text.text = ""
        val graduationTypeValidation =
            activity?.findViewById<View>(R.id.graduation_type_validation)
        graduationTypeValidation?.visibility = View.GONE

        graduationTypeInput = 2
    }
}