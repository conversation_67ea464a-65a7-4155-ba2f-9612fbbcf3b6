package jp.co.cac.ope.shokken.lab.ui.customview

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R

class RadarChartView(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {
    var data: ArrayList<Double> = arrayListOf()

    private val mainColor = ContextCompat.getColor(context, R.color.color_primary)
    private val textColor = ContextCompat.getColor(context, R.color.base_text)
    private val radarMax = AppConstants.RADER_MAX

    init {
        background = null
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        Log.d("ActionLog", "Radar: " + data.toString())

        val radius = 2.5f * Resources.getSystem().displayMetrics.density

        val paint = Paint().apply {
            style = Paint.Style.STROKE
            color = textColor
            alpha = (0.3 * 255).toInt()
            strokeWidth = 1.5f * Resources.getSystem().displayMetrics.density
        }

        val width = width.toFloat()
        val height = height.toFloat()
        val centerX = width / 2.0f
        val centerY = height / 2.0f

        val path = Path()

        // **外枠**
        path.moveTo(centerX, radius)
        path.lineTo(width - radius, centerY)
        path.lineTo(centerX, height - radius)
        path.lineTo(radius, centerY)
        path.lineTo(centerX, radius)
        canvas.drawPath(path, paint)

        // **十字線**
        path.reset()
        paint.strokeWidth = 1.0f * Resources.getSystem().displayMetrics.density
        path.moveTo(centerX, radius)
        path.lineTo(centerX, height - radius)
        canvas.drawPath(path, paint)

        path.reset()
        path.moveTo(radius, centerY)
        path.lineTo(width - radius, centerY)
        canvas.drawPath(path, paint)

        // **グリッド線（破線）**
        paint.pathEffect = DashPathEffect(
            floatArrayOf(
                2.0f * Resources.getSystem().displayMetrics.density,
                2.0f * Resources.getSystem().displayMetrics.density
            ), 0.0f
        )
        for (i in 1 until 5) {
            val divWidth = width / 2.0f / 5.0f * i
            val divHeight = height / 2.0f / 5.0f * i

            path.reset()
            path.moveTo(centerX, centerY - divHeight)
            path.lineTo(centerX + divWidth, centerY)
            path.lineTo(centerX, centerY + divHeight)
            path.lineTo(centerX - divWidth, centerY)
            path.lineTo(centerX, centerY - divHeight)

            canvas.drawPath(path, paint)
        }
        paint.pathEffect = null  // 破線のリセット

        if (data.size == 4) {
            // **チャート本体**
            val chartPaint = Paint().apply {
                style = Paint.Style.FILL
                color = mainColor
                alpha = (0.1 * 255).toInt()
            }

            path.reset()
            path.moveTo(
                centerX,
                centerY - (height / 2.0f) * ((data[0].coerceAtLeast(0.0) / radarMax).toFloat()) + radius
            )
            path.lineTo(
                centerX + (width / 2.0f) * ((data[1].coerceAtLeast(0.0) / radarMax).toFloat()) - radius,
                centerY
            )
            path.lineTo(
                centerX,
                centerY + (height / 2.0f) * ((data[2].coerceAtLeast(0.0) / radarMax).toFloat()) - radius
            )
            path.lineTo(
                centerX - (width / 2.0f) * ((data[3].coerceAtLeast(0.0) / radarMax).toFloat()) + radius,
                centerY
            )
            path.close()
            canvas.drawPath(path, chartPaint)

            // **枠線**
            chartPaint.style = Paint.Style.STROKE
            chartPaint.color = mainColor
            chartPaint.alpha = 255
            canvas.drawPath(path, chartPaint)

            // **頂点ポイント**
            val pointPaint = Paint().apply {
                style = Paint.Style.FILL
                color = mainColor
            }

            val points = listOf(
                PointF(
                    centerX,
                    centerY - (height / 2.0f) * ((data[0].coerceAtLeast(0.0) / radarMax).toFloat()) + radius
                ),
                PointF(
                    centerX + (width / 2.0f) * ((data[1].coerceAtLeast(0.0) / radarMax).toFloat()) - radius,
                    centerY
                ),
                PointF(
                    centerX,
                    centerY + (height / 2.0f) * ((data[2].coerceAtLeast(0.0) / radarMax).toFloat()) - radius
                ),
                PointF(
                    centerX - (width / 2.0f) * ((data[3].coerceAtLeast(0.0) / radarMax).toFloat()) + radius,
                    centerY
                )
            )

            for (point in points) {
                canvas.drawCircle(point.x, point.y, radius, pointPaint)
            }
        }
    }
}