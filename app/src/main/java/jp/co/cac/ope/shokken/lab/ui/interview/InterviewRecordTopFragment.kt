package jp.co.cac.ope.shokken.lab.ui.interview

import android.content.Context.MODE_PRIVATE
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.card.MaterialCardView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.MockInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentInterviewRecordTopBinding
import jp.co.cac.ope.shokken.lab.model.MockInterviewContentModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewGradeModel
import jp.co.cac.ope.shokken.lab.ui.modal.ActivityIndicatorDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil

class InterviewRecordTopFragment : Fragment() {
    private var _binding: FragmentInterviewRecordTopBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    var mockInterviewContent: ArrayList<MockInterviewContentModel> =
        arrayListOf<MockInterviewContentModel>()
    var mockInterviewGrade: ArrayList<MockInterviewGradeModel> =
        arrayListOf<MockInterviewGradeModel>()

    private var indicator = ActivityIndicatorDialogFragment.newInstance()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.VISIBLE

        val interviewRecordTopViewModel =
            ViewModelProvider(this).get(InterviewRecordTopViewModel::class.java)

        var interviewRecordViewModel =
            ViewModelProvider(requireActivity()).get(InterviewRecordViewModel::class.java)

        _binding = FragmentInterviewRecordTopBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val titleTextview: TextView = binding.backHeaderBar.headerTitle
        interviewRecordTopViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        val backButton: ImageButton = binding.backHeaderBar.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        indicator.show(parentFragmentManager, "ActivityIndicatorDialogFragment")
        // 通信して模擬面接コンテンツ一覧を取得する
        MockInterviewAPI.GetListMockInterviewContent(
            sharedPreferences,
            parentFragmentManager,
            false,
            { result: ArrayList<MockInterviewContentModel>, error: Exception? ->
                if (error != null) {
                    indicator.dismissWithAnimation()
                    CommonDialogFragment.newInstance(
                        "模擬面接コンテンツ一覧の取得でエラーが発生しました。",
                        true,
                        "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }

                            override fun callbackFromDialogCancelButton() {
                            }
                        }
                    ).show(
                        parentFragmentManager,
                        "CommonDialogFragment"
                    )
                } else {
                    Log.d("ActionLog", "模擬面接コンテンツ一覧件数:${result.count()}")

                    mockInterviewContent = arrayListOf<MockInterviewContentModel>()

                    result.forEach { item ->
                        // ユーザ設定のモードと同じかすべて表示のコンテンツのみ表示
                        if (item.disp_mode == 0 || item.disp_mode == SharedPreferenceUtil.getMode(
                                sharedPreferences
                            )
                        ) {
                            mockInterviewContent.add(item)
                        }
                    }

                    // ユーザ設定のモードによって並び順を変える
                    if (SharedPreferenceUtil.getMode(sharedPreferences) == 1) {
                        mockInterviewContent.sortBy { it.order_training }
                    } else {
                        mockInterviewContent.sortBy { it.order_assignment }
                    }

                    mockInterviewContent.forEach { item ->
                        val grade = MockInterviewGradeModel()

                        grade.mock_interview_contents_id = -1
                        grade.mock_interview_contents_name = ""
                        grade.practice_index_name = ""
                        grade.total_practice_count = -1
                        grade.best_score = -1
                        grade.record_num = -1
                        grade.mock_interview_grade_list = arrayListOf<MockInterviewGradeInfoModel>()

                        mockInterviewGrade.add(grade)
                    }
                    var processCount = mockInterviewContent.count()

                    for (i in 0 until mockInterviewContent.count()) {
                        // 通信して模擬面接コンテンツ一覧を取得する
                        MockInterviewAPI.GetMockInterviewGrade(
                            i,
                            mockInterviewContent[i].mock_interview_contents_id,
                            mockInterviewContent[i].practice_index_name,
                            sharedPreferences,
                            parentFragmentManager,
                            false,
                            { result: MockInterviewGradeModel, index: Int, error: Exception? ->
                                processCount -= 1

                                if (error != null) {
                                    indicator.dismissWithAnimation()
                                    CommonDialogFragment.newInstance(
                                        "模擬面接コンテンツ一覧の取得でエラーが発生しました。",
                                        true,
                                        "OK",
                                        object : CommonDialogFragment.CallbackListener {
                                            override fun callbackFromDialogCloseButton() {
                                            }

                                            override fun callbackFromDialogCancelButton() {
                                            }
                                        }
                                    ).show(
                                        parentFragmentManager,
                                        "CommonDialogFragment"
                                    )
                                } else {
                                    Log.d(
                                        "Actionlog",
                                        "functionName: MockInterviewAPI.GetMockInterviewGrade, message: 模擬面接結果: ${result.description()}"
                                    )

                                    mockInterviewGrade[index] = result

                                    if (processCount == 0) {
                                        indicator.dismissWithAnimation()

                                        // 取得エラーなどで詳細が初期値のままのデータがもしあれば消し込む
                                        mockInterviewGrade.removeAll { it.mock_interview_contents_id == -1 }

                                        interviewRecordTopViewModel.connectionComplete.postValue(
                                            true
                                        )
                                    }
                                }
                            })
                    }
                }
            })

        // データ取得後画面に反映
        // 戻ってきたときはconnectionCompleteを変えないままだと即座に反映
        // 再取得の場合はconnectionCompleteを変えておく
        interviewRecordTopViewModel.connectionComplete.observe(viewLifecycleOwner) { shouldNavigate ->
            if (shouldNavigate) {
                val contentsContainer: LinearLayout = binding.contentsContainer
                contentsContainer.removeAllViews()

                // リストを動的に追加
                for ((index, interviewItem) in mockInterviewContent.withIndex()) {
                    val menuButtonView: View = layoutInflater.inflate(
                        R.layout.interview_record_menu_button,
                        contentsContainer,
                        false
                    )

                    // タイトル部分を設定
                    val titleTextView: TextView =
                        menuButtonView.findViewById(R.id.title)
                    titleTextView.text = interviewItem.mock_interview_contents_name

                    val kindLabelTextView: TextView =
                        menuButtonView.findViewById(R.id.kind_label)
                    kindLabelTextView.text = interviewItem.practice_index_name

                    val crown: ImageView = menuButtonView.findViewById(R.id.crown)
                    val best: TextView = menuButtonView.findViewById(R.id.best)
                    val score: TextView = menuButtonView.findViewById(R.id.score)

                    // 記録があるもののみボタンとして押せるように
                    if (mockInterviewGrade[index].total_practice_count > 0) {
                        crown.visibility = View.VISIBLE
                        best.visibility = View.VISIBLE
                        score.text = mockInterviewGrade[index].best_score.toString()
                        score.visibility = View.VISIBLE

                        val card: MaterialCardView = menuButtonView.findViewById(R.id.card)

                        // タップで次へ
                        card.setOnClickListener {
                            interviewRecordViewModel.selectInterview = interviewItem
                            interviewRecordViewModel.mockInterviewGrade = mockInterviewGrade[index]
                            findNavController().navigate(R.id.action_navigation_interview_record_top_to_navigation_interview_record_select)
                        }
                    } else {
                        titleTextView.setTextColor(
                            ContextCompat.getColor(
                                requireContext(),
                                R.color.gray_grayout
                            )
                        )
                        val kindLabelTitleTextView: TextView =
                            menuButtonView.findViewById(R.id.kind_label_title)
                        kindLabelTitleTextView.setTextColor(
                            ContextCompat.getColor(
                                requireContext(),
                                R.color.gray_grayout
                            )
                        )
                        val kindLabelTextView: TextView =
                            menuButtonView.findViewById(R.id.kind_label)
                        kindLabelTextView.setTextColor(
                            ContextCompat.getColor(
                                requireContext(),
                                R.color.gray_grayout
                            )
                        )
                        crown.visibility = View.GONE
                        best.visibility = View.GONE
                        score.visibility = View.GONE
                    }

                    // コンテナに追加
                    contentsContainer.addView(menuButtonView)
                }

                interviewRecordTopViewModel.connectionComplete.postValue(false)
            }
        }

        return root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}