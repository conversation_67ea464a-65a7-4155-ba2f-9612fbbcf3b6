<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.OPELab" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/color_primary</item>
        <item name="colorPrimaryVariant">@color/base_color_primary_koyui</item>
        <item name="colorOnPrimary">@color/base_white</item>
        <item name="android:textColorPrimary">@color/base_text</item>
        <item name="android:windowBackground">@color/base_white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/color_primary</item>
        <item name="colorSecondaryVariant">@color/base_30</item>
        <item name="colorOnSecondary">@color/base_10</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/base_white</item>
        <item name="android:windowLightStatusBar">true</item>
        <!-- Navigation bar color. -->
        <item name="android:navigationBarColor">@color/base_white</item>
        <item name="android:forceDarkAllowed">false</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.OPELabSplash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/base_white</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/splash_icon</item>
        <item name="windowSplashScreenIconBackgroundColor">@color/base_white</item>
        <item name="postSplashScreenTheme">@style/Theme.OPELab</item>

        <item name="android:windowDisablePreview">true</item>
        <item name="android:statusBarColor">@color/white_transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@color/base_white</item>
    </style>
</resources>