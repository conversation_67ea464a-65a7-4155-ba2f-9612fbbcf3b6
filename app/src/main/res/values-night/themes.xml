<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <!--<style name="Theme.OPELab" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        &lt;!&ndash; Primary brand color. &ndash;&gt;
        <item name="colorPrimary">@color/color_primary</item>
        <item name="colorPrimaryVariant">@color/base_color_primary_koyui</item>
        <item name="colorOnPrimary">@color/base_white</item>
        <item name="android:textColorPrimary">@color/base_text</item>
        &lt;!&ndash; Secondary brand color. &ndash;&gt;
        <item name="colorSecondary">@color/color_primary</item>
        <item name="colorSecondaryVariant">@color/base_30</item>
        <item name="colorOnSecondary">@color/base_10</item>
        &lt;!&ndash; Status bar color. &ndash;&gt;
        <item name="android:statusBarColor">@color/white_transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        &lt;!&ndash; Navigation bar color. &ndash;&gt;
        <item name="android:navigationBarColor">@color/base_white</item>
        &lt;!&ndash; Customize your theme here. &ndash;&gt;
    </style>
    <style name="Theme.OPELabSplash" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/splash_background</item>
        <item name="android:statusBarColor">@color/white_transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@color/base_white</item>
    </style>-->
</resources>