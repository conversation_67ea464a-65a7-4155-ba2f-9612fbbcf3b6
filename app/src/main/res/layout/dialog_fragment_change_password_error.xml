<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/textView11"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="56dp"
            android:layout_marginEnd="20dp"
            android:text="現在のパスワードに誤りがあります。正しいパスワードを入力してください。"
            android:textAlignment="center"
            android:textColor="#382F2D"
            android:textSize="16sp" />

        <Button
            android:id="@+id/okBtn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="48dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="OK"
            android:textAllCaps="false"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>