<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_marginBottom="0dp"
        android:background="@drawable/round_rectangle_5dp_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/closeButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:background="@color/base_white"
            android:includeFontPadding="false"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/bottom_announce_modal_button"
            android:textColor="@color/color_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/messageTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="48dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:text="@string/bottom_announce_modal_message"
            android:textColor="@color/base_text"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/faceImage"/>

        <ImageView
            android:id="@+id/faceImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:visibility="gone"
            android:src="@drawable/img_training_keep1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/messageTextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@id/spacer"/>

        <View
            android:id="@+id/spacer"
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:layout_marginTop="20dp"
            android:layout_marginStart="0dp"
            android:layout_marginEnd="0dp"
            android:layout_marginBottom="0dp"
            android:background="@color/base_white"
            app:layout_constraintTop_toBottomOf="@id/faceImage"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>