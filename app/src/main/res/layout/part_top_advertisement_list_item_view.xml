<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="0dp"
    android:layout_marginRight="0dp"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/contentView"
        android:layout_width="match_parent"
        android:layout_height="75dp"
        app:cardCornerRadius="5dp"
        app:cardElevation="2dp"
        app:strokeColor="#382F2D26"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:backgroundTint="@color/base_white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/leftView"
                android:layout_width="100dp"
                android:layout_height="75dp"
                app:cardCornerRadius="5dp"
                app:strokeColor="@color/color_primary"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:layout_marginLeft="0dp"
                android:backgroundTint="@color/base_white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">
                <ImageView
                    android:id="@+id/imageView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="0dp"
                    android:padding="0dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/img_column_noimage"
                    tools:ignore="MissingConstraints" />
            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:id="@+id/centerView"
                android:layout_marginLeft="108dp"
                android:layout_marginRight="34dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                app:layout_constraintStart_toEndOf="@+id/leftView"
                app:layout_constraintEnd_toStartOf="@+id/rightView"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    tools:ignore="Suspicious0dp">

                    <TextView
                        android:id="@+id/adTitleTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxLines="2"
                        android:ellipsize="end"
                        android:text="サンプルサンプルサンプルサンプルサンプルサンプルサンプルサンプルサンプルサンプル"
                        android:textColor="@color/base_text"
                        android:textStyle="bold"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        tools:ignore="Suspicious0dp">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="広告"
                            android:textColor="@color/gray_placeholder"
                            android:textStyle="normal"
                            android:textSize="12sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="2dp"
                            android:text="･"
                            android:textColor="@color/gray_placeholder"
                            android:textStyle="normal"
                            android:textSize="12sp" />
                        <TextView
                            android:id="@+id/adTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="2dp"
                            android:text="株式会社カチメン株式会社カチメン株式会社カ…"
                            android:textColor="@color/gray_placeholder"
                            android:textStyle="normal"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/rightView"
                android:layout_width="30dp"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/menuView"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/icon_ad_menu" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/emptyReasonView"
        android:layout_width="match_parent"
        android:layout_height="75dp"
        app:cardCornerRadius="5dp"
        app:cardElevation="2dp"
        app:strokeColor="#382F2D26"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:backgroundTint="@color/base_white">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">
            <TextView
                android:id="@+id/reasonTextView"
                android:layout_marginHorizontal="20dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:maxLines="2"
                android:ellipsize="end"
                android:text="この広告を一定期間非表示にしました。\n次の広告表示までお待ちください。"
                android:textColor="@color/base_text"
                android:textStyle="normal"
                android:textSize="12sp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>