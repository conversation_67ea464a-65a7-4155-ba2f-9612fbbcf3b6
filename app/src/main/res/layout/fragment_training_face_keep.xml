<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.training.TrainingFaceFreeViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.training.TrainingFaceKeepFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/sliderButton"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <LinearLayout
                android:id="@+id/sliderLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <include
                    android:id="@+id/smile"
                    layout="@layout/training_slider_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <include
                    android:id="@+id/serious"
                    layout="@layout/training_slider_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginEnd="30dp" />

                <include
                    android:id="@+id/interested"
                    layout="@layout/training_slider_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextureView
            android:id="@+id/textureView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sliderButton" />

        <jp.co.cac.ope.shokken.lab.ui.customview.FaceKeepView
            android:id="@+id/faceKeepView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/textureView" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="12dp"
            android:src="@drawable/img_affectiva_gray"
            app:layout_constraintBottom_toBottomOf="@id/textureView"
            app:layout_constraintStart_toStartOf="parent" />

        <jp.co.cac.ope.shokken.lab.ui.customview.FaceTrackingView
            android:id="@+id/faceTrackingView"
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:background="@android:color/transparent"
            app:layout_constraintBottom_toBottomOf="@id/textureView"
            app:layout_constraintEnd_toEndOf="@id/textureView"
            app:layout_constraintStart_toStartOf="@id/textureView"
            app:layout_constraintTop_toTopOf="@id/textureView" />

        <ImageView
            android:id="@+id/hintButton"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:src="@drawable/ic_btn_hint"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <ImageView
            android:id="@+id/verygoodView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>