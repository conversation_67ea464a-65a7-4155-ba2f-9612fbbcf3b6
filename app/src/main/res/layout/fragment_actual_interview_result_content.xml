<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.actual_interview.ActualInterviewResultViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/scrollContentLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white"
                android:paddingBottom="96dp">

                <LinearLayout
                    android:id="@+id/titleLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="44dp"
                    android:layout_marginTop="20dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/titleBackwardImg"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:background="?selectableItemBackgroundBorderless"
                        android:padding="8dp"
                        app:srcCompat="@drawable/ic_chevron_backward" />

                    <TextView
                        android:id="@+id/titleTxt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="2dp"
                        android:gravity="center"
                        android:text="自己紹介"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/titleForwardImg"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:background="?selectableItemBackgroundBorderless"
                        android:padding="8dp"
                        app:srcCompat="@drawable/ic_chevron_forward" />
                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/movieLayout"
                    android:layout_width="268dp"
                    android:layout_height="476dp"
                    android:layout_marginTop="20dp"
                    android:background="@color/base_white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/titleLayout">

                    <ImageButton
                        android:id="@+id/fullsize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_fullscreen"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:id="@+id/controlLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="11dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <ImageButton
                            android:id="@+id/backward"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_10backward_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/play"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_play_b" />

                        <ImageButton
                            android:id="@+id/pause"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_pause_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/replay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_replay_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/forward"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_10forward_b"
                            android:visibility="gone" />
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:text="表情分析"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/movieLayout" />

                <FrameLayout
                    android:id="@+id/analyzeFaceLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView2">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="24dp"
                        android:layout_marginVertical="12dp"
                        android:outlineSpotShadowColor="#1A000000"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="10dp">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <jp.co.cac.ope.shokken.lab.ui.customview.RadarChartView
                                android:id="@+id/radarChart"
                                android:layout_width="150dp"
                                android:layout_height="150dp"
                                android:layout_marginStart="81dp"
                                android:layout_marginTop="63dp"
                                android:layout_marginEnd="81dp"
                                android:layout_marginBottom="59dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/radarLabel1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="笑顔"
                                android:textColor="@color/base_text"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toTopOf="@id/radarChart"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent" />

                            <TextView
                                android:id="@+id/radarLabel1Score"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="2dp"
                                android:text="--"
                                android:textColor="@color/color_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toTopOf="@id/radarLabel1"
                                app:layout_constraintEnd_toEndOf="@id/radarLabel1"
                                app:layout_constraintStart_toStartOf="@id/radarLabel1" />

                            <ImageButton
                                android:id="@+id/radarLabel1Button"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginStart="4dp"
                                android:background="@color/white_transparent"
                                android:src="@drawable/icon_tooltip"
                                app:layout_constraintBottom_toBottomOf="@id/radarLabel1"
                                app:layout_constraintStart_toEndOf="@id/radarLabel1"
                                app:layout_constraintTop_toTopOf="@id/radarLabel1Score" />

                            <TextView
                                android:id="@+id/radarLabel2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:text="落ち着き"
                                android:textColor="@color/base_text"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toEndOf="@id/radarChart"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/radarLabel2Score"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="2dp"
                                android:text="--"
                                android:textColor="@color/color_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toTopOf="@id/radarLabel2"
                                app:layout_constraintEnd_toEndOf="@id/radarLabel2"
                                app:layout_constraintStart_toStartOf="@id/radarLabel2" />

                            <ImageButton
                                android:id="@+id/radarLabel2Button"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="4dp"
                                android:background="@color/white_transparent"
                                android:src="@drawable/icon_tooltip"
                                app:layout_constraintEnd_toEndOf="@id/radarLabel2"
                                app:layout_constraintStart_toStartOf="@id/radarLabel2"
                                app:layout_constraintTop_toBottomOf="@id/radarLabel2" />

                            <TextView
                                android:id="@+id/radarLabel3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="22dp"
                                android:text="表現力"
                                android:textColor="@color/base_text"
                                android:textSize="12sp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/radarChart" />

                            <TextView
                                android:id="@+id/radarLabel3Score"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="2dp"
                                android:text="--"
                                android:textColor="@color/color_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toTopOf="@id/radarLabel3"
                                app:layout_constraintEnd_toEndOf="@id/radarLabel3"
                                app:layout_constraintStart_toStartOf="@id/radarLabel3" />

                            <ImageButton
                                android:id="@+id/radarLabel3Button"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginStart="4dp"
                                android:background="@color/white_transparent"
                                android:src="@drawable/icon_tooltip"
                                app:layout_constraintBottom_toBottomOf="@id/radarLabel3"
                                app:layout_constraintStart_toEndOf="@id/radarLabel3"
                                app:layout_constraintTop_toTopOf="@id/radarLabel3Score" />

                            <TextView
                                android:id="@+id/radarLabel4"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="4dp"
                                android:text="興味関心"
                                android:textColor="@color/base_text"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/radarChart"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/radarLabel4Score"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="2dp"
                                android:text="--"
                                android:textColor="@color/color_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toTopOf="@id/radarLabel4"
                                app:layout_constraintEnd_toEndOf="@id/radarLabel4"
                                app:layout_constraintStart_toStartOf="@id/radarLabel4" />

                            <ImageButton
                                android:id="@+id/radarLabel4Button"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="4dp"
                                android:background="@color/white_transparent"
                                android:src="@drawable/icon_tooltip"
                                app:layout_constraintEnd_toEndOf="@id/radarLabel4"
                                app:layout_constraintStart_toStartOf="@id/radarLabel4"
                                app:layout_constraintTop_toBottomOf="@id/radarLabel4" />

                            <include
                                android:id="@+id/analyzeFaceMask"
                                layout="@layout/show_detail"
                                android:layout_width="0dp"
                                android:layout_height="0dp"
                                android:layout_margin="5dp"
                                android:visibility="gone"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </com.google.android.material.card.MaterialCardView>

                </FrameLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/analyzeScoreLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="3dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/movieLayout">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/bg_score"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/total_score"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="000"
                        android:textColor="@color/color_primary"
                        android:textSize="36sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/expressionGraphTitleText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="Expression Graph"
                    android:textColor="@color/base_text"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/analyzeFaceLayout" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/expressionGraphLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:outlineSpotShadowColor="#1A000000"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/expressionGraphTitleText">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/plusElementLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="10dp"
                            android:text="Plus Element"
                            android:textColor="@color/base_text"
                            android:textSize="14sp"
                            app:layout_constraintBottom_toTopOf="@+id/expressionGraphDivider"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/negativeElementLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="10dp"
                            android:text="Negative Element"
                            android:textColor="@color/base_text"
                            android:textSize="14sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/expressionGraphDivider" />

                        <View
                            android:id="@+id/expressionGraphTextDivider"
                            android:layout_width="1.5dp"
                            android:layout_height="0dp"
                            android:layout_marginStart="10dp"
                            android:background="@color/gray_bg01"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/negativeElementLabel"
                            app:layout_constraintTop_toTopOf="parent" />

                        <View
                            android:id="@+id/expressionGraphDivider"
                            android:layout_width="match_parent"
                            android:layout_height="1.5dp"
                            android:background="@color/gray_grayout"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <jp.co.cac.ope.shokken.lab.ui.customview.ExpressionGraphView
                            android:id="@+id/expressionGraph"
                            android:layout_width="0dp"
                            android:layout_height="140dp"
                            app:gridColor="@color/gray_grayout"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/expressionGraphTextDivider"
                            app:layout_constraintTop_toTopOf="parent"
                            app:negativeColor="@color/accent_focus"
                            app:positiveColor="@color/base_30" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/resultFaceLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:outlineSpotShadowColor="#1A000000"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/expressionGraphLayout">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textView3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:text="表情評価"
                            android:textColor="@color/base_text"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/resultFaceText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="20dp"
                            android:text="穏やかな笑顔が出ています。笑顔でいると自分も相手も、明るい気持ちになり話しやすくなるので、もっと笑顔で話せるようにトレーニングしましょう。以下のポイントを改善することで、スコアアップ、好印象を与えることができます。"
                            android:textColor="@color/base_text"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/textView3" />

                        <include
                            android:id="@+id/resultFaceMask"
                            layout="@layout/show_detail"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="14dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="5dp"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/textView3" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:text="トーク分析"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/resultFaceLayout" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/analyzeSpeechLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    android:outlineSpotShadowColor="#1A000000"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView4">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:id="@+id/speechLabelLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="21dp"
                            android:gravity="center"
                            android:orientation="horizontal"
                            app:layout_constraintTop_toTopOf="parent">

                            <View
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:backgroundTint="@color/color_primary_40"
                                android:background="@drawable/circle" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:text="高評価なエリア"
                                android:textColor="@color/base_text"
                                android:textSize="12sp" />

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="20dp"
                                android:src="@drawable/icon_marker" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:text="あなたの評価"
                                android:textColor="@color/base_text"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <View
                            android:id="@+id/border1"
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="15dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/gray_border02"
                            app:layout_constraintTop_toBottomOf="@id/speechLabelLayout" />

                        <TextView
                            android:id="@+id/textView8"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:text="スピード"
                            android:textColor="@color/base_text"
                            android:textSize="16sp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/border1" />

                        <ImageButton
                            android:id="@+id/speedHint"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="4dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_tooltip"
                            app:layout_constraintBottom_toBottomOf="@id/textView8"
                            app:layout_constraintStart_toEndOf="@id/textView8"
                            app:layout_constraintTop_toTopOf="@id/textView8" />

                        <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                            android:id="@+id/transcriptionBar"
                            android:layout_width="match_parent"
                            android:layout_height="28dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginEnd="20dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/textView8" />

                        <TextView
                            android:id="@+id/minLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="遅い"
                            android:textColor="@color/base_text"
                            android:textSize="12sp"
                            app:layout_constraintStart_toStartOf="@id/transcriptionBar"
                            app:layout_constraintTop_toBottomOf="@id/transcriptionBar" />

                        <TextView
                            android:id="@+id/maxLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="早い"
                            android:textColor="@color/base_text"
                            android:textSize="12sp"
                            app:layout_constraintEnd_toEndOf="@id/transcriptionBar"
                            app:layout_constraintTop_toBottomOf="@id/transcriptionBar" />

                        <View
                            android:id="@+id/border2"
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="15dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/gray_border02"
                            app:layout_constraintTop_toBottomOf="@id/minLabel" />

                        <TextView
                            android:id="@+id/textView9"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:text="台本類似度"
                            android:textColor="@color/base_text"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="@id/similarityBar"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@id/similarityBar" />

                        <ImageButton
                            android:id="@+id/similarityHint"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="4dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_tooltip"
                            app:layout_constraintBottom_toBottomOf="@id/textView9"
                            app:layout_constraintStart_toEndOf="@id/textView9"
                            app:layout_constraintTop_toTopOf="@id/textView9" />

                        <TextView
                            android:id="@+id/textView10"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="10dp"
                            android:text="分析結果なし"
                            android:textColor="@color/gray_border01"
                            android:textSize="12sp"
                            app:layout_constraintBottom_toBottomOf="@id/similarityBar"
                            app:layout_constraintEnd_toStartOf="@id/similarityBar"
                            app:layout_constraintTop_toTopOf="@id/similarityBar" />

                        <jp.co.cac.ope.shokken.lab.ui.customview.SimilarityView
                            android:id="@+id/similarityBar"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginTop="15dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="20dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/border2" />

                        <include
                            android:id="@+id/analyzeSpeechMask"
                            layout="@layout/show_detail"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_margin="5dp"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/resultSpeechLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    android:outlineSpotShadowColor="#1A000000"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/analyzeSpeechLayout">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textView6"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:text="音声文字起こし"
                            android:textColor="@color/base_text"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/resultSpeechText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="20dp"
                            android:text="〇大学☓学部△学科の□□■■と申します。大学では◯✕を専攻して✕✕に関する研究をしておりました。学業以外では〇〇でのアルバイト活動に力を入れており、アルバイトの経験から接客の楽しさや、利用者ごとに納得してもらう難しさを十分に学ぶことができました。このような経験を元に他者へ喜んで貰えるような接客がどういったものなのか、日々向上心を持って学んで行こうと考えております。これから社会人として学んでいく上で取引先の方やお客様と接する機会も多いかと思いますが、アルバイトで培った経験を元に、相手が望んでいることを最大限汲み取って対応することを目指し活動したいと考えています。本日は貴重なお時間を頂き有難うございます。"
                            android:textColor="@color/base_text"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/textView6" />

                        <include
                            android:id="@+id/resultSpeechMask"
                            layout="@layout/show_detail"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="14dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="5dp"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/textView6" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <ImageButton
            android:id="@+id/memoButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:background="@color/white_transparent"
            android:src="@drawable/ic_memo"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/fullMovieLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                android:id="@+id/minisize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@color/white_transparent"
                android:src="@drawable/icon_close_fullscreen"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/fullControlLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="11dp"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent">

                <ImageButton
                    android:id="@+id/fullBackward"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_10backward_b"
                    android:visibility="gone" />

                <ImageButton
                    android:id="@+id/fullPlay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_play_b" />

                <ImageButton
                    android:id="@+id/fullPause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_pause_b"
                    android:visibility="gone" />

                <ImageButton
                    android:id="@+id/fullReplay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_replay_b"
                    android:visibility="gone" />

                <ImageButton
                    android:id="@+id/fullForward"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_10forward_b"
                    android:visibility="gone" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>