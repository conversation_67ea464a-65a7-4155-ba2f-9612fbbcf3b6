<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:layout_marginTop="0dp"
            android:paddingBottom="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <LinearLayout
                android:id="@+id/innerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="left"
                android:gravity="left"
                android:layout_marginRight="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                tools:ignore="MissingConstraints">

                <TextView
                    android:id="@+id/title1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:textColor="#382F2D"
                    android:textSize="20dp"
                    android:textStyle="bold"
                    android:textAlignment="center"
                    android:text="初回登録の方は\n1週間無料でPremiumプランを\nお楽しみいただけます。" />

                <TextView
                    android:id="@+id/title2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginEnd="28dp"
                    android:layout_marginBottom="20dp"
                    android:textColor="#382F2D"
                    android:textSize="14dp"
                    android:textAlignment="center"
                    android:text="支払いプランを選択してください。" />

<!--                <FrameLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="24dp"-->
<!--                    android:layout_marginEnd="24dp"-->
<!--                    android:background="@drawable/round_border_5dp_color_primary">-->

<!--                    <RadioButton-->
<!--                        android:id="@+id/radioButton2"-->
<!--                        android:layout_width="30dp"-->
<!--                        android:layout_height="30dp"-->
<!--                        android:layout_gravity="center_vertical"-->
<!--                        android:layout_marginStart="20dp" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:orientation="vertical">-->

<!--                        <TextView-->
<!--                            android:id="@+id/textView41"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:text="1ヶ月プラン"-->
<!--                            android:textColor="#382F2D"-->
<!--                            android:textSize="14dp"-->
<!--                            android:layout_marginTop="8dp"-->
<!--                            android:textStyle="bold" />-->

<!--                        <TextView-->
<!--                            android:id="@+id/textView42"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:text="1ヶ月毎に¥0000のお支払い"-->
<!--                            android:textColor="#382F2D"-->
<!--                            android:layout_marginBottom="8dp"-->
<!--                            android:textSize="12dp" />-->
<!--                    </LinearLayout>-->

<!--                </FrameLayout>-->

<!--                <FrameLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="24dp"-->
<!--                    android:layout_marginTop="16dp"-->
<!--                    android:layout_marginEnd="24dp"-->
<!--                    android:background="@drawable/round_border_5dp_gray">-->

<!--                    <RadioButton-->
<!--                        android:id="@+id/radioButton3"-->
<!--                        android:layout_width="30dp"-->
<!--                        android:layout_height="30dp"-->
<!--                        android:layout_gravity="center_vertical"-->
<!--                        android:layout_marginStart="20dp" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:orientation="vertical">-->

<!--                        <TextView-->
<!--                            android:id="@+id/textView43"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:text="3ヶ月プラン"-->
<!--                            android:textColor="#382F2D"-->
<!--                            android:textSize="14dp"-->
<!--                            android:layout_marginTop="8dp"-->
<!--                            android:textStyle="bold" />-->

<!--                        <TextView-->
<!--                            android:id="@+id/textView44"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:text="3ヶ月毎に¥0000のお支払い"-->
<!--                            android:textColor="#382F2D"-->
<!--                            android:layout_marginBottom="8dp"-->
<!--                            android:textSize="12dp" />-->
<!--                    </LinearLayout>-->

<!--                </FrameLayout>-->

<!--                <FrameLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="24dp"-->
<!--                    android:layout_marginTop="16dp"-->
<!--                    android:layout_marginEnd="24dp"-->
<!--                    android:background="@drawable/round_border_5dp_gray">-->

<!--                    <RadioButton-->
<!--                        android:id="@+id/radioButton4"-->
<!--                        android:layout_width="30dp"-->
<!--                        android:layout_height="30dp"-->
<!--                        android:layout_gravity="center_vertical"-->
<!--                        android:layout_marginStart="20dp" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:orientation="vertical">-->

<!--                        <TextView-->
<!--                            android:id="@+id/textView45"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:text="6ヶ月プラン"-->
<!--                            android:textColor="#382F2D"-->
<!--                            android:textSize="14dp"-->
<!--                            android:layout_marginTop="8dp"-->
<!--                            android:textStyle="bold" />-->

<!--                        <TextView-->
<!--                            android:id="@+id/textView46"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:text="6ヶ月毎に¥0000のお支払い"-->
<!--                            android:textColor="#382F2D"-->
<!--                            android:layout_marginBottom="8dp"-->
<!--                            android:textSize="12dp" />-->
<!--                    </LinearLayout>-->

<!--                </FrameLayout>-->

<!--                <FrameLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="24dp"-->
<!--                    android:layout_marginTop="16dp"-->
<!--                    android:layout_marginEnd="24dp"-->
<!--                    android:background="@drawable/round_border_5dp_gray">-->

<!--                    <RadioButton-->
<!--                        android:id="@+id/radioButton5"-->
<!--                        android:layout_width="30dp"-->
<!--                        android:layout_height="30dp"-->
<!--                        android:layout_gravity="center_vertical"-->
<!--                        android:layout_marginStart="20dp" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:orientation="vertical">-->

<!--                        <TextView-->
<!--                            android:id="@+id/textView47"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:text="12ヶ月プラン"-->
<!--                            android:textColor="#382F2D"-->
<!--                            android:textSize="14dp"-->
<!--                            android:layout_marginTop="8dp"-->
<!--                            android:textStyle="bold" />-->

<!--                        <TextView-->
<!--                            android:id="@+id/textView48"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:text="初回登録時1週間無料トライアル\n/以降12ヶ月毎に¥0000のお支払い"-->
<!--                            android:textColor="#382F2D"-->
<!--                            android:layout_marginBottom="8dp"-->
<!--                            android:textSize="12dp" />-->
<!--                    </LinearLayout>-->

<!--                </FrameLayout>-->

                <LinearLayout
                    android:id="@+id/btnWrapper"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginEnd="20dp"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/cancelBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
                        android:textColor="#382F2D"
                        app:backgroundTint="@null"
                        android:text="キャンセル" />

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.2">

                    </FrameLayout>

                    <Button
                        android:id="@+id/confirmBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/round_rectangle_25dp_color_primary"
                        app:backgroundTint="@null"
                        android:text="　 確認 　" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>