<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.analysis.AnalysisUploadViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.analysis.AnalysisUploadFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_setting"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="0dp"
            android:fillViewport="true"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:gravity="center"
                    android:text="動画を分析"
                    android:textColor="@color/base_text"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/description"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="61dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="61dp"
                    android:gravity="center"
                    android:text="スマホに保存されている\n選考用動画ファイルを採点します。\n分析には時間がかかります。\n分析中は他の機能を使用できません。\n分析が完了するまでお待ちください。"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title" />

                <Button
                    android:id="@+id/uploadButton"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="40dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/round_rectangle_25dp_color_primary"
                    android:gravity="center"
                    android:text="動画ファイルを選択"
                    android:textColor="@color/base_white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@null"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/description" />

                <LinearLayout
                    android:id="@+id/linearLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="32dp"
                    android:layout_marginEnd="28dp"
                    android:background="@drawable/round_rectangle_5dp_base_10"
                    android:orientation="vertical"
                    android:paddingStart="20dp"
                    android:paddingTop="24dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="24dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/uploadButton">

                    <TextView
                        android:id="@+id/availableMovie"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:gravity="center"
                        android:text="動画を分析"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/voicelayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:gravity="start|center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toBottomOf="@id/availableMovie">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_check_main" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="ひとりのみ写っている動画"
                            android:textColor="@color/base_text"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/howlayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="13dp"
                        android:gravity="start|center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toBottomOf="@id/textView1">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_check_main" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="3分以内で撮影した動画"
                            android:textColor="@color/base_text"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/tonelayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="13dp"
                        android:gravity="start|center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toBottomOf="@id/textView2">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_check_main" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="AndroidでHD撮影した動画"
                            android:textColor="@color/base_text"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <Button
                        android:id="@+id/hdSettingButton"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_marginStart="11dp"
                        android:layout_marginTop="21dp"
                        android:layout_marginEnd="11dp"
                        android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
                        android:drawableLeft="@drawable/icon_hint_b"
                        android:drawablePadding="8dp"
                        android:gravity="center"
                        android:paddingStart="48dp"
                        android:paddingEnd="44dp"
                        android:text="HD撮影の設定方法"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        app:backgroundTint="@null" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>