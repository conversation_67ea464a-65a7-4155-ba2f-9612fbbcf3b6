<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_image"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="26dp"
            android:layout_marginTop="@dimen/login_input_title1_margin_top"
            android:text="@string/login_input_create_new_user"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar" />

        <Button
            android:id="@+id/createButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginTop="@dimen/login_input_create_button_margin_top"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/login_create_button"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:id="@+id/lineLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="@dimen/login_input_or_line_margin_top"
            android:gravity="center|center_horizontal"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/createButton">

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1"
            android:layout_marginEnd="1dp"
            android:background="@color/gray_border01"/>

        <TextView
            android:id="@+id/textView4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/create_new_user_line"
            android:textColor="@color/gray_placeholder"
            android:textSize="12sp" />

        <View
            android:id="@+id/view2"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="1dp"
            android:layout_marginStart="1dp"
            android:background="@color/gray_border01"/>
        </LinearLayout>

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="26dp"
            android:layout_marginTop="@dimen/login_input_title2_margin_top"
            android:text="@string/login_input_title"
            android:textColor="@color/base_text"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/lineLayout" />

        <TextView
            android:id="@+id/textView3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_input_email_margin_top"
            android:text="@string/login_input_email"
            android:textColor="@color/base_text"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView2" />

        <include
            android:id="@+id/email"
            layout="@layout/input_text_email"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_input_email_inputarea_margin_top"
            android:layout_marginEnd="28dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView3" />

        <TextView
            android:id="@+id/textView5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_input_password_margin_top"
            android:text="@string/login_input_password"
            android:textColor="@color/base_text"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/email" />

        <include
            android:id="@+id/password"
            layout="@layout/input_text_password"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_input_password_inputarea_margin_top"
            android:layout_marginEnd="28dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView5" />

        <TextView
            android:id="@+id/forget_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/login_input_forget_password_margin_top"
            android:text="@string/login_input_forget_password"
            android:textColor="@color/color_primary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password" />

        <Button
            android:id="@+id/loginButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginTop="@dimen/login_input_login_button_margin_top"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/login_login_button"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintTop_toBottomOf="@id/forget_password"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>