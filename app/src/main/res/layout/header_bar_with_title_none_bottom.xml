<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:background="@color/base_white">

    <ImageButton
        android:id="@+id/backButton"
        android:layout_width="36dp"
        android:layout_height="wrap_content"
        android:layout_weight="0"
        android:layout_gravity="center_vertical"
        android:background="@color/white_transparent"
        app:srcCompat="@drawable/btn_back" />

    <TextView
        android:id="@+id/headerTitle"
        android:layout_width="wrap_content"
        android:layout_height="29dp"
        android:layout_weight="65535"
        android:layout_gravity="center_vertical"
        android:textColor="@color/base_text"
        android:textSize="20sp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
            xmlns:app="http://schemas.android.com/apk/res-auto"
            xmlns:tools="http://schemas.android.com/tools"
            android:id="@+id/item_card"
            android:layout_width="81dp"
            android:layout_height="24dp"
            app:cardCornerRadius="5dp"
            android:layout_marginRight="16dp"
            android:layout_gravity="center_vertical"
            android:backgroundTint="@color/gray_bg01">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:gravity="center">
            <TextView
                android:id="@+id/currentCountTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="0dp"
                android:singleLine="true"
                android:ellipsize="end"
                android:text="0"
                android:textStyle="bold"
                android:textColor="@color/color_primary"
                android:textSize="16sp" />
            <TextView
                android:id="@+id/unitTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="0dp"
                android:singleLine="true"
                android:ellipsize="end"
                android:text="件"
                android:textColor="@color/color_primary"
                android:textSize="12sp" />
            <TextView
                android:id="@+id/totalCountTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="0dp"
                android:singleLine="true"
                android:ellipsize="end"
                android:text="/0"
                android:textColor="#999999"
                android:textSize="12sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

</LinearLayout>