<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.interview.InterviewFaceAdjustViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.interview.InterviewFaceAdjustFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="2/3"
            android:textColor="@color/gray_placeholder"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/backHeaderBar" />

        <View
            android:id="@+id/currentBarBaseView"
            android:layout_width="match_parent"
            android:layout_height="7dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/round_rectangle_10dp_white_and_color_primary_line"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView" />

        <LinearLayout
            android:id="@+id/progressBar3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/currentBarBaseView"
            app:layout_constraintEnd_toEndOf="@id/currentBarBaseView"
            app:layout_constraintStart_toStartOf="@id/currentBarBaseView"
            app:layout_constraintTop_toTopOf="@+id/currentBarBaseView">

            <View
                android:layout_width="0dp"
                android:layout_height="7dp"
                android:layout_weight="2"
                android:background="@drawable/round_rectangle_10dp_color_primary_line" />

            <View
                android:layout_width="0dp"
                android:layout_height="7dp"
                android:layout_weight="1" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/progressBar2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/currentBarBaseView"
            app:layout_constraintEnd_toEndOf="@id/currentBarBaseView"
            app:layout_constraintStart_toStartOf="@id/currentBarBaseView"
            app:layout_constraintTop_toTopOf="@+id/currentBarBaseView">

            <View
                android:layout_width="0dp"
                android:layout_height="7dp"
                android:layout_weight="1"
                android:background="@drawable/round_rectangle_10dp_color_primary_line" />

            <View
                android:layout_width="0dp"
                android:layout_height="7dp"
                android:layout_weight="1" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            app:layout_constraintBottom_toTopOf="@id/nextButton"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/currentBarBaseView">

            <TextureView
                android:id="@+id/textureView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/descriptionView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@color/gray_transparent"
                android:gravity="center"
                android:padding="12dp"
                android:text="枠が黄色になるまで枠内に顔を合わせましょう\n顔(眉毛～顎)が髪の毛、マスク、サングラス、帽子などで隠れないようにしてください"
                android:textColor="@color/base_white"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <jp.co.cac.ope.shokken.lab.ui.customview.FaceTrackingView
                android:id="@+id/faceTrackingView"
                android:layout_width="220dp"
                android:layout_height="220dp"
                android:background="@android:color/transparent"
                app:layout_constraintBottom_toBottomOf="@id/textureView"
                app:layout_constraintEnd_toEndOf="@id/textureView"
                app:layout_constraintStart_toStartOf="@id/textureView"
                app:layout_constraintTop_toTopOf="@id/textureView" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <Button
            android:id="@+id/nextButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/round_rectangle_25dp_gray"
            android:drawableEnd="@drawable/ic_arrow_next_white_16dp"
            android:drawablePadding="8dp"
            android:enabled="false"
            android:gravity="center"
            android:padding="0dp"
            android:paddingStart="48dp"
            android:paddingTop="0dp"
            android:paddingEnd="14dp"
            android:paddingBottom="0dp"
            android:text="次へ"
            android:textColor="@color/base_white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>