<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.createnewuser.CreateNewUserEmailAuthenticationViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.createnewuser.CreateNewUserEmailAuthenticationFragment">

        <LinearLayout
            android:id="@+id/backHeaderBar"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:background="@drawable/header_shadow_bottom_large"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="-56dp"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/headerTitle"
                    android:layout_width="46dp"
                    android:layout_height="46dp"
                    android:layout_gravity="center"
                    android:layout_marginVertical="8dp"
                    app:srcCompat="@drawable/img_logo" />
            </FrameLayout>


            <ImageButton
                android:id="@+id/logoutBtn"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_gravity="center_vertical"
                android:background="@color/white_transparent"
                android:padding="16dp"
                app:srcCompat="@drawable/ic_logout_24"
                app:tint="@android:color/black" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white">

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:text="Activate Account"
                    android:textColor="@color/base_text"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView5"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="28dp"
                    android:gravity="center"
                    android:text="Registration is not yet complete. Please enter the 6-digit activation code that was sent to the email address you entered."
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView4" />

                <LinearLayout
                    android:id="@+id/codeLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="40dp"
                    android:layout_marginEnd="28dp"
                    android:gravity="top|center_horizontal"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView5">

                    <include
                        android:id="@+id/code1"
                        layout="@layout/input_text_email"
                        android:layout_width="44dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="0dp"
                        android:layout_marginTop="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="parent" />

                    <include
                        android:id="@+id/code2"
                        layout="@layout/input_text_email"
                        android:layout_width="44dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="parent" />

                    <include
                        android:id="@+id/code3"
                        layout="@layout/input_text_email"
                        android:layout_width="44dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="parent" />

                    <include
                        android:id="@+id/code4"
                        layout="@layout/input_text_email"
                        android:layout_width="44dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="parent" />

                    <include
                        android:id="@+id/code5"
                        layout="@layout/input_text_email"
                        android:layout_width="44dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="parent" />

                    <include
                        android:id="@+id/code6"
                        layout="@layout/input_text_email"
                        android:layout_width="44dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="parent" />
                </LinearLayout>

                <TextView
                    android:id="@+id/textView10"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="32dp"
                    android:text="メールアドレス"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/codeLayout" />

                <TextView
                    android:id="@+id/email"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="43dp"
                    android:layout_marginTop="12dp"
                    android:text="<EMAIL>"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView10" />

                <TextView
                    android:id="@+id/textView13"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="24dp"
                    android:text="@string/login_forget_password_confirm_not_receive"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/email" />

                <TextView
                    android:id="@+id/textView14"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    android:text="@string/login_forget_password_confirm_not_receive_description1"
                    android:textColor="@color/base_text"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView13" />

                <TextView
                    android:id="@+id/textView15"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="28dp"
                    android:text="@string/login_forget_password_confirm_not_receive_description2"
                    android:textColor="@color/base_text"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView14" />

                <TextView
                    android:id="@+id/textView16"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="8dp"
                    android:text="@string/login_forget_password_confirm_not_receive_description3"
                    android:textColor="@color/base_text"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView15" />

                <TextView
                    android:id="@+id/textView17"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="0dp"
                    android:onClick="@{() -> viewModel.onInquiryButtonClick()}"
                    android:text="@string/login_forget_password_confirm_not_receive_description4"
                    android:textColor="@color/color_primary"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@id/textView16"
                    app:layout_constraintTop_toTopOf="@id/textView16" />

                <TextView
                    android:id="@+id/textView18"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="0dp"
                    android:text="@string/login_forget_password_confirm_not_receive_description5"
                    android:textColor="@color/base_text"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@id/textView17"
                    app:layout_constraintTop_toTopOf="@id/textView16" />

                <TextView
                    android:id="@+id/textView19"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="2dp"
                    android:text="@string/login_forget_password_confirm_not_receive_description6"
                    android:textColor="@color/base_text"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView16" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="156dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView19" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/closeButton"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:onClick="@{() -> viewModel.onSendCompleteButtonClick()}"
            android:padding="0dp"
            android:text="認証する"
            android:textColor="@color/base_white"
            android:textSize="16sp"
            app:backgroundTint="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>