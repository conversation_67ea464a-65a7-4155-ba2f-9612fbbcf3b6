<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"
        android:paddingTop="0dp"
        android:paddingBottom="0dp"
        android:orientation="vertical"
        android:background="@drawable/round_rectangle_5dp_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/spacer"
            android:layout_width="match_parent"
            android:layout_height="41dp" />

        <TextView
            android:id="@+id/titleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="20dp"
            android:text="Premiumプラン初回登録のみ\n1週間無料トライアル"
            android:textAlignment="center"
            android:textColor="@color/base_text"
            android:textStyle="bold"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/messageTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="20dp"
            android:text="採点結果の詳細情報を閲覧するにはログインまたは会員登録後Premiumプランに登録してください。"
            android:textAlignment="center"
            android:textColor="@color/base_text"
            android:textSize="14sp" />

        <Button
            android:id="@+id/closeButton"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="48dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:drawableEnd="@drawable/ic_arrow_next_white_16dp"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:paddingStart="48dp"
            android:paddingTop="0dp"
            android:paddingEnd="14dp"
            android:paddingBottom="0dp"
            android:stateListAnimator="@null"
            android:text="ログイン/新規会員登録"
            android:textAllCaps="false"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp" />
        <View
            android:id="@+id/spaceView"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            tools:ignore="MissingConstraints" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>