<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_white">

    <LinearLayout
        android:id="@+id/headerBar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/header_shadow_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/closeButton"
            android:layout_width="36dp"
            android:layout_height="wrap_content"
            android:layout_weight="0"
            android:layout_gravity="center_vertical"
            android:background="@color/white_transparent"
            app:srcCompat="@drawable/btn_close" />
    </LinearLayout>
    <ScrollView
        android:id="@+id/selectView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="48dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerBar">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            tools:ignore="MissingConstraints">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="パスワード変更が完了しました"
                android:gravity="center"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:textColor="@color/base_text"
                android:textStyle="bold"
                android:textSize="20sp"
                tools:ignore="MissingConstraints" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="引き続きカチメン！をお楽しみください。"
                android:gravity="center"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:textColor="@color/base_text"
                android:textSize="14sp"
                tools:ignore="MissingConstraints" />

        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/backBtn"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginBottom="12dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/round_rectangle_25dp_color_primary"
        android:gravity="center"
        android:padding="0dp"
        android:stateListAnimator="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="16sp"
        android:textStyle="bold"
        app:backgroundTint="@null"
        app:elevation="0dp"
        android:text="ホーム画面へ戻る" />

</androidx.constraintlayout.widget.ConstraintLayout>