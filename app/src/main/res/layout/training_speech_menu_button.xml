<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/spacer"
        android:layout_width="match_parent"
        android:layout_height="7dp" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:cardElevation="7dp"
        app:layout_constraintTop_toBottomOf="@id/spacer">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/round_rectangle_5dp_base_10">

            <ImageView
                android:id="@+id/voice_image"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="14dp"
                android:src="@drawable/icon_voice"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/checked_image"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="14dp"
                android:src="@drawable/icon_mogimen_checked"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="66dp"
                android:layout_marginTop="11dp"
                android:layout_marginEnd="14dp"
                android:layout_marginBottom="11dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="新卒：自己紹介"
                        android:textColor="@color/base_text"
                        android:textSize="16sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="bottom"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/target_time_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="目安時間 : "
                        android:textColor="@color/gray_icon"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/target_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1分"
                        android:textColor="@color/gray_icon"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <View
        android:layout_width="match_parent"
        android:layout_height="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card" />
</androidx.constraintlayout.widget.ConstraintLayout>