<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.login.LoginForgetPasswordCompleteViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.login.LoginForgetPasswordCompleteFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/login_forget_password_input_title_margin_top"
            android:text="パスワード再設定が完了しました"
            android:textSize="20sp"
            android:textColor="@color/base_text"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar" />

        <TextView
            android:id="@+id/textView7"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_forget_password_input_top_description_margin_top"
            android:layout_marginEnd="28dp"
            android:textAlignment="center"
            android:textSize="14sp"
            android:textColor="@color/base_text"
            android:text="引き続きカチメン！をお楽しみください。"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView4" />

        <Button
            android:id="@+id/button3"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="5dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:onClick="@{() -> viewModel.onLoginButtonClick()}"
            android:text="ログインする"
            android:textColor="@color/base_white"
            app:backgroundTint="@null"
            android:textSize="16sp"
            android:textStyle="bold"
            android:padding="0dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
