<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/mogimenImage"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/btn_top_mock_interview" />

        <TextView
            android:id="@+id/mogimenTextView"
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:layout_gravity="center_horizontal"
            android:text="練習モード"
            android:textColor="@color/base_text"
            android:textSize="12sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/actualInterviewImage"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_gravity="center_vertical"
            android:layout_marginHorizontal="24dp"
            android:src="@drawable/btn_top_actual_interview" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:layout_gravity="center_horizontal"
            android:text="本番モード"
            android:textColor="@color/base_text"
            android:textSize="12sp" />
    </LinearLayout>

    <!--<LinearLayout
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center">
        <ImageView
            android:id="@+id/videoImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="50dp"
            android:layout_marginEnd="50dp"
            android:src="@drawable/btn_top_video" />
        <TextView
            android:id="@+id/videoTextView"
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:layout_gravity="center_horizontal"
            android:text="選考動画分析"
            android:textColor="@color/base_text"
            android:textSize="12sp" />
    </LinearLayout>-->

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/trainingImage"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/btn_top_training_circle" />

        <TextView
            android:id="@+id/trainingTextView"
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:layout_gravity="center_horizontal"
            android:text="表情トレーニング"
            android:textColor="@color/base_text"
            android:textSize="12sp" />
    </LinearLayout>
</LinearLayout>