<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:id="@+id/mainView"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/buttonLargeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="horizontal">
        <Button
            android:id="@+id/planCangeButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:text="プラン変更"
            android:textColor="@color/base_white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null" />
    </LinearLayout>

    <TextView
        android:id="@+id/cancelPlanTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:text="@string/cancel_plan_text"
        android:textColor="@color/color_primary"
        android:textStyle="normal"
        android:textSize="12sp"/>

</LinearLayout>