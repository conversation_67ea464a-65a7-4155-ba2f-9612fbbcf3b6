<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.tutorial.TutorialTopViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_tutorial_bg01"
        android:backgroundTint="@color/base_tutorial_bg01"
        tools:context=".ui.tutorial.TutorialTopFragment">

        <ImageView
            android:id="@+id/keyVisualView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:layout_marginTop="0dp"
            android:layout_marginStart="0dp"
            android:layout_marginEnd="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/img_tutorial_mv" />

        <View
            android:id="@+id/descriptionView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tutorial_top_description_height"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="@dimen/tutorial_top_description_margin_top"
            android:layout_marginRight="16dp"
            android:background="@drawable/round_rectangle_10dp_white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/keyVisualView" />

        <ImageView
            android:id="@+id/teacherView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginTop="@dimen/tutorial_teacher_margin_top"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:src="@drawable/img_tutorial_teacher_area"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/keyVisualView" />

        <TextView
            android:id="@+id/descriptionTitle1TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/tutorial_description_title1_margin_start"
            android:layout_marginTop="@dimen/tutorial_description_title1_margin_top"
            android:text="@string/tutorial_description_title1"
            android:textColor="@color/color_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@id/descriptionView"
            app:layout_constraintTop_toTopOf="@id/descriptionView" />

        <TextView
            android:id="@+id/descriptionTitle2TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginBottom="0dp"
            android:text="@string/tutorial_description_title2"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/descriptionTitle1TextView"
            app:layout_constraintBottom_toBottomOf="@id/descriptionTitle1TextView" />

        <TextView
            android:id="@+id/descriptionTitle3TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="0dp"
            android:layout_marginTop="-6dp"
            android:text="@string/tutorial_description_title3"
            android:textColor="@color/color_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@id/descriptionTitle1TextView"
            app:layout_constraintTop_toBottomOf="@id/descriptionTitle1TextView" />

        <TextView
            android:id="@+id/descriptionTitle4TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginBottom="0dp"
            android:text="@string/tutorial_description_title4"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/descriptionTitle3TextView"
            app:layout_constraintBottom_toBottomOf="@id/descriptionTitle3TextView" />

        <TextView
            android:id="@+id/description1TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="@dimen/tutorial_description1_margin_top"
            android:layout_marginEnd="14dp"
            android:text="@string/tutorial_description1"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="@id/descriptionView"
            app:layout_constraintStart_toStartOf="@id/descriptionView"
            app:layout_constraintTop_toBottomOf="@id/descriptionTitle3TextView" />

        <TextView
            android:id="@+id/description2TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="@dimen/tutorial_description2_margin_top"
            android:layout_marginEnd="14dp"
            android:text="@string/tutorial_description2"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="@id/descriptionView"
            app:layout_constraintStart_toStartOf="@id/descriptionView"
            app:layout_constraintTop_toBottomOf="@id/description1TextView" />

        <ImageView
            android:id="@+id/logoView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginTop="@dimen/tutorial_margin_top"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:src="@drawable/img_tutorial_logo_rec"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descriptionView" />

        <View
            android:id="@+id/bottomView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/tutorial_bottom_margin_top"
            android:background="@color/base_white"
            android:backgroundTint="@color/base_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/logoView" />

        <LinearLayout
            android:id="@+id/pagerLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/tutorial_pager_margin_top"
            android:gravity="top|center_horizontal"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@id/bottomView"
            app:layout_constraintEnd_toEndOf="@id/bottomView"
            app:layout_constraintStart_toStartOf="@id/bottomView"
            app:layout_constraintTop_toTopOf="@id/bottomView">

            <ImageView
                android:id="@+id/pagerOne"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet_active" />

            <ImageView
                android:id="@+id/pagerTwo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerOne" />

            <ImageView
                android:id="@+id/pagerThree"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerTwo" />

            <ImageView
                android:id="@+id/pagerFour"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerThree" />

            <ImageView
                android:id="@+id/pagerFive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerFour" />

            <ImageView
                android:id="@+id/pagerSix"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerFive" />
        </LinearLayout>

        <Button
            android:id="@+id/skipButton"
            android:layout_width="72dp"
            android:layout_height="32dp"
            android:layout_marginTop="@dimen/tutorial_skip_margin_top"
            android:layout_marginEnd="@dimen/tutorial_skip_margin_end"
            android:background="@drawable/round_rectangle_16dp_white_and_color_primary_line"
            android:onClick="@{() -> viewModel.onSkipButtonClick()}"
            android:stateListAnimator="@null"
            android:text="@string/tutorial_skip"
            android:padding="0dp"
            android:gravity="center"
            android:textColor="@color/color_primary"
            android:textSize="14sp"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>