<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.analysis.AnalysisRecordResultViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.analysis.AnalysisRecordResultFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white">

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="44dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="44dp"
                    android:gravity="center"
                    android:text="お疲れ様でした！\nトレーニングを振り返ってみましょう"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/movieLayout"
                    android:layout_width="268dp"
                    android:layout_height="476dp"
                    android:layout_marginTop="20dp"
                    android:background="@color/base_white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView1">

                    <ImageButton
                        android:id="@+id/fullsize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_fullscreen"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:id="@+id/controlLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="11dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <ImageButton
                            android:id="@+id/backward"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_10backward_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/play"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_play_b" />

                        <ImageButton
                            android:id="@+id/pause"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_pause_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/replay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_replay_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/forward"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_10forward_b"
                            android:visibility="gone" />
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:text="表情分析"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/movieLayout" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/analyzeFaceLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView2">

                    <com.github.mikephil.charting.charts.RadarChart
                        android:id="@+id/radarChart"
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:layout_marginStart="81dp"
                        android:layout_marginTop="65dp"
                        android:layout_marginEnd="81dp"
                        android:layout_marginBottom="57dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/radarLabel1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="笑顔"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toTopOf="@id/radarChart"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <TextView
                        android:id="@+id/radarLabel1Score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="2dp"
                        android:text="--"
                        android:textColor="@color/color_primary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toTopOf="@id/radarLabel1"
                        app:layout_constraintEnd_toEndOf="@id/radarLabel1"
                        app:layout_constraintStart_toStartOf="@id/radarLabel1" />

                    <ImageButton
                        android:id="@+id/radarLabel1Button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/radarLabel1"
                        app:layout_constraintStart_toEndOf="@id/radarLabel1"
                        app:layout_constraintTop_toTopOf="@id/radarLabel1Score" />

                    <TextView
                        android:id="@+id/radarLabel2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:layout_marginBottom="81dp"
                        android:text="真剣熱意"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="@id/radarChart"
                        app:layout_constraintStart_toEndOf="@id/radarChart" />

                    <TextView
                        android:id="@+id/radarLabel2Score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="2dp"
                        android:text="--"
                        android:textColor="@color/color_primary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toTopOf="@id/radarLabel2"
                        app:layout_constraintEnd_toEndOf="@id/radarLabel2"
                        app:layout_constraintStart_toStartOf="@id/radarLabel2" />

                    <ImageButton
                        android:id="@+id/radarLabel2Button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintEnd_toEndOf="@id/radarLabel2"
                        app:layout_constraintStart_toStartOf="@id/radarLabel2"
                        app:layout_constraintTop_toBottomOf="@id/radarLabel2" />

                    <TextView
                        android:id="@+id/radarLabel3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="111dp"
                        android:layout_marginTop="20dp"
                        android:text="興味関心"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/radarChart"
                        app:layout_constraintTop_toBottomOf="@id/radarChart" />

                    <TextView
                        android:id="@+id/radarLabel3Score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="2dp"
                        android:text="--"
                        android:textColor="@color/color_primary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toTopOf="@id/radarLabel3"
                        app:layout_constraintEnd_toEndOf="@id/radarLabel3"
                        app:layout_constraintStart_toStartOf="@id/radarLabel3" />

                    <ImageButton
                        android:id="@+id/radarLabel3Button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/radarLabel3"
                        app:layout_constraintStart_toEndOf="@id/radarLabel3"
                        app:layout_constraintTop_toTopOf="@id/radarLabel3Score" />

                    <TextView
                        android:id="@+id/radarLabel4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="111dp"
                        android:text="落ち着き"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/radarChart"
                        app:layout_constraintTop_toBottomOf="@id/radarChart" />

                    <TextView
                        android:id="@+id/radarLabel4Score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="2dp"
                        android:text="--"
                        android:textColor="@color/color_primary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toTopOf="@id/radarLabel4"
                        app:layout_constraintEnd_toEndOf="@id/radarLabel4"
                        app:layout_constraintStart_toStartOf="@id/radarLabel4" />

                    <ImageButton
                        android:id="@+id/radarLabel4Button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/radarLabel4"
                        app:layout_constraintStart_toEndOf="@id/radarLabel4"
                        app:layout_constraintTop_toTopOf="@id/radarLabel3Score" />

                    <TextView
                        android:id="@+id/radarLabel5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:layout_marginBottom="81dp"
                        android:text="興味関心"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="@id/radarChart"
                        app:layout_constraintEnd_toStartOf="@id/radarChart" />

                    <TextView
                        android:id="@+id/radarLabel5Score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="2dp"
                        android:text="--"
                        android:textColor="@color/color_primary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toTopOf="@id/radarLabel5"
                        app:layout_constraintEnd_toEndOf="@id/radarLabel5"
                        app:layout_constraintStart_toStartOf="@id/radarLabel5" />

                    <ImageButton
                        android:id="@+id/radarLabel5Button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintEnd_toEndOf="@id/radarLabel5"
                        app:layout_constraintStart_toStartOf="@id/radarLabel5"
                        app:layout_constraintTop_toBottomOf="@id/radarLabel5" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/resultFaceLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/analyzeFaceLayout">

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="表情評価"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/resultFaceText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="20dp"
                        android:layout_marginBottom="20dp"
                        android:text="穏やかな笑顔が出ています。笑顔でいると自分も相手も、明るい気持ちになり話しやすくなるので、もっと笑顔で話せるようにトレーニングしましょう。以下のポイントを改善することで、スコアアップ、好印象を与えることができます。"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView3" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:text="トーク分析"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/resultFaceLayout" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/resultSpeechLayoutLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView4">

                    <TextView
                        android:id="@+id/textView5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="音声評価"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/resultSpeechText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="20dp"
                        android:layout_marginBottom="20dp"
                        android:text="音声評価アドバイスのテキストが入ります。音声評価アドバイスのテキストが入ります。音声評価アドバイスのテキストが入ります。音声評価アドバイスのテキストが入ります。音声評価アドバイスのテキストが入ります。"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView5" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/analyzeSpeechLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/resultSpeechLayoutLayout">

                    <LinearLayout
                        android:id="@+id/speechLabelLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="21dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toTopOf="parent">

                        <View
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:backgroundTint="@color/color_primary_40"
                            android:background="@drawable/circle" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="高評価なエリア"
                            android:textColor="@color/base_text"
                            android:textSize="12sp" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:src="@drawable/icon_marker" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="あなたの評価"
                            android:textColor="@color/base_text"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <View
                        android:id="@+id/border1"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/speechLabelLayout" />

                    <TextView
                        android:id="@+id/title1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="明るさ"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border1" />

                    <ImageButton
                        android:id="@+id/hintButton1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title1"
                        app:layout_constraintStart_toEndOf="@id/title1"
                        app:layout_constraintTop_toTopOf="@id/title1" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar1"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title1" />

                    <TextView
                        android:id="@+id/minLabel1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="暗い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar1"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar1" />

                    <TextView
                        android:id="@+id/maxLabel1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="明るい"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar1"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar1" />

                    <View
                        android:id="@+id/border2"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel1" />

                    <TextView
                        android:id="@+id/title2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="エネルギー"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border2" />

                    <ImageButton
                        android:id="@+id/hintButton2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title2"
                        app:layout_constraintStart_toEndOf="@id/title2"
                        app:layout_constraintTop_toTopOf="@id/title2" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar2"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title2" />

                    <TextView
                        android:id="@+id/minLabel2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="低い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar2"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar2" />

                    <TextView
                        android:id="@+id/maxLabel2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="高い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar2"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar2" />

                    <View
                        android:id="@+id/border3"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel2" />

                    <TextView
                        android:id="@+id/title3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="ボリューム"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border3" />

                    <ImageButton
                        android:id="@+id/hintButton3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title3"
                        app:layout_constraintStart_toEndOf="@id/title3"
                        app:layout_constraintTop_toTopOf="@id/title3" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar3"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title3" />

                    <TextView
                        android:id="@+id/minLabel3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="小さい"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar3"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar3" />

                    <TextView
                        android:id="@+id/maxLabel3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="大きい"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar3"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar3" />

                    <View
                        android:id="@+id/border4"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel3" />

                    <TextView
                        android:id="@+id/title4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="ピッチ"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border4" />

                    <ImageButton
                        android:id="@+id/hintButton4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title4"
                        app:layout_constraintStart_toEndOf="@id/title4"
                        app:layout_constraintTop_toTopOf="@id/title4" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar4"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title4" />

                    <TextView
                        android:id="@+id/minLabel4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="低い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar4"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar4" />

                    <TextView
                        android:id="@+id/maxLabel4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="高い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar4"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar4" />

                    <View
                        android:id="@+id/border5"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel4" />

                    <TextView
                        android:id="@+id/title5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="インターバル"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border5" />

                    <ImageButton
                        android:id="@+id/hintButton5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title5"
                        app:layout_constraintStart_toEndOf="@id/title5"
                        app:layout_constraintTop_toTopOf="@id/title5" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar5"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title5" />

                    <TextView
                        android:id="@+id/minLabel5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="20dp"
                        android:text="短い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar5"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar5" />

                    <TextView
                        android:id="@+id/maxLabel5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="20dp"
                        android:text="長い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar5"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar5" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <Button
                    android:id="@+id/trainingButton"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="32dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/round_rectangle_25dp_color_primary"
                    android:gravity="center"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text="トレーニングメニュー"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@null"
                    app:elevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/analyzeSpeechLayout" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    app:layout_constraintTop_toBottomOf="@id/trainingButton" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <ImageButton
            android:id="@+id/memoButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:background="@color/white_transparent"
            android:src="@drawable/ic_memo"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/fullMovieLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                android:id="@+id/minisize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@color/white_transparent"
                android:src="@drawable/icon_close_fullscreen"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/fullControlLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="11dp"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent">

                <ImageButton
                    android:id="@+id/fullBackward"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_10backward_b"
                    android:visibility="gone" />

                <ImageButton
                    android:id="@+id/fullPlay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_play_b" />

                <ImageButton
                    android:id="@+id/fullPause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_pause_b"
                    android:visibility="gone" />

                <ImageButton
                    android:id="@+id/fullReplay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_replay_b"
                    android:visibility="gone" />

                <ImageButton
                    android:id="@+id/fullForward"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@color/white_transparent"
                    android:src="@drawable/icon_10forward_b"
                    android:visibility="gone" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>