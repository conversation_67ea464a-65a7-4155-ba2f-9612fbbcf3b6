<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/textView11"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="48dp"
            android:layout_marginRight="20dp"
            android:text="Premiumプラン初回登録のみ 1週間無料トライアル"
            android:textStyle="bold"
            android:textAlignment="center"
            android:textColor="#382F2D"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/textView12"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:text="このコンテンツを利用するには ログインまたは会員登録後Premiumプランに登録してください。"
            android:textAlignment="center"
            android:textColor="#382F2D"
            android:textSize="14sp" />

        <Button
            android:id="@+id/button4"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="48dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="ログイン/新規会員登録"
            android:textAllCaps="false"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>