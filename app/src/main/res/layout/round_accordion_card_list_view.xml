<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:backgroundTint="@color/base_white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/headerTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="15dp"
                android:layout_marginBottom="15dp"
                android:text="Title"
                android:textColor="@color/base_text"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/round_accordion_card_list_view_arrowImageView"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_arrow_down_color_primary_16dp" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/round_accordion_card_list_view_detailsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>