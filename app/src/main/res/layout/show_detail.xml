<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_white">

    <Button
        android:id="@+id/button"
        android:layout_width="175dp"
        android:layout_height="40dp"
        android:background="@drawable/round_rectangle_25dp_color_primary"
        android:gravity="center"
        android:padding="0dp"
        android:stateListAnimator="@null"
        android:text="詳細を見る"
        android:textSize="14sp"
        android:textStyle="bold"
        app:backgroundTint="@null"
        app:elevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>