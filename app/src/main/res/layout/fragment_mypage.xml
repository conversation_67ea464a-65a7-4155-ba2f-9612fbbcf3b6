<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.mypage.MyPageFragment">

    <include
        android:id="@+id/mainPageHeaderBar"
        layout="@layout/header_bar_main_page_title_center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="56dp"
        android:translationY="-56dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mainPageHeaderBar"
        app:strokeColor="#E6E6E6"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="36dp"
                android:layout_marginVertical="20dp"
                android:gravity="center"
                android:text="Account Information"
                android:textAlignment="gravity"
                android:textColor="@color/base_text"
                android:textSize="20sp"
                android:textStyle="bold" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E6E6E6" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:srcCompat="@drawable/ic_mail" />

                <TextView
                    android:id="@+id/emailTxt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    tools:text="<EMAIL>" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E6E6E6" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:srcCompat="@drawable/ic_hourglass_fill" />

                <TextView
                    android:id="@+id/expireTxt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    tools:text="Expires on 2025年08月18日" />
            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!--<ScrollView
        android:id="@+id/selectView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="48dp"
        android:background="@color/gray_bg01"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mainPageHeaderBar">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/base_white"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            tools:ignore="MissingConstraints">

            <include
                android:id="@+id/partMypageName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                layout="@layout/part_mypage_name"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="40dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <include
                android:id="@+id/partMypageLoginNot"
                layout="@layout/part_mypage_login_not"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <include
                android:id="@+id/partMypageLoginFree"
                layout="@layout/part_mypage_login_free"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <include
                android:id="@+id/partMypageLoginTral"
                layout="@layout/part_mypage_login_tral"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            &lt;!&ndash;<View
                android:id="@+id/campaignMargin"
                android:layout_width="match_parent"
                android:layout_height="24dp"
                android:visibility="gone"/>&ndash;&gt;

            <include
                android:id="@+id/partMypageLoginCurrentPlan"
                layout="@layout/part_mypage_login_current_plan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            &lt;!&ndash;<ImageView
                android:id="@+id/imgCampaignBg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="12dp"
                android:src="@drawable/img_campaign_bg" />&ndash;&gt;

            <TextView
                android:id="@+id/planChangeMessageView"
                android:layout_width="216dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:text="利用中プランの次回更新日時経過後より 1ヶ月プランが適用されます。"
                android:textColor="@color/base_text"
                android:textStyle="normal"
                android:textSize="12sp"/>

            <include
                android:id="@+id/partMypageLoginPlanChange"
                layout="@layout/part_mypage_login_plan_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="40dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_gravity="center"
                android:background="#EEEEEE" />
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_height="match_parent">
                <Button
                    android:id="@+id/accountConfirmButton"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginTop="0dp"
                    android:background="@color/base_white"
                    android:textColor="@color/base_text"
                    android:gravity="left|center_vertical"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text="アカウント情報確認・変更"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    app:backgroundTint="@null"
                    app:elevation="0dp"
                    tools:ignore="MissingConstraints" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_next_base_16dp"
                    android:layout_marginRight="-4dp"
                    tools:ignore="MissingConstraints"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            &lt;!&ndash;<View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_gravity="center"
                android:background="#EEEEEE" />
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_height="match_parent">
                <Button
                    android:id="@+id/scheduleListButton"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginTop="0dp"
                    android:background="@color/base_white"
                    android:textColor="@color/base_text"
                    android:gravity="left|center_vertical"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text="予定リスト"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    app:backgroundTint="@null"
                    app:elevation="0dp"
                    tools:ignore="MissingConstraints" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_next_base_16dp"
                    android:layout_marginRight="-4dp"
                    tools:ignore="MissingConstraints"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>&ndash;&gt;

        </LinearLayout>
    </ScrollView>-->

</androidx.constraintlayout.widget.ConstraintLayout>