<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/top_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="18dp"
        android:layout_marginEnd="16dp"
        android:gravity="start"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/recording"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/round_rectangle_5dp_orange"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingTop="4dp"
            android:paddingEnd="10dp"
            android:paddingBottom="4dp"
            android:text="録音中"
            android:textColor="@color/base_white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/target_time_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="目安時間 : "
            android:textColor="@color/gray_placeholder"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/target_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1分"
            android:textColor="@color/gray_placeholder"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/timeCountLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="00:00"
            android:textColor="@color/base_text"
            android:textSize="20sp"
            android:textStyle="bold" />
    </LinearLayout>

    <jp.co.cac.ope.shokken.lab.ui.customview.MyselfScrollView
        android:id="@+id/myselfScrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="40dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="36dp"
        android:layout_marginBottom="23dp"
        app:layout_constraintBottom_toTopOf="@id/endButton"
        app:layout_constraintTop_toBottomOf="@id/top_status" />

    <jp.co.cac.ope.shokken.lab.ui.customview.SeekBarView
        android:id="@+id/seekBar"
        android:layout_width="15dp"
        android:layout_height="0dp"
        android:layout_marginStart="15dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintBottom_toBottomOf="@id/myselfScrollView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/myselfScrollView" />

    <ImageView
        android:id="@+id/count_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_count3_w"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/endButton"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/round_rectangle_25dp_color_primary"
        android:gravity="center"
        android:text="終了"
        android:textColor="@color/base_white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:backgroundTint="@null"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>