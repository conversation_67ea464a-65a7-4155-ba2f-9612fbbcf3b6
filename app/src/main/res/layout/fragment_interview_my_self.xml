<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.interview.InterviewMySelfViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.interview.InterviewMySelfFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="1/3"
            android:textColor="@color/gray_placeholder"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/backHeaderBar" />

        <View
            android:id="@+id/currentBarBaseView"
            android:layout_width="match_parent"
            android:layout_height="7dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/round_rectangle_10dp_white_and_color_primary_line"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView" />

        <LinearLayout
            android:id="@+id/progressBar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/currentBarBaseView"
            app:layout_constraintEnd_toEndOf="@id/currentBarBaseView"
            app:layout_constraintStart_toStartOf="@id/currentBarBaseView"
            app:layout_constraintTop_toTopOf="@+id/currentBarBaseView">

            <View
                android:layout_width="0dp"
                android:layout_height="7dp"
                android:layout_weight="1"
                android:background="@drawable/round_rectangle_10dp_color_primary_line" />

            <View
                android:layout_width="0dp"
                android:layout_height="7dp"
                android:layout_weight="2" />
        </LinearLayout>

        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/interview_myself_title"
            android:textColor="@color/base_text"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progressBar" />

        <ImageView
            android:id="@+id/showHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:clickable="true"
            android:focusable="true"
            android:src="@drawable/btn_hint_b"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progressBar" />

        <TextView
            android:id="@+id/descriptionText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="28dp"
            android:gravity="center"
            android:text="@string/interview_myself_description"
            android:textColor="@color/base_text"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleText" />

        <include
            android:id="@+id/mySelf"
            layout="@layout/input_text"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="28dp"
            android:layout_marginTop="21dp"
            android:layout_marginEnd="28dp"
            android:layout_marginBottom="12dp"
            app:layout_constraintBottom_toTopOf="@id/showExampleButton"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descriptionText" />

        <TextView
            android:id="@+id/countText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginBottom="34dp"
            android:text="300"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@id/checkboxView"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/countTextBase1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="34dp"
            android:text="@string/interview_myself_count_base1"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@id/checkboxView"
            app:layout_constraintStart_toEndOf="@id/countText" />

        <TextView
            android:id="@+id/countTextTarget"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="34dp"
            android:text="300"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@id/checkboxView"
            app:layout_constraintStart_toEndOf="@id/countTextBase1" />

        <TextView
            android:id="@+id/countTextBase2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="34dp"
            android:text="@string/interview_myself_count_base2"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@id/checkboxView"
            app:layout_constraintStart_toEndOf="@id/countTextTarget" />

        <Button
            android:id="@+id/showExampleButton"
            android:layout_width="102dp"
            android:layout_height="28dp"
            android:layout_marginEnd="30dp"
            android:layout_marginBottom="29dp"
            android:background="@drawable/round_rectangle_16dp_white_and_color_primary_line"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/interview_myself_show_example"
            android:textColor="@color/color_primary"
            android:textSize="12sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintBottom_toTopOf="@id/checkboxView"
            app:layout_constraintEnd_toEndOf="parent" />

        <LinearLayout
            android:id="@+id/checkboxView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="23dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toTopOf="@id/nextButton"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <CheckBox
                android:id="@+id/checkbox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginStart="4dp"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white"
                android:checked="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:text="@string/interview_myself_not_use_my_self"
                android:textColor="@color/base_text"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/showCheckboxHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:clickable="true"
                android:focusable="true"
                android:src="@drawable/btn_tooltip" />
        </LinearLayout>

        <Button
            android:id="@+id/nextButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:drawableEnd="@drawable/ic_arrow_next_white_16dp"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="0dp"
            android:paddingStart="48dp"
            android:paddingTop="0dp"
            android:paddingEnd="14dp"
            android:paddingBottom="0dp"
            android:text="@string/interview_myself_next"
            android:textColor="@color/base_white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>