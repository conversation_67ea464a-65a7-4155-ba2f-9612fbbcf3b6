<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.createnewuser.CreateNewUserInputViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.createnewuser.CreateNewUserInputFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white">

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="ユーザ名"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <include
                    android:id="@+id/requireText1"
                    layout="@layout/require_text"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="0dp"
                    app:layout_constraintStart_toEndOf="@id/textView1"
                    app:layout_constraintTop_toTopOf="@id/textView1" />

                <include
                    android:id="@+id/username"
                    layout="@layout/input_text_email"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView1" />

                <include
                    android:id="@+id/username_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/username"
                    app:layout_constraintEnd_toEndOf="@id/username"
                    app:layout_constraintTop_toBottomOf="@id/username" />

                <TextView
                    android:id="@+id/textView7"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="生年月日"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/username_validation" />

                <include
                    android:id="@+id/requireText4"
                    layout="@layout/require_text"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="0dp"
                    app:layout_constraintStart_toEndOf="@id/textView7"
                    app:layout_constraintTop_toTopOf="@id/textView7" />

                <LinearLayout
                    android:id="@+id/birthdayLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    android:gravity="top|center_horizontal"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView7">

                    <Spinner
                        android:id="@+id/birthday_year"
                        android:layout_width="86dp"
                        android:layout_height="40dp"
                        android:background="@drawable/spinner_background" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="8dp"
                        android:text="年"
                        android:textColor="@color/base_text"
                        android:textSize="12sp" />

                    <Spinner
                        android:id="@+id/birthday_month"
                        android:layout_width="71dp"
                        android:layout_height="40dp"
                        android:background="@drawable/spinner_background" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="8dp"
                        android:text="月"
                        android:textColor="@color/base_text"
                        android:textSize="12sp" />

                    <Spinner
                        android:id="@+id/birthday_day"
                        android:layout_width="71dp"
                        android:layout_height="40dp"
                        android:background="@drawable/spinner_background" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="8dp"
                        android:text="日"
                        android:textColor="@color/base_text"
                        android:textSize="12sp" />
                </LinearLayout>

                <include
                    android:id="@+id/birthday_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/birthdayLayout"
                    app:layout_constraintEnd_toEndOf="@id/birthdayLayout"
                    app:layout_constraintTop_toBottomOf="@id/birthdayLayout" />

                <TextView
                    android:id="@+id/textView8"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="性別"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/birthday_validation" />

                <include
                    android:id="@+id/requireText5"
                    layout="@layout/require_text"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="0dp"
                    app:layout_constraintStart_toEndOf="@id/textView8"
                    app:layout_constraintTop_toTopOf="@id/textView8" />

                <LinearLayout
                    android:id="@+id/genderLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    android:gravity="top|center_horizontal"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView8">

                    <Button
                        android:id="@+id/maleButton"
                        android:layout_width="101dp"
                        android:layout_height="40dp"
                        android:background="@drawable/round_rectangle_5dp_white_with_gray_line"
                        android:gravity="center"
                        android:padding="0dp"
                        android:text="男性"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        app:backgroundTint="@null" />

                    <Button
                        android:id="@+id/femaleButton"
                        android:layout_width="101dp"
                        android:layout_height="40dp"
                        android:background="@drawable/round_rectangle_5dp_white_with_gray_line"
                        android:gravity="center"
                        android:padding="0dp"
                        android:text="女性"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        app:backgroundTint="@null" />

                    <Button
                        android:id="@+id/otherButton"
                        android:layout_width="101dp"
                        android:layout_height="40dp"
                        android:background="@drawable/round_rectangle_5dp_white_with_gray_line"
                        android:gravity="center"
                        android:padding="0dp"
                        android:text="その他"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        app:backgroundTint="@null" />
                </LinearLayout>

                <include
                    android:id="@+id/gender_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/genderLayout"
                    app:layout_constraintEnd_toEndOf="@id/genderLayout"
                    app:layout_constraintTop_toBottomOf="@id/genderLayout" />

                <TextView
                    android:id="@+id/textView9"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="卒業予定年月または既卒年月"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/gender_validation" />

                <include
                    android:id="@+id/requireText6"
                    layout="@layout/require_text"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="0dp"
                    app:layout_constraintStart_toEndOf="@id/textView9"
                    app:layout_constraintTop_toTopOf="@id/textView9" />

                <LinearLayout
                    android:id="@+id/graduationLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    android:gravity="top|center_horizontal"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView9">

                    <Spinner
                        android:id="@+id/graduation_date_year"
                        android:layout_width="128dp"
                        android:layout_height="40dp"
                        android:background="@drawable/spinner_background" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="8dp"
                        android:text="年"
                        android:textColor="@color/base_text"
                        android:textSize="12sp" />

                    <Spinner
                        android:id="@+id/graduation_date_month"
                        android:layout_width="128dp"
                        android:layout_height="40dp"
                        android:background="@drawable/spinner_background" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="8dp"
                        android:text="月"
                        android:textColor="@color/base_text"
                        android:textSize="12sp" />
                </LinearLayout>

                <include
                    android:id="@+id/graduation_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/graduationLayout"
                    app:layout_constraintEnd_toEndOf="@id/graduationLayout"
                    app:layout_constraintTop_toBottomOf="@id/graduationLayout" />

                <LinearLayout
                    android:id="@+id/graduationTypeLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    android:gravity="top|center_horizontal"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/graduation_validation">

                    <Button
                        android:id="@+id/willGraduationButton"
                        android:layout_width="152dp"
                        android:layout_height="40dp"
                        android:background="@drawable/round_rectangle_5dp_white_with_gray_line"
                        android:gravity="center"
                        android:padding="0dp"
                        android:text="卒業予定"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        app:backgroundTint="@null" />

                    <Button
                        android:id="@+id/graduationButton"
                        android:layout_width="152dp"
                        android:layout_height="40dp"
                        android:background="@drawable/round_rectangle_5dp_white_with_gray_line"
                        android:gravity="center"
                        android:padding="0dp"
                        android:text="既卒"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        app:backgroundTint="@null" />
                </LinearLayout>

                <include
                    android:id="@+id/graduation_type_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/graduationTypeLayout"
                    app:layout_constraintEnd_toEndOf="@id/graduationTypeLayout"
                    app:layout_constraintTop_toBottomOf="@id/graduationTypeLayout" />

                <TextView
                    android:id="@+id/textView10"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="学校名"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/graduation_type_validation" />

                <include
                    android:id="@+id/school_name"
                    layout="@layout/input_text_email"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView10" />

                <include
                    android:id="@+id/school_name_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/school_name"
                    app:layout_constraintEnd_toEndOf="@id/school_name"
                    app:layout_constraintTop_toBottomOf="@id/school_name" />

                <Button
                    android:id="@+id/updateBtn"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="32dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/round_rectangle_25dp_color_primary"
                    android:gravity="center"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text="更新する"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@null"
                    app:elevation="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/school_name_validation" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>