<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.actual_interview.ActualInterviewResultViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.actual_interview.ActualInterviewResultFragment">

        <LinearLayout
            android:id="@+id/backHeaderBar"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/header_shadow_bottom"
            android:elevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/headerTitle"
                android:layout_width="0dp"
                android:layout_height="29dp"
                android:layout_gravity="center"
                android:layout_marginHorizontal="16dp"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingEnd="-52dp"
                android:textAlignment="gravity"
                android:textColor="@color/base_text"
                android:textSize="20sp"
                tools:text="Title" />

            <ImageButton
                android:id="@+id/closeButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center_vertical"
                android:layout_weight="0"
                android:background="@color/white_transparent"
                app:srcCompat="@drawable/btn_close"
                app:tint="@color/base_text" />
        </LinearLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/layoutFooter"
            app:layout_constraintTop_toBottomOf="@+id/backHeaderBar" />

        <LinearLayout
            android:id="@+id/layoutFooter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:orientation="vertical"
            android:paddingBottom="4dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:id="@+id/saveAnalyzeLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="24dp"
                    android:layout_marginTop="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="今回の分析結果を保存する"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/saveAnalyze"
                        android:layout_width="51dp"
                        android:layout_height="31dp"
                        android:checked="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/saveAnalyzeDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="24dp"
                    android:text="※保存せずにこの画面を閉じると結果とメモは削除されます"
                    android:textColor="@color/base_text"
                    android:textSize="12sp" />
            </LinearLayout>

            <Button
                android:id="@+id/trainingButton"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/round_rectangle_25dp_color_primary"
                android:gravity="center"
                android:padding="0dp"
                android:stateListAnimator="@null"
                android:text="トレーニングメニュー"
                android:textSize="16sp"
                android:textStyle="bold"
                app:backgroundTint="@null"
                app:elevation="0dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>