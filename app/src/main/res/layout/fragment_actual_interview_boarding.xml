<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_white"
    android:backgroundTint="@color/base_white"
    android:orientation="vertical"
    tools:context=".ui.actual_interview.ActualInterviewBoardingFragment">

    <LinearLayout
        android:id="@+id/mainPageHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/header_shadow_bottom">

        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="0"
            android:background="@color/white_transparent"
            app:srcCompat="@drawable/ic_round_arrow_back_24"
            app:tint="@color/base_text" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="24dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="@string/actual_interview_boarding_message"
        android:textAlignment="gravity"
        android:textColor="@android:color/black"
        android:textSize="16sp" />

    <Button
        android:id="@+id/startButton"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/round_rectangle_25dp_color_primary"
        android:gravity="center"
        android:padding="0dp"
        android:paddingHorizontal="48dp"
        android:paddingTop="0dp"
        android:paddingBottom="0dp"
        android:text="@string/actual_interview_boarding_action_button_text"
        android:textColor="@color/base_white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:backgroundTint="@null" />
</LinearLayout>