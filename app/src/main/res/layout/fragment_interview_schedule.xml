<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.mypage.InterviewScheduleViewModel" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.mypage.InterviewScheduleFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:layout_marginTop="0dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <LinearLayout
                android:id="@+id/innerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="left"
                android:gravity="left"
                android:layout_marginRight="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:id="@+id/dtlineView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="left|center_vertical"
                    android:gravity="left|center_vertical"
                    android:layout_marginTop="20dp"
                    tools:ignore="MissingConstraints">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="予定日時"
                        android:textAlignment="textStart"
                        android:layout_marginRight="10dp"
                        android:textColor="@color/base_text"
                        android:textStyle="bold"
                        android:textSize="16sp" />
                    <include
                        layout="@layout/require_text"
                        android:layout_width="34dp"
                        android:layout_height="18dp"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/dtvaluelineView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="left|center_vertical"
                    android:gravity="left|center_vertical"
                    android:layout_marginTop="10dp"
                    tools:ignore="MissingConstraints">
                    <TextView
                        android:id="@+id/dateTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="——年—月—日(-)"
                        android:textAlignment="textStart"
                        android:textColor="@color/base_text"
                        android:textStyle="normal"
                        android:textSize="14sp" />
                    <ImageView
                        android:id="@+id/iconCalendar"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/icon_calendar"
                        android:layout_marginLeft="8dp"
                        tools:ignore="MissingConstraints" />
                    <TextView
                        android:id="@+id/timeTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="PM —:—"
                        android:layout_marginLeft="20dp"
                        android:textAlignment="textStart"
                        android:textColor="@color/base_text"
                        android:textStyle="normal"
                        android:textSize="14sp" />
                    <ImageView
                        android:id="@+id/iconClock"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/icon_clock"
                        android:layout_marginLeft="8dp"
                        tools:ignore="MissingConstraints" />
                </LinearLayout>
                <include
                    android:id="@+id/interview_date_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />
                <include
                    android:id="@+id/interview_time_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="left|center_vertical"
                    android:gravity="left|center_vertical"
                    android:layout_marginTop="20dp"
                    tools:ignore="MissingConstraints">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="企業名"
                        android:textAlignment="textStart"
                        android:layout_marginRight="10dp"
                        android:textColor="@color/base_text"
                        android:textStyle="bold"
                        android:textSize="16sp" />
                    <include
                        layout="@layout/require_text"
                        android:layout_width="34dp"
                        android:layout_height="18dp"/>
                </LinearLayout>
                <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/companyNameConstraintLayout"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/input_text_with_border">
                    <EditText
                        android:id="@+id/companyNameTextInput"
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@null"
                        android:backgroundTint="@color/gray_placeholder"
                        android:hint="@string/login_input_email_inputarea_hint"
                        android:inputType="textAutoComplete"
                        android:textSize="14sp"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <include
                    android:id="@+id/companyName_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="選考状況"
                    android:textAlignment="textStart"
                    android:layout_marginTop="20dp"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <Spinner
                    android:id="@+id/selection_status"
                    android:layout_width="match_parent"
                    android:layout_marginTop="10dp"
                    android:layout_height="40dp"
                    android:background="@drawable/spinner_background" />

            <LinearLayout
                android:id="@+id/otherSelectionStatusView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="left|center_vertical"
                android:gravity="left|center_vertical"
                android:visibility="visible"
                tools:ignore="MissingConstraints">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="選考状況(その他)"
                    android:textAlignment="textStart"
                    android:layout_marginTop="20dp"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/otherSelectionStatusConstraintLayout"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/input_text_with_border">
                    <EditText
                        android:id="@+id/otherSelectionStatusTextInput"
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@null"
                        android:backgroundTint="@color/gray_placeholder"
                        android:hint="@string/login_input_email_inputarea_hint"
                        android:inputType="textAutoComplete"
                        android:textSize="14sp"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <include
                    android:id="@+id/other_selection_status_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />
            </LinearLayout>

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="場所"
                    android:textAlignment="textStart"
                    android:layout_marginTop="20dp"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/locationConstraintLayout"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/input_text_with_border">
                    <EditText
                        android:id="@+id/locationTextInput"
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@null"
                        android:backgroundTint="@color/gray_placeholder"
                        android:hint="@string/login_input_email_inputarea_hint"
                        android:inputType="textAutoComplete"
                        android:textSize="14sp"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <include
                    android:id="@+id/location_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="担当者"
                    android:textAlignment="textStart"
                    android:layout_marginTop="20dp"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/staffConstraintLayout"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/input_text_with_border">
                    <EditText
                        android:id="@+id/staffTextInput"
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@null"
                        android:backgroundTint="@color/gray_placeholder"
                        android:hint="@string/login_input_email_inputarea_hint"
                        android:inputType="textAutoComplete"
                        android:textSize="14sp"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <include
                    android:id="@+id/staff_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="担当者電話番号"
                    android:textAlignment="textStart"
                    android:layout_marginTop="20dp"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/staffTelConstraintLayout"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/input_text_with_border">
                    <EditText
                        android:id="@+id/staffTelTextInput"
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@null"
                        android:backgroundTint="@color/gray_placeholder"
                        android:hint="@string/login_input_email_inputarea_hint"
                        android:inputType="textAutoComplete"
                        android:textSize="14sp"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <include
                    android:id="@+id/staff_tel_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="担当者メールアドレス"
                    android:textAlignment="textStart"
                    android:layout_marginTop="20dp"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/staffMailaddressConstraintLayout"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/input_text_with_border">
                    <EditText
                        android:id="@+id/staffMailaddressTextInput"
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@null"
                        android:backgroundTint="@color/gray_placeholder"
                        android:hint="@string/login_input_email_inputarea_hint"
                        android:inputType="textEmailAddress"
                        android:textSize="14sp"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <include
                    android:id="@+id/staff_mailaddress_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="メモ"
                    android:textAlignment="textStart"
                    android:layout_marginTop="20dp"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/memoConstraintLayout"
                    android:layout_width="match_parent"
                    android:layout_height="346dp"
                    android:background="@drawable/input_text_with_border">
                    <androidx.core.widget.NestedScrollView
                        android:layout_width="match_parent"
                        android:layout_height="322dp"
                        android:fillViewport="true"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">

                        <EditText
                            android:id="@+id/memoTextInput"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="0dp"
                            android:layout_marginTop="0dp"
                            android:layout_marginEnd="0dp"
                            android:layout_marginBottom="0dp"
                            android:background="@null"
                            android:backgroundTint="@color/gray_placeholder"
                            android:hint="@string/login_input_email_inputarea_hint"
                            android:inputType="textMultiLine"
                            android:textSize="14sp"
                            android:textColor="@color/base_text"
                            android:gravity="top"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.core.widget.NestedScrollView>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <include
                    android:id="@+id/memo_validation"
                    layout="@layout/validation_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="left|center_vertical"
                    android:gravity="left|center_vertical"
                    tools:ignore="MissingConstraints">
                    <TextView
                        android:id="@+id/countTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="0字 / 最大1000字"
                        android:textAlignment="textStart"
                        android:textColor="@color/base_text"
                        android:textStyle="normal"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:layout_marginTop="30dp"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginBottom="0dp">

                    <Button
                        android:id="@+id/registButton"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="50dp"
                        android:layout_marginStart="0dp"
                        android:layout_marginBottom="0dp"
                        android:background="@drawable/round_rectangle_25dp_color_primary"
                        android:text="登録する"
                        android:textColor="@color/base_white"
                        android:textStyle="bold"
                        app:backgroundTint="@null"
                        android:stateListAnimator="@null"
                        android:textSize="16sp"
                        android:padding="0dp"
                        android:gravity="center" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginBottom="20dp">

                    <Button
                        android:id="@+id/cancelButton"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="50dp"
                        android:layout_marginStart="0dp"
                        android:layout_marginBottom="0dp"
                        android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
                        android:text="キャンセル"
                        android:textColor="@color/base_text"
                        android:textStyle="bold"
                        app:backgroundTint="@null"
                        android:stateListAnimator="@null"
                        android:textSize="16sp"
                        android:padding="0dp"
                        android:gravity="center" />
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/bottomSpace"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:layout_marginBottom="200dp">
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>