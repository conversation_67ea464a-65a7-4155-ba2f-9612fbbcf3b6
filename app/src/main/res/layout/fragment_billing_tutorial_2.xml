<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.billing.BillingTutorial2Fragment" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.tutorial.TutorialTopFragment">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_white"
            android:orientation="vertical">

            <Button
                android:id="@+id/skipButton"
                android:layout_gravity="right"
                android:layout_width="72dp"
                android:layout_height="32dp"
                android:layout_marginTop="50dp"
                android:layout_marginEnd="@dimen/tutorial_skip_margin_end"
                android:background="@drawable/round_rectangle_16dp_white_and_color_primary_line"
                android:stateListAnimator="@null"
                android:text="@string/tutorial_skip"
                android:padding="0dp"
                android:gravity="center"
                android:textColor="@color/color_primary"
                android:textSize="14sp"
                app:backgroundTint="@null"
                app:elevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"/>

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:src="@drawable/icon_premium_content1"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textColor="@color/base_text"
                android:textSize="16sp"
                android:layout_gravity="center"
                android:textAlignment="center"
                android:layout_marginTop="12dp"
                android:text="詳細な採点結果で振り返る"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/base_text"
                android:layout_gravity="center"
                android:textAlignment="center"
                android:layout_marginTop="8dp"
                android:text="無料版では閲覧できなかった詳細な採点結果で\nさらに自己分析を深堀り。"/>

            <FrameLayout
                android:id="@+id/colorPrimaryArea"
                android:layout_width="match_parent"
                android:layout_height="394dp"
                android:background="@color/base_tutorial_bg01"
                android:layout_marginTop="51dp"
                android:layout_gravity="center">
                <ImageView
                    android:id="@+id/tutorialSpImage"
                    android:layout_width="253dp"
                    android:layout_height="366dp"
                    android:layout_gravity="bottom|center"
                    android:src="@drawable/img_android_premium_tutorial1"/>
            </FrameLayout>


            <LinearLayout
                android:id="@+id/pagerLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top|center_horizontal"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="16dp"
                app:layout_constraintStart_toStartOf="parent">

                <ImageView
                    android:id="@+id/pagerOne"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet" />

                <ImageView
                    android:id="@+id/pagerTwo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet_active"
                    app:layout_constraintStart_toEndOf="@id/pagerOne" />

                <ImageView
                    android:id="@+id/pagerThree"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet"
                    app:layout_constraintStart_toEndOf="@id/pagerTwo" />

                <ImageView
                    android:id="@+id/pagerFour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet"
                    app:layout_constraintStart_toEndOf="@id/pagerThree" />
            </LinearLayout>

            <View
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"/>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>