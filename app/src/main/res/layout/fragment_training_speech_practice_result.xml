<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.training.TrainingSpeechPracticeResultViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.training.TrainingSpeechPracticeResultFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white">

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="44dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="44dp"
                    android:gravity="center"
                    android:text="お疲れ様でした！\nトレーニングを振り返ってみましょう"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/audioLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="40dp"
                    android:layout_marginEnd="24dp"
                    android:background="@color/base_white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView1">

                    <jp.co.cac.ope.shokken.lab.ui.customview.SeekBarView
                        android:id="@+id/seekBar"
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/currentTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="00:00"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/seekBar"
                        app:layout_constraintTop_toBottomOf="@id/seekBar" />

                    <TextView
                        android:id="@+id/contentTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="00:00"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/seekBar"
                        app:layout_constraintTop_toBottomOf="@id/seekBar" />

                    <LinearLayout
                        android:id="@+id/controlLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/currentTime">

                        <ImageButton
                            android:id="@+id/backward"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_10backward_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/play"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_play_b" />

                        <ImageButton
                            android:id="@+id/pause"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_pause_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/replay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_replay_b"
                            android:visibility="gone" />

                        <ImageButton
                            android:id="@+id/forward"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@color/white_transparent"
                            android:src="@drawable/icon_10forward_b"
                            android:visibility="gone" />
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="60dp"
                    android:gravity="center"
                    android:text="トーク分析"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/audioLayout" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/resultSpeechLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView4">

                    <TextView
                        android:id="@+id/resultSpeechText"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="20dp"
                        android:layout_marginBottom="20dp"
                        android:text="〇大学☓学部△学科の□□■■と申します。大学では◯✕を専攻して✕✕に関する研究をしておりました。学業以外では〇〇でのアルバイト活動に力を入れており、アルバイトの経験から接客の楽しさや、利用者ごとに納得してもらう難しさを十分に学ぶことができました。このような経験を元に他者へ喜んで貰えるような接客がどういったものなのか、日々向上心を持って学んで行こうと考えております。これから社会人として学んでいく上で取引先の方やお客様と接する機会も多いかと思いますが、アルバイトで培った経験を元に、相手が望んでいることを最大限汲み取って対応することを目指し活動したいと考えています。本日は貴重なお時間を頂き有難うございます。"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/analyzeSpeechLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/resultSpeechLayout">

                    <LinearLayout
                        android:id="@+id/speechLabelLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="21dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toTopOf="parent">

                        <View
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:backgroundTint="@color/color_primary_40"
                            android:background="@drawable/circle" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="高評価なエリア"
                            android:textColor="@color/base_text"
                            android:textSize="12sp" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:src="@drawable/icon_marker" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="あなたの評価"
                            android:textColor="@color/base_text"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <View
                        android:id="@+id/border1"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/speechLabelLayout" />

                    <TextView
                        android:id="@+id/title1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="明るさ"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border1" />

                    <ImageButton
                        android:id="@+id/hintButton1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title1"
                        app:layout_constraintStart_toEndOf="@id/title1"
                        app:layout_constraintTop_toTopOf="@id/title1" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar1"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title1" />

                    <TextView
                        android:id="@+id/minLabel1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="暗い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar1"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar1" />

                    <TextView
                        android:id="@+id/maxLabel1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="明るい"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar1"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar1" />

                    <View
                        android:id="@+id/border2"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel1" />

                    <TextView
                        android:id="@+id/title2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="エネルギー"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border2" />

                    <ImageButton
                        android:id="@+id/hintButton2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title2"
                        app:layout_constraintStart_toEndOf="@id/title2"
                        app:layout_constraintTop_toTopOf="@id/title2" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar2"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title2" />

                    <TextView
                        android:id="@+id/minLabel2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="低い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar2"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar2" />

                    <TextView
                        android:id="@+id/maxLabel2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="高い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar2"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar2" />

                    <View
                        android:id="@+id/border3"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel2" />

                    <TextView
                        android:id="@+id/title3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="ボリューム"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border3" />

                    <ImageButton
                        android:id="@+id/hintButton3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title3"
                        app:layout_constraintStart_toEndOf="@id/title3"
                        app:layout_constraintTop_toTopOf="@id/title3" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar3"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title3" />

                    <TextView
                        android:id="@+id/minLabel3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="小さい"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar3"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar3" />

                    <TextView
                        android:id="@+id/maxLabel3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="大きい"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar3"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar3" />

                    <View
                        android:id="@+id/border4"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel3" />

                    <TextView
                        android:id="@+id/title4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="ピッチ"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border4" />

                    <ImageButton
                        android:id="@+id/hintButton4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title4"
                        app:layout_constraintStart_toEndOf="@id/title4"
                        app:layout_constraintTop_toTopOf="@id/title4" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar4"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title4" />

                    <TextView
                        android:id="@+id/minLabel4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="低い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar4"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar4" />

                    <TextView
                        android:id="@+id/maxLabel4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="高い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar4"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar4" />

                    <View
                        android:id="@+id/border5"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel4" />

                    <TextView
                        android:id="@+id/title5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="インターバル"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border5" />

                    <ImageButton
                        android:id="@+id/hintButton5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title5"
                        app:layout_constraintStart_toEndOf="@id/title5"
                        app:layout_constraintTop_toTopOf="@id/title5" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar5"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title5" />

                    <TextView
                        android:id="@+id/minLabel5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="20dp"
                        android:text="短い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar5"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar5" />

                    <TextView
                        android:id="@+id/maxLabel5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="20dp"
                        android:text="長い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar5"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar5" />

                    <View
                        android:id="@+id/border6"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:background="@color/gray_border02"
                        app:layout_constraintTop_toBottomOf="@id/minLabel5" />

                    <TextView
                        android:id="@+id/title6"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="スピード"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/border6" />

                    <ImageButton
                        android:id="@+id/hintButton6"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/icon_tooltip"
                        app:layout_constraintBottom_toBottomOf="@id/title6"
                        app:layout_constraintStart_toEndOf="@id/title6"
                        app:layout_constraintTop_toTopOf="@id/title6" />

                    <jp.co.cac.ope.shokken.lab.ui.customview.TranscriptionBarView
                        android:id="@+id/transcriptionBar6"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/title6" />

                    <TextView
                        android:id="@+id/minLabel6"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="20dp"
                        android:text="遅い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="@id/transcriptionBar6"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar6" />

                    <TextView
                        android:id="@+id/maxLabel6"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="20dp"
                        android:text="早い"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="@id/transcriptionBar6"
                        app:layout_constraintTop_toBottomOf="@id/transcriptionBar6" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    app:layout_constraintTop_toBottomOf="@id/analyzeSpeechLayout" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>