<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.home.ColumnListViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.home.ColumnListFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_setting"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/notFoundColumns"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="登録がありません"
            android:visibility="gone"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:textColor="#999999"
            android:textStyle="bold"
            android:textSize="20sp"
            tools:ignore="MissingConstraints"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar" />

        <androidx.recyclerview.widget.RecyclerView
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar"/>

        <LinearLayout
            android:id="@+id/bottomContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <Button
                    android:id="@+id/allColumnButton"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="208dp"
                    android:layout_height="40dp"
                    android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
                    android:text="すべてのコラムを見る"
                    android:paddingStart="-16dp"
                    android:textColor="@color/base_text"
                    app:backgroundTint="@null"
                    android:textSize="14sp"
                    android:padding="0dp"
                    android:visibility="visible"
                    android:stateListAnimator="@null"
                    android:gravity="center"
                    tools:ignore="MissingConstraints"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />
                <ImageView
                    android:id="@+id/iconTrash"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_next_base_16dp"
                    android:layout_marginRight="24dp"
                    tools:ignore="MissingConstraints"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>