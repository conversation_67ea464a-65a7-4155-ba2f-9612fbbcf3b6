<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"
        android:background="@drawable/round_rectangle_5dp_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/messageTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="56dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="48dp"
            android:text="@string/training_announce_modal_message"
            android:textAlignment="center"
            android:textColor="@color/base_text"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:adjustViewBounds="false"
            android:scaleType="centerCrop"
            android:src="@drawable/img_camera_focus"
            app:layout_constraintDimensionRatio="w,197:261"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/messageTextView" />

        <LinearLayout
            android:id="@+id/checkView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="25dp"
            android:layout_marginEnd="20dp"
            android:gravity="center|center_vertical"
            android:orientation="horizontal"
            android:padding="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imageView">

            <CheckBox
                android:id="@+id/checkbox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white"
                android:checked="false"
                android:enabled="true" />

            <TextView
                android:id="@+id/textView8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="@string/training_announce_modal_check_message"
                android:textColor="@color/base_text"
                android:textSize="14sp" />
        </LinearLayout>

        <Button
            android:id="@+id/closeButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="25dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/training_announce_modal_button"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/checkView" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>