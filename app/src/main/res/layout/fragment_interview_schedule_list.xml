<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.mypage.InterviewScheduleListViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mainView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.mypage.InterviewScheduleListFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title_none_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/tabBar"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:orientation="horizontal"
            android:layout_gravity="center"
            android:background="@drawable/border_bottom_color_primary_usui_1dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/backHeaderBar"
            tools:ignore="MissingConstraints">

            <TextView
                android:id="@+id/status1TextView"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="39dp"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:gravity="center"
                android:textSize="16sp"
                android:textColor="@color/base_text"
                android:text="現在の予定"
                android:textStyle="bold"
                android:background="@drawable/border_bottom_color_primary_3dp">
            </TextView>
            <TextView
                android:id="@+id/status2TextView"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="39dp"
                android:layout_marginRight="8dp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:gravity="center"
                android:textSize="16sp"
                android:text="過去の予定"
                android:textStyle="normal"
                android:textColor="@color/base_text"
                android:background="@color/base_white">
            </TextView>
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:layout_marginTop="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tabBar">

            <LinearLayout
                android:id="@+id/listContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="0dp"
                android:layout_marginRight="0dp"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/seachFromLayout"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/input_search">

                    <ImageView
                        android:id="@+id/searchButton"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="@color/white_transparent"
                        app:srcCompat="@drawable/baseline_search_48"
                        tools:ignore="MissingConstraints"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="10dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <EditText
                        android:id="@+id/companyNameSearchInput"
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:layout_marginStart="40dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@null"
                        android:inputType="textEmailAddress"
                        android:textSize="14sp"
                        android:textColor="@color/base_text"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/itemListContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:visibility="gone"
                    android:orientation="vertical" />
                <LinearLayout
                    android:id="@+id/noitemContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="48dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:visibility="visible"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/notFoundPickupSchedules"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="登録がありません"
                        android:visibility="visible"
                        android:layout_gravity="center"
                        android:layout_marginBottom="16dp"
                        android:textColor="#999999"
                        android:textStyle="bold"
                        android:textSize="20sp" />

                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
        <LinearLayout
            android:id="@+id/buttonLargeLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="right"
            android:orientation="horizontal"
            android:layout_marginEnd="0dp"
            android:layout_marginBottom="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/addButton"
                android:layout_width="54dp"
                android:layout_height="54dp"
                android:src="@drawable/btn_add"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                tools:ignore="MissingConstraints" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>