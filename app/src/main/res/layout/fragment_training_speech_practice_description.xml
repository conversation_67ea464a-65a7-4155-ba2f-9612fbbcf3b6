<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.training.TrainingSpeechPracticeDescriptionViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.training.TrainingSpeechPracticeDescriptionFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:text="お手本文章を読みながら適切な声色で\n話す練習をしましょう。"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <Button
                    android:id="@+id/startButton"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/round_rectangle_25dp_color_primary"
                    android:drawableEnd="@drawable/ic_arrow_next_white_16dp"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingStart="48dp"
                    android:paddingTop="0dp"
                    android:paddingEnd="16dp"
                    android:paddingBottom="0dp"
                    android:text="トレーニングを開始する"
                    android:textColor="@color/base_white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@null"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title" />

                <LinearLayout
                    android:id="@+id/noticelayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="23dp"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toBottomOf="@id/startButton">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/icon_hint_m" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="6dp"
                        android:text="話す際の注意点"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linearLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/round_rectangle_5dp_base_10"
                    android:orientation="vertical"
                    android:paddingStart="20dp"
                    android:paddingTop="30dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="24dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/noticelayout">

                    <LinearLayout
                        android:id="@+id/voicelayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start|center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toBottomOf="@id/startButton">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_check_main" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="声の大きさ"
                            android:textColor="@color/base_text"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/textView1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="22dp"
                        android:layout_marginTop="8dp"
                        android:text="普段よりも少し大きめを意識する"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintTop_toBottomOf="@id/voicelayout" />

                    <LinearLayout
                        android:id="@+id/howlayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:gravity="start|center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toBottomOf="@id/textView1">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_check_main" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="話し方"
                            android:textColor="@color/base_text"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/textView2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="22dp"
                        android:layout_marginTop="8dp"
                        android:text="口をしっかり動かし抑揚を意識して話す"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintTop_toBottomOf="@id/howlayout" />

                    <LinearLayout
                        android:id="@+id/tonelayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:gravity="start|center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toBottomOf="@id/textView2">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_check_main" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="声のトーン"
                            android:textColor="@color/base_text"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="22dp"
                        android:layout_marginTop="8dp"
                        android:text="普段より1トーン明るく話す"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintTop_toBottomOf="@id/tonelayout" />

                    <LinearLayout
                        android:id="@+id/speedlayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:gravity="start|center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintTop_toBottomOf="@id/textView3">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_check_main" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="話すスピード"
                            android:textColor="@color/base_text"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/textView4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="22dp"
                        android:layout_marginTop="8dp"
                        android:text="焦りすぎない。相手に届けることを意識する"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintTop_toBottomOf="@id/speedlayout" />
                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/playLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="32dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="40dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/linearLayout">

                    <TextView
                        android:id="@+id/textView5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="32dp"
                        android:text="お手本音声を聴く"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:id="@+id/buttonHalfLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="20dp"
                        android:layout_marginBottom="20dp"
                        android:gravity="center_horizontal"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView5">

                        <Button
                            android:id="@+id/maleButton"
                            android:layout_width="0dp"
                            android:layout_height="50dp"
                            android:layout_marginEnd="8dp"
                            android:layout_marginBottom="5dp"
                            android:layout_weight="1"
                            android:background="@drawable/round_rectangle_25dp_gray"
                            android:enabled="false"
                            android:gravity="center"
                            android:padding="0dp"
                            android:text="男性"
                            android:textColor="@color/base_white"
                            android:textSize="16sp"
                            app:backgroundTint="@null" />

                        <Button
                            android:id="@+id/femaleButton"
                            android:layout_width="0dp"
                            android:layout_height="50dp"
                            android:layout_marginStart="8dp"
                            android:layout_marginBottom="5dp"
                            android:layout_weight="1"
                            android:background="@drawable/round_rectangle_25dp_gray"
                            android:enabled="false"
                            android:gravity="center"
                            android:padding="0dp"
                            android:text="女性"
                            android:textColor="@color/base_white"
                            android:textSize="16sp"
                            app:backgroundTint="@null" />
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>