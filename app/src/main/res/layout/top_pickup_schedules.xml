<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scheduleListLayout"
    android:visibility="visible"
    android:layout_width="272dp"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center"
    android:layout_gravity="center">

        <ImageButton
            android:id="@+id/prevScheduleButton"
            android:layout_width="20dp"
            android:layout_weight="0"
            android:layout_height="50dp"
            app:srcCompat="@drawable/icon_triangle_g"
            android:scaleX="-1"
            android:background="@color/base_white"
            android:layout_marginLeft="0dp"
            android:gravity="center_vertical" />
    <ImageButton
        android:id="@+id/prevScheduleButtonOff"
        android:layout_width="20dp"
        android:layout_weight="0"
        android:layout_height="50dp"
        android:scaleX="-1"
        android:background="@color/base_white"
        android:layout_marginLeft="0dp"
        android:gravity="center_vertical" />

        <LinearLayout
            android:id="@+id/scheduleLayout"
            android:layout_width="232dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:layout_marginLeft="0dp"
            android:layout_marginRight="0dp"
            android:gravity="center"
            android:orientation="vertical"
            tools:ignore="MissingConstraints">

            <TextView
                android:id="@+id/dtTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="0dp"
                android:text="2022/12/15 PM13:00"
                android:textColor="@color/base_text"
                android:textStyle="bold"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/companyTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="0dp"
                android:singleLine="true"
                android:ellipsize="end"
                android:text="株式会社OOOOコンサルティング"
                android:textColor="@color/base_text"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/selectionStatusTextView"
                android:layout_width="183dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:layout_marginTop="7dp"
                android:background="@drawable/round_rectangle_5dp_color_primary_usui"
                android:singleLine="true"
                android:ellipsize="end"
                android:text="インターンシップ期間インタ…"
                android:textAlignment="center"
                android:gravity="center"
                android:textColor="@color/base_text"
                android:textSize="12sp" />

            <Button
                android:id="@+id/editScheduleButton"
                android:layout_width="175dp"
                android:layout_weight="1"
                android:layout_height="40dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/round_rectangle_25dp_color_primary"
                android:text="詳細を見る"
                android:textColor="@color/base_white"
                android:textStyle="bold"
                app:backgroundTint="@null"
                android:textSize="16sp"
                android:padding="0dp"
                android:stateListAnimator="@null"
                android:gravity="center" />

            <LinearLayout
            android:id="@+id/pagerLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="top|center_horizontal"
            android:orientation="horizontal"
            tools:ignore="NotSibling,Suspicious0dp">

                <ImageView
                    android:id="@+id/pagerOne"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet_active" />

                <ImageView
                    android:id="@+id/pagerTwo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet" />

                <ImageView
                    android:id="@+id/pagerThree"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet" />

                <ImageView
                    android:id="@+id/pagerFour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet" />

                <ImageView
                    android:id="@+id/pagerFive"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/slider_pagination_bullet" />
            </LinearLayout>
        </LinearLayout>

        <ImageButton
            android:id="@+id/nextScheduleButton"
            android:layout_width="20dp"
            android:layout_weight="0"
            android:layout_height="50dp"
            android:layout_marginRight="0dp"
            android:background="@color/base_white"
            app:srcCompat="@drawable/icon_triangle_g"
            android:gravity="center_vertical" />
        <ImageButton
            android:id="@+id/nextScheduleButtonOff"
            android:layout_width="20dp"
            android:layout_weight="0"
            android:layout_height="50dp"
            android:layout_marginRight="0dp"
            android:background="@color/base_white"
            android:gravity="center_vertical" />

</LinearLayout>