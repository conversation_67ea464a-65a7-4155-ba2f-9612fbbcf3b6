<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.mypage.InterviewScheduleDetailViewModel" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.mypage.InterviewScheduleDetailFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:layout_marginTop="0dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <LinearLayout
                android:id="@+id/innerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="left"
                android:gravity="left"
                android:layout_marginRight="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                tools:ignore="MissingConstraints">

                <TextView
                    android:id="@+id/selectionStatusTextView"
                    android:layout_width="match_parent"
                    android:layout_height="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="0dp"
                    android:background="@drawable/round_rectangle_5dp_color_primary_usui"
                    android:singleLine="true"
                    android:ellipsize="end"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp"
                    android:visibility="gone"
                    android:text=""
                    android:gravity="center"
                    android:textColor="@color/base_text"
                    android:textSize="12sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:singleLine="true"
                    android:text="予定日時"
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <TextView
                    android:id="@+id/dtTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:singleLine="true"
                    android:text=""
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="normal"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:singleLine="true"
                    android:text="企業名"
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <TextView
                    android:id="@+id/companyNameTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="normal"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:singleLine="true"
                    android:text="場所"
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <TextView
                    android:id="@+id/locationTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="normal"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:singleLine="true"
                    android:text="担当者"
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <TextView
                    android:id="@+id/staffTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="normal"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:singleLine="true"
                    android:text="担当者電話番号"
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <TextView
                    android:id="@+id/staffTelTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="normal"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:singleLine="true"
                    android:text="担当者メールアドレス"
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <TextView
                    android:id="@+id/staffMailaddressTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="normal"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:singleLine="true"
                    android:text="メモ"
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <TextView
                    android:id="@+id/memoTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textAlignment="textStart"
                    android:textColor="@color/base_text"
                    android:textStyle="normal"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:layout_marginTop="30dp"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginBottom="0dp">

                    <Button
                        android:id="@+id/editButton"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="50dp"
                        android:layout_marginStart="0dp"
                        android:layout_marginBottom="0dp"
                        android:background="@drawable/round_rectangle_25dp_color_primary"
                        android:text="編集する"
                        android:textColor="@color/base_white"
                        android:textStyle="bold"
                        app:backgroundTint="@null"
                        android:textSize="16sp"
                        android:padding="0dp"
                        android:stateListAnimator="@null"
                        android:gravity="center" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/trashLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginBottom="100dp">

                    <ImageView
                        android:id="@+id/iconTrash"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/icon_trash"
                        android:layout_marginLeft="20dp"
                        tools:ignore="MissingConstraints" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="20dp"
                        android:text="この予定を削除する"
                        android:textAlignment="textStart"
                        android:textColor="@color/gray_icon"
                        android:textStyle="bold"
                        android:textSize="14sp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>