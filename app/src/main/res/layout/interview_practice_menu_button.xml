<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/base_white"
    android:backgroundTint="@color/base_white">

    <View
        android:id="@+id/spacer"
        android:layout_width="match_parent"
        android:layout_height="7dp"
        android:background="@color/white_transparent"
        android:backgroundTint="@color/white_transparent" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:cardElevation="7dp"
        app:layout_constraintTop_toBottomOf="@id/spacer">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/round_rectangle_5dp_base_10">

            <ImageView
                android:id="@+id/billing_image"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/icon_billing_mark"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/checked_image"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/icon_mogimen_checked"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/contents_image"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/icon_mogimen_contents"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/voice_image"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/icon_voice"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="64dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="12dp"
                android:layout_marginBottom="8dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="bottom"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/no"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:text="01"
                        android:textColor="@color/base_text"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:visibility="visible" />

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="タイトル"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/kind_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/round_rectangle_5dp_gray"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingStart="8dp"
                        android:paddingTop="3dp"
                        android:paddingEnd="8dp"
                        android:paddingBottom="4dp"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/kind_label_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="練習指標 : "
                            android:textColor="@color/base_text"
                            android:textSize="12sp"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/kind_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="真剣・熱意"
                            android:textColor="@color/base_text"
                            android:textSize="12sp"
                            android:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/target_time_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="目安時間 : "
                        android:textColor="@color/gray_icon"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/target_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1分"
                        android:textColor="@color/gray_icon"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

    <ImageView
        android:id="@+id/next_image"
        android:layout_width="18.351dp"
        android:layout_height="11.172dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="5dp"
        android:src="@drawable/icon_triangle_b"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card" />
</androidx.constraintlayout.widget.ConstraintLayout>