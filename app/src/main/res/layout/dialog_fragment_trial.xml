<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"
        android:paddingTop="0dp"
        android:paddingBottom="0dp"
        android:orientation="vertical"
        android:background="@drawable/round_rectangle_5dp_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/titleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="56dp"
            android:layout_marginEnd="20dp"
            android:text="Premiumプランの全ての機能を\n体験しよう"
            android:textAlignment="center"
            android:textColor="@color/base_text"
            android:textStyle="bold"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/messageTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="20dp"
            android:text="Premiumプランをご利用の際にはログイン\nまたは会員登録が必要です。"
            android:textAlignment="center"
            android:textColor="@color/base_text"
            android:textSize="14sp" />

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/round_rectangle_5dp_base_10"
            android:orientation="vertical"
            android:paddingStart="20dp"
            android:paddingTop="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:id="@+id/freelayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start|center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/startButton">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_check_main" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:text="初回登録なら1ヶ月無料"
                    android:textColor="@color/base_text"
                    android:textSize="13sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/locklayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="start|center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/freelayout">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_check_main" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="ロックされた機能を解除"
                    android:textColor="@color/base_text"
                    android:textSize="13sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="22dp"
                android:layout_marginTop="4dp"
                android:text="無料版より多くのコンテンツが利用可能。"
                android:textColor="@color/base_text"
                android:textSize="12sp"
                app:layout_constraintTop_toBottomOf="@id/locklayout" />

            <LinearLayout
                android:id="@+id/recordlayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="start|center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/textView2">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_check_main" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="詳細な採点結果で振り返る"
                    android:textColor="@color/base_text"
                    android:textSize="13sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/textView3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="22dp"
                android:layout_marginTop="8dp"
                android:text="練習した動画や音声をAIが分析。詳細な採点結果でさらに自己分析を深掘り。"
                android:textColor="@color/base_text"
                android:textSize="12sp"
                app:layout_constraintTop_toBottomOf="@id/recordlayout" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/checkLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <CheckBox
                android:id="@+id/checkbox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white"
                android:checked="false"
                android:enabled="true" />

            <TextView
                android:id="@+id/textView8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="次回以降表示しない"
                android:textColor="@color/base_text"
                android:textSize="14sp" />
        </LinearLayout>

        <Button
            android:id="@+id/closeButton"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="24dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:drawableEnd="@drawable/ic_arrow_next_white_16dp"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:paddingStart="48dp"
            android:paddingTop="0dp"
            android:paddingEnd="14dp"
            android:paddingBottom="0dp"
            android:stateListAnimator="@null"
            android:text="ログイン/新規会員登録"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp" />
        <View
            android:id="@+id/spaceView"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            tools:ignore="MissingConstraints" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>