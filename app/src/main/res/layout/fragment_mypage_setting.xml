<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.mypage.MyPageFragment">

    <include
        android:id="@+id/backHeaderBar"
        layout="@layout/header_bar_with_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/gray_bg01"
        android:backgroundTint="@color/gray_bg01"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/backHeaderBar">



        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/gray_bg01"
            android:backgroundTint="@color/gray_bg01"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="28dp"
                android:layout_marginEnd="28dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/newGraduatesMode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/round_rectangle_3dp_color_primary"
                    android:text="研修中モード"
                    android:textColor="@color/base_white"
                    android:textSize="14dp"
                    app:backgroundTint="@null" />

                <Button
                    android:id="@+id/careerChangeMode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/round_rectangle_3dp_white_and_gray_line"
                    android:text="転職モード"
                    android:textColor="#382F2D"
                    android:textSize="14dp"
                    app:backgroundTint="@null" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp"
                android:background="@color/base_white"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/mockInterviewerBtn"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/mockInterviewerText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:text="模擬面接官設定"
                        android:textColor="#382F2D"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/currentMockInterviewer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="12dp"
                        android:text=""
                        android:textColor="#AAAAAA"
                        android:textSize="12dp" />

                    <ImageView
                        android:id="@+id/imageView11"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>

                <!--<LinearLayout
                    android:id="@+id/campaignCodeBtn"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/campaignCodeText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:text="キャンペーン・クーポンコード"
                        android:textColor="#382F2D"
                        android:textSize="14dp" />

                    <ImageView
                        android:id="@+id/imageView12"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>-->

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/helpBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="14dp"
                        android:textColor="#382F2D"
                        android:layout_gravity="center_vertical"
                        android:text="ヘルプ" />

                    <ImageView
                        android:id="@+id/imageView13"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/passwordChangeBtn"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textView55"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="14dp"
                        android:textColor="#382F2D"
                        android:layout_gravity="center_vertical"
                        android:text="パスワード変更" />

                    <ImageView
                        android:id="@+id/imageView14"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/emailChangeBtn"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textView56"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="14dp"
                        android:textColor="#382F2D"
                        android:layout_gravity="center_vertical"
                        android:text="メールアドレス変更" />

                    <ImageView
                        android:id="@+id/imageView15"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>

                <!--<LinearLayout
                    android:id="@+id/premiumPlanBtn"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textView57"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:text="Premiumプランを管理"
                        android:textColor="#382F2D"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/planName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="12dp"
                        android:text="未登録"
                        android:textColor="#AAAAAA"
                        android:textSize="12dp" />

                    <ImageView
                        android:id="@+id/imageView16"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>-->

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp"
                android:background="@color/base_white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/termsBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:text="利用規約"
                        android:textColor="#382F2D"
                        android:textSize="14dp" />

                    <ImageView
                        android:id="@+id/imageView17"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/privacyPolicyBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:text="プライバシーポリシー"
                        android:textColor="#382F2D"
                        android:textSize="14dp" />

                    <ImageView
                        android:id="@+id/imageView18"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>

                <!--<LinearLayout
                    android:id="@+id/appReviewBtn"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textView61"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:text="アプリを評価"
                        android:textColor="#382F2D"
                        android:textSize="14dp" />

                    <ImageView
                        android:id="@+id/imageView19"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>-->

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp"
                android:background="@color/base_white"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/logoutBtn"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/border_bottom_gray_1dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textView62"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:text="ログアウト"
                        android:textColor="#382F2D"
                        android:textSize="14dp" />

                    <ImageView
                        android:id="@+id/imageView20"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/deleteBtn"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/deleteText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:text="アカウント削除"
                        android:textColor="#F08000"
                        android:textSize="14dp" />

                    <ImageView
                        android:id="@+id/imageView21"
                        android:layout_width="6.83dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_menu_arrow" />
                </LinearLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/textView64"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="32dp"
                android:gravity="end"
                android:text="アプリバージョン　1.0.0"
                android:textColor="#999999"
                android:textSize="12dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>