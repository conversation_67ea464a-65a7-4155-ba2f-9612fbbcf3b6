<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:layout_marginTop="0dp"
            android:paddingBottom="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <LinearLayout
                android:id="@+id/innerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="left"
                android:gravity="left"
                android:layout_marginRight="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                tools:ignore="MissingConstraints">

                <ImageView
                    android:id="@+id/imageView2"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_marginTop="20dp"
                    app:srcCompat="@drawable/img_logo" />

                <TextView
                    android:id="@+id/textView21"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="さらに詳細な採点結果の閲覧や\nメンバーだけのコンテンツが利用可能"
                    android:textAlignment="center"
                    android:textColor="#382F2D"
                    android:textSize="14dp" />

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginTop="40dp">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/textView22"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="40dp"
                            android:layout_marginTop="24dp"
                            android:layout_marginEnd="40dp"
                            android:text="初回登録\n1週間無料トライアル"
                            android:textAlignment="center"
                            android:textColor="#382F2D"
                            android:textSize="20dp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/textView23"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="8dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="24dp"
                            android:textSize="12dp"
                            android:textColor="#382F2D"
                            android:text="1週間無料トライアル経過後、選択されたプランの金額が自動で定額請求されます。\nPremiumプランはいつでもキャンセル可能です。\nすでに1週間無料トライアルを使用された方は無料トライアル対象外になりますのでご注意ください。"
                            android:textAlignment="center" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:layout_marginBottom="16dp"
                    android:layout_gravity="center_horizontal"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/imageView3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:srcCompat="@drawable/icon_premium_member" />

                    <TextView
                        android:id="@+id/textView25"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginTop="-3dp"
                        android:layout_weight="1"
                        android:textColor="#382F2D"
                        android:textSize="20dp"
                        android:textStyle="bold"
                        android:text="Premium限定の機能" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="#F4D8C7"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="20dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/imageView4"
                            android:layout_width="52dp"
                            android:layout_height="52dp"
                            android:layout_marginEnd="8dp"
                            android:scaleType="fitCenter"
                            app:srcCompat="@drawable/icon_premium_content1" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView27"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="8dp"
                                android:text="詳細な採点結果で振り返る"
                                android:textColor="#382F2D"
                                android:textSize="16dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/textView28"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="無料版では閲覧できなかった詳細な採点結果でさらに自己分析を深掘り。"
                                android:textColor="#382F2D"
                                android:textSize="12dp" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="20dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/imageView5"
                            android:layout_width="52dp"
                            android:layout_height="52dp"
                            android:layout_marginEnd="8dp"
                            android:scaleType="fitCenter"
                            app:srcCompat="@drawable/icon_premium_content2" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView29"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="8dp"
                                android:text="選考動画ファイル分析が可能"
                                android:textColor="#382F2D"
                                android:textSize="16dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/textView30"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="予め端末で撮影した動画ファイルで表情や音声の分析が可能に。"
                                android:textColor="#382F2D"
                                android:textSize="12dp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="20dp"
                        android:layout_marginBottom="24dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/imageView6"
                            android:layout_width="52dp"
                            android:layout_height="52dp"
                            android:layout_marginEnd="8dp"
                            android:scaleType="fitCenter"
                            app:srcCompat="@drawable/icon_premium_content3" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView31"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="8dp"
                                android:text="音声分析で話すトレーニング"
                                android:textColor="#382F2D"
                                android:textSize="16dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/textView32"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="発話した音声を分析し適切な声色で話すためのトレーニングが可能に。"
                                android:textColor="#382F2D"
                                android:textSize="12dp" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="32dp"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/imageView7"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:srcCompat="@drawable/icon_premium_price" />

                    <TextView
                        android:id="@+id/textView33"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="-3dp"
                        android:layout_marginStart="4dp"
                        android:layout_weight="1"
                        android:text="料金と解約について"
                        android:textColor="#382F2D"
                        android:textSize="20dp"
                        android:textStyle="bold" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:background="#F4D8C7"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="20dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/imageView8"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginEnd="8dp"
                            android:scaleType="fitCenter"
                            app:srcCompat="@drawable/icon_check_main" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView34"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="8dp"
                                android:text="初回登録なら1週間無料"
                                android:textColor="#382F2D"
                                android:textSize="16dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/textView35"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="1週間無料トライアル終了日時の24時間前までに解約すれば、以降料金が請求されることはありません。"
                                android:textColor="#382F2D"
                                android:textSize="12dp" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="20dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/imageView9"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginEnd="8dp"
                            android:scaleType="fitCenter"
                            app:srcCompat="@drawable/icon_check_main" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView36"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="8dp"
                                android:text="支払い間隔はプランによって選べる"
                                android:textColor="#382F2D"
                                android:textSize="16dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/textView37"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="定額請求される間隔を1ヶ月 / 3ヶ月 / 6ヶ月 / 12ヶ月のプランから選択できます。"
                                android:textColor="#382F2D"
                                android:textSize="12dp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="20dp"
                        android:layout_marginBottom="24dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/imageView10"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginEnd="8dp"
                            android:scaleType="fitCenter"
                            app:srcCompat="@drawable/icon_check_main" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView38"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="8dp"
                                android:text="解約方法はとても簡単"
                                android:textColor="#382F2D"
                                android:textSize="16dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/textView39"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Google Play ストアからいつでも簡単に解約できます。"
                                android:textColor="#382F2D"
                                android:textSize="12dp" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/textView40"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginTop="12dp"
                    android:textColor="#766C6A"
                    android:textSize="12dp"
                    android:text="購入後のお支払いはGoogle アカウントに請求されます。お支払いが完了した時点でPremium機能が開始されます。1週間無料トライアルは初めて登録される方が対象です。1週間無料トライアル終了後は、更新日時の24時間前までに解約しない限り、選択したプラン価格での請求が自動更新されます。解約手続きはGoogle Play ストアの設定でできます。 登録した時点で利用規約とプライバシーポリシーに同意したことになります。" />

                <TextView
                    android:id="@+id/terms"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="20dp"
                    android:textSize="14dp"
                    android:textColor="#F08000"
                    android:text="利用規約" />

                <TextView
                    android:id="@+id/privacyPolicy"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="20dp"
                    android:textSize="14dp"
                    android:textColor="#F08000"
                    android:text="プライバシーポリシー" />

                <Button
                    android:id="@+id/premium"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="40dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="40dp"
                    android:background="@drawable/round_rectangle_25dp_color_primary"
                    android:gravity="center"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text=""
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textAllCaps="false"
                    app:backgroundTint="@null"
                    app:elevation="0dp"/>


            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>