<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.createnewuser.CreateNewUserInputViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.createnewuser.CreateNewUserInputFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title_and_close_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white">

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="メールアドレス"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <include
                    android:id="@+id/requireText2"
                    layout="@layout/require_text"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="0dp"
                    app:layout_constraintStart_toEndOf="@id/textView2"
                    app:layout_constraintTop_toTopOf="@id/textView2" />

                <include
                    android:id="@+id/email"
                    layout="@layout/input_text_email"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView2" />

                <include
                    android:id="@+id/email_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/email"
                    app:layout_constraintEnd_toEndOf="@id/email"
                    app:layout_constraintTop_toBottomOf="@id/email" />

                <TextView
                    android:id="@+id/textView3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:text="確認のためもう一度入力してください"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/email_validation" />

                <include
                    android:id="@+id/confirm_email"
                    layout="@layout/input_text_email"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView3" />

                <include
                    android:id="@+id/confirm_email_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/confirm_email"
                    app:layout_constraintEnd_toEndOf="@id/confirm_email"
                    app:layout_constraintTop_toBottomOf="@id/confirm_email" />

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="パスワード"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/confirm_email_validation" />

                <include
                    android:id="@+id/requireText3"
                    layout="@layout/require_text"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="0dp"
                    app:layout_constraintStart_toEndOf="@id/textView4"
                    app:layout_constraintTop_toTopOf="@id/textView4" />

                <TextView
                    android:id="@+id/textView5"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    android:text="半角英大文字、半角英小文字、記号、半角数字をそれぞれ１文字以上含む、８文字以上で入力してください。"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/requireText3" />

                <include
                    android:id="@+id/password"
                    layout="@layout/input_text_password"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView5" />

                <include
                    android:id="@+id/password_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/password"
                    app:layout_constraintEnd_toEndOf="@id/password"
                    app:layout_constraintTop_toBottomOf="@id/password" />

                <TextView
                    android:id="@+id/textView6"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:text="確認のためもう一度入力してください"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/password_validation" />

                <include
                    android:id="@+id/confirm_password"
                    layout="@layout/input_text_password"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView6" />

                <include
                    android:id="@+id/confirm_password_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/confirm_password"
                    app:layout_constraintEnd_toEndOf="@id/confirm_password"
                    app:layout_constraintTop_toBottomOf="@id/confirm_password" />

                <TextView
                    android:id="@+id/textView9"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="Choose Mode"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/confirm_password_validation" />

                <include
                    android:id="@+id/requireText6"
                    layout="@layout/require_text"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="0dp"
                    app:layout_constraintStart_toEndOf="@id/textView9"
                    app:layout_constraintTop_toTopOf="@id/textView9" />

                <LinearLayout
                    android:id="@+id/modeLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    android:gravity="top|center_horizontal"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/requireText6">

                    <Button
                        android:id="@+id/willGraduationButton"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="40dp"
                        android:background="@drawable/round_rectangle_5dp_white_with_gray_line"
                        android:gravity="center"
                        android:onClick="@{() -> viewModel.onWillGraduationButtonClick()}"
                        android:padding="0dp"
                        android:text="卒業予定"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        app:backgroundTint="@null" />

                    <androidx.legacy.widget.Space
                        android:layout_width="4dp"
                        android:layout_height="wrap_content"/>

                    <Button
                        android:id="@+id/graduationButton"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="40dp"
                        android:background="@drawable/round_rectangle_5dp_white_with_gray_line"
                        android:gravity="center"
                        android:onClick="@{() -> viewModel.onGraduationButtonClick()}"
                        android:padding="0dp"
                        android:text="既卒"
                        android:textColor="@color/base_text"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        app:backgroundTint="@null" />
                </LinearLayout>

                <include
                    android:id="@+id/mode_validation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/modeLayout"
                    app:layout_constraintEnd_toEndOf="@id/modeLayout"
                    app:layout_constraintTop_toBottomOf="@id/modeLayout" />

                <Button
                    android:id="@+id/actionButton"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="32dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/round_rectangle_25dp_color_primary"
                    android:gravity="center"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text="入力内容を確認"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@null"
                    app:elevation="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mode_validation" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>