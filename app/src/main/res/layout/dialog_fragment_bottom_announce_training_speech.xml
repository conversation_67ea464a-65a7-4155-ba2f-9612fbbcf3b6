<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_marginBottom="0dp"
        android:background="@drawable/round_rectangle_5dp_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/closeButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:background="@color/base_white"
            android:includeFontPadding="false"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/bottom_announce_modal_button"
            android:textColor="@color/color_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <jp.co.cac.ope.shokken.lab.ui.customview.SeekBarView
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="15dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="48dp"
            android:layout_marginEnd="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/currentTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="00:00"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@id/seekBar"
            app:layout_constraintTop_toBottomOf="@id/seekBar" />

        <TextView
            android:id="@+id/contentTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="00:00"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@id/seekBar"
            app:layout_constraintTop_toBottomOf="@id/seekBar" />

        <LinearLayout
            android:id="@+id/controlLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="37dp"
            android:layout_marginBottom="40dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/seekBar">

            <ImageButton
                android:id="@+id/backward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@color/white_transparent"
                android:src="@drawable/icon_10backward_b"
                android:visibility="gone" />

            <ImageButton
                android:id="@+id/play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:background="@color/white_transparent"
                android:src="@drawable/icon_play_b" />

            <ImageButton
                android:id="@+id/pause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:background="@color/white_transparent"
                android:src="@drawable/icon_pause_b"
                android:visibility="gone" />

            <ImageButton
                android:id="@+id/replay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:background="@color/white_transparent"
                android:src="@drawable/icon_replay_b"
                android:visibility="gone" />

            <ImageButton
                android:id="@+id/forward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@color/white_transparent"
                android:src="@drawable/icon_10forward_b"
                android:visibility="gone" />
        </LinearLayout>

        <View
            android:id="@+id/spacer"
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:layout_marginStart="0dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="0dp"
            android:layout_marginBottom="0dp"
            android:background="@color/base_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/controlLayout" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>