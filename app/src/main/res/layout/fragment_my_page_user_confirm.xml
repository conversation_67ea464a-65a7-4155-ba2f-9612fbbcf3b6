<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.mypage.MyPageUserConfirmViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.mypage.MyPageUserConfirmFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:layout_marginTop="0dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <LinearLayout
                android:id="@+id/accordionContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="28dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="@color/base_text"
                        android:layout_weight="1"
                        android:text="プロフィール"/>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/profileEditBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <ImageView
                            android:id="@+id/iconEdit"
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_marginTop="3dp"
                            android:src="@drawable/icon_edit"
                            app:layout_constraintStart_toStartOf="parent"
                            tools:ignore="MissingConstraints" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="7dp"
                            android:textSize="14sp"
                            android:textColor="@color/color_primary"
                            android:text="編集"/>
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linearLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    android:background="@drawable/round_rectangle_5dp_base_10"
                    android:orientation="vertical"
                    android:paddingStart="20dp"
                    android:paddingTop="24dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="24dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView2">

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="ユーザ名"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/username"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="12dp"
                        android:text="-"
                        android:textColor="@color/base_text"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textView6"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:text="生年月日"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/birthday"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="12dp"
                        android:text="-"
                        android:textColor="@color/base_text"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textView7"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:text="性別"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/gender"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="12dp"
                        android:text="-"
                        android:textColor="@color/base_text"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textView8"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:text="卒業予定年月または既卒年月"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/graduationDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="12dp"
                        android:text="-"
                        android:textColor="@color/base_text"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textView9"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:text="学校名"
                        android:textColor="@color/base_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/schoolName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="12dp"
                        android:text="-"
                        android:textColor="@color/base_text"
                        android:textSize="14sp" />
                </LinearLayout>

            </LinearLayout>

        </ScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>