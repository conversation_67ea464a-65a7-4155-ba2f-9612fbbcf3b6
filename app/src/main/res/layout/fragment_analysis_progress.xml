<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.analysis.AnalysisProgressViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.analysis.AnalysisProgressFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:gravity="center"
            android:text="分析採点中…"
            android:textColor="@color/base_text"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar" />

        <TextView
            android:id="@+id/description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="28dp"
            android:gravity="center"
            android:text="採点が終わるまで少々お待ちください。\n中止ボタンや閉じるボタンで\nアプリを閉じると分析は中断されます。"
            android:textColor="@color/base_text"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title" />

        <ImageView
            android:id="@+id/progressImage"
            android:layout_width="68dp"
            android:layout_height="68dp"
            android:layout_marginTop="90dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/description" />

        <jp.co.cac.ope.shokken.lab.ui.customview.AnalysisProgressBarView
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:layout_marginStart="30dp"
            android:layout_marginTop="38dp"
            android:layout_marginEnd="30dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progressImage" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="28dp"
            android:gravity="center"
            android:text="このページから離れると分析が中断されます。\n分析が完了するまでお待ちください。"
            android:textColor="@color/base_text"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progressBar" />

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
            android:gravity="center"
            android:padding="0dp"
            android:text="中止"
            android:textColor="@color/base_text"
            android:textSize="16sp"
            app:backgroundTint="@null"
            app:layout_constraintBottom_toTopOf="@id/affectiva_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/affectiva_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:src="@drawable/img_affectiva_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>