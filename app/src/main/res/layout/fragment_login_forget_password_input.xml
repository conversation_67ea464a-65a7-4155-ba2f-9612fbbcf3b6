<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.login.LoginForgetPasswordInputViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.login.LoginForgetPasswordInputFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/login_forget_password_input_title_margin_top"
            android:text="@string/login_forget_password_input_title"
            android:textSize="20sp"
            android:textColor="@color/base_text"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar" />

        <TextView
            android:id="@+id/textView7"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_forget_password_input_top_description_margin_top"
            android:layout_marginEnd="28dp"
            android:textAlignment="center"
            android:textSize="14sp"
            android:textColor="@color/base_text"
            android:text="@string/login_forget_password_input_top_description"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView4" />

        <View
            android:id="@+id/view1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_forget_password_input_top_line_margin_top"
            android:layout_marginEnd="28dp"
            android:background="@color/gray_border01"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView7" />

        <TextView
            android:id="@+id/textView8"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_forget_password_input_description01_margin_top"
            android:layout_marginEnd="28dp"
            android:text="@string/login_forget_password_input_description01"
            android:textColor="@color/base_text"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view1" />

        <TextView
            android:id="@+id/textView9"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_forget_password_input_description02_margin_top"
            android:layout_marginEnd="28dp"
            android:text="@string/login_forget_password_input_description02"
            android:textColor="@color/base_text"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView8" />

        <TextView
            android:id="@+id/textView10"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_forget_password_input_email_margin_top"
            android:text="@string/login_input_email"
            android:textColor="@color/base_text"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView9" />

        <include
            android:id="@+id/requireText"
            layout="@layout/require_text"
            android:layout_width="34dp"
            android:layout_height="18dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="@dimen/login_forget_password_input_email_require_margin_top"
            app:layout_constraintStart_toEndOf="@id/textView10"
            app:layout_constraintTop_toBottomOf="@id/textView9" />

        <include
            android:id="@+id/email"
            layout="@layout/input_text_email"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/login_forget_password_input_email_inputarea_margin_top"
            android:layout_marginEnd="28dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView10" />

        <Button
            android:id="@+id/button3"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="5dp"
            android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
            android:onClick="@{() -> viewModel.onSendConfirmButtonClick()}"
            android:text="@string/login_forget_password_input_send_confirm"
            android:textColor="@color/base_text"
            app:backgroundTint="@null"
            android:textSize="16sp"
            android:padding="0dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
