<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.tutorial.Tutorial2ndViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.tutorial.TutorialTopFragment">

        <ImageView
            android:id="@+id/keyVisualView"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="@dimen/tutorial_icon_margin_top"
            android:src="@drawable/icon_tutorial1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/tutorial_title_margin_top"
            android:text="模擬面接練習"
            android:textColor="@color/base_text"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/keyVisualView" />

        <TextView
            android:id="@+id/descriptionTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="45dp"
            android:layout_marginTop="@dimen/tutorial_other_description_margin_top"
            android:layout_marginEnd="45dp"
            android:text="質問内容に合わせて表情や話す速度を意識しながら仮想面接官と面接練習。最初は台本を見ながら練習をしてみよう！"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleView" />

        <View
            android:id="@+id/bottomView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tutorial_other_bottom_height"
            android:layout_marginTop="@dimen/tutorial_other_bottom_not_warning_margin_top"
            android:background="@color/base_tutorial_bg01"
            android:backgroundTint="@color/base_tutorial_bg01"
            app:layout_constraintTop_toBottomOf="@id/descriptionTextView" />

        <ImageView
            android:id="@+id/displayView"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/tutorial_other_display_margin_top"
            android:layout_marginBottom="0dp"
            android:src="@drawable/img_android_tutorial1"
            app:layout_constraintTop_toTopOf="@id/bottomView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/bottomView" />


        <LinearLayout
            android:id="@+id/displayDescriptionView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="54dp"
            android:layout_marginBottom="@dimen/tutorial_other_display_description_margin_bottom"
            android:layout_marginEnd="54dp"
            android:gravity="top|center_vertical"
            android:orientation="vertical"
            android:background="@drawable/round_rectangle_10dp_gray"
            app:layout_constraintBottom_toBottomOf="@id/bottomView">

        <TextView
            android:id="@+id/displayDescriptionTitleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="模擬面接"
            android:textColor="@color/base_white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="@+id/displayDescriptionView"
            app:layout_constraintEnd_toEndOf="@id/displayDescriptionView"
            app:layout_constraintStart_toStartOf="@id/displayDescriptionView" />

        <TextView
            android:id="@+id/displayDescriptionTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="表情指標は「笑顔」「真剣熱意」から選択。面接官の話を聞く練習も選択可能。"
            android:textColor="@color/base_white"
            android:textSize="12sp"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/displayDescriptionTitleTextView"
            app:layout_constraintEnd_toEndOf="@id/displayDescriptionView"
            app:layout_constraintStart_toStartOf="@id/displayDescriptionView" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/pagerLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/tutorial_pager_margin_top"
            android:gravity="top|center_horizontal"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/bottomView">

            <ImageView
                android:id="@+id/pagerOne"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet" />

            <ImageView
                android:id="@+id/pagerTwo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet_active"
                app:layout_constraintStart_toEndOf="@id/pagerOne" />

            <ImageView
                android:id="@+id/pagerThree"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerTwo" />

            <ImageView
                android:id="@+id/pagerFour"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerThree" />

            <ImageView
                android:id="@+id/pagerFive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerFour" />

            <ImageView
                android:id="@+id/pagerSix"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerFive" />
        </LinearLayout>

        <Button
            android:id="@+id/skipButton"
            android:layout_width="72dp"
            android:layout_height="32dp"
            android:layout_marginTop="@dimen/tutorial_skip_margin_top"
            android:layout_marginEnd="@dimen/tutorial_skip_margin_end"
            android:background="@drawable/round_rectangle_16dp_white_and_color_primary_line"
            android:onClick="@{() -> viewModel.onSkipButtonClick()}"
            android:stateListAnimator="@null"
            android:text="@string/tutorial_skip"
            android:padding="0dp"
            android:gravity="center"
            android:textColor="@color/color_primary"
            android:textSize="14sp"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>