<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.home.HomeFragment">

    <include
        android:id="@+id/headerBar"
        layout="@layout/header_bar_home"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerBar">

        <LinearLayout
            android:id="@+id/scrollContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="24dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingEnd="46dp"
                    android:text="OPE職見Lab"
                    android:textAlignment="gravity"
                    android:textColor="@android:color/black"
                    android:textSize="22sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageButton
                        android:id="@+id/helpButton"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/btn_help" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ヘルプ"
                        android:textColor="@color/base_text"
                        android:textSize="14sp" />
                </LinearLayout>
            </FrameLayout>

            <include
                android:id="@+id/topBtns"
                layout="@layout/top_btns"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="24dp" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/noInterviewSessionLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="30dp"
                android:visibility="gone"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/gray_grayout"
                app:strokeWidth="1dp"
                tools:visibility="visible">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="26dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="No interview session yet.\nReady to get started?"
                        android:textAlignment="gravity"
                        android:textColor="@android:color/black"
                        android:textSize="16sp" />
                </FrameLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="30dp"
                android:background="@drawable/tab_background"
                app:tabBackground="@drawable/tab_selector_background"
                app:tabGravity="fill"
                app:tabIndicatorHeight="0dp"
                app:tabMode="fixed"
                app:tabSelectedTextColor="@color/base_white"
                app:tabTextAppearance="@style/TabTextAppearance"
                app:tabTextColor="@color/base_text" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="20dp" />

            <!--<TextView
                android:id="@+id/textViewSchedule"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="直近の予定"
                android:textStyle="bold"
                android:textColor="@color/base_text"
                android:textSize="16sp"
                android:layout_marginTop="32dp" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/item_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:layout_marginRight="24dp"
                android:backgroundTint="@color/base_white"
                tools:ignore="MissingConstraints"
                app:cardCornerRadius="5dp"
                app:cardElevation="2dp"
                app:strokeColor="#382F2D26"
                android:layout_marginTop="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center"
                    android:padding="20dp">

                    <TextView
                        android:id="@+id/notFoundPickupSchedules"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="登録がありません"
                        android:visibility="visible"
                        android:layout_gravity="center"
                        android:layout_marginBottom="16dp"
                        android:textColor="#999999"
                        android:textStyle="bold"
                        android:textSize="20sp" />

                    <include
                        android:id="@+id/pickupSchedules"
                        layout="@layout/top_pickup_schedules"
                        android:visibility="gone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginBottom="16dp" />

                    <View
                        android:layout_width="272dp"
                        android:layout_height="1dp"
                        android:layout_marginTop="0dp"
                        android:layout_gravity="center"
                        android:layout_marginBottom="16dp"
                        android:background="#EEEEEE" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="0dp"
                        android:layout_gravity="center">

                        <Button
                            android:id="@+id/newScheduleButton"
                            style="@style/Widget.MaterialComponents.Button.Icon"
                            app:icon="@drawable/icon_add_circle"
                            app:iconTint="@color/base_text"
                            app:iconGravity="textStart"
                            app:iconSize="15dp"
                            app:iconPadding="4dp"
                            android:layout_width="132dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="4dp"
                            android:layout_marginBottom="0dp"
                            android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
                            android:text="新規追加"
                            android:textColor="@color/base_text"
                            app:backgroundTint="@null"
                            android:textSize="16sp"
                            android:padding="0dp"
                            android:stateListAnimator="@null"
                            android:gravity="center" />

                        <Button
                            android:id="@+id/allScheduleButton"
                            android:layout_width="132dp"
                            android:layout_height="40dp"
                            android:layout_marginStart="4dp"
                            android:layout_marginBottom="0dp"
                            android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
                            android:text="すべて見る"
                            android:textColor="@color/base_text"
                            app:backgroundTint="@null"
                            android:textSize="16sp"
                            android:padding="0dp"
                            android:stateListAnimator="@null"
                            android:gravity="center" />
                    </LinearLayout>
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>-->

            <!--<LinearLayout
                android:id="@+id/adListContainer"
                android:layout_gravity="center_horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                android:orientation="vertical" />

            <include
                android:id="@+id/emptyReason"
                layout="@layout/part_top_advertisement_none_item_view"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="12dp" />

            <View
                android:id="@+id/noneAdListContainer"
                android:layout_width="match_parent"
                android:visibility="gone"
                android:layout_height="8dp"/>-->

            <!--<LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_gravity="center"
                android:layout_marginTop="12dp"
                android:background="@color/base_20"
                android:paddingLeft="0dp"
                android:paddingRight="0dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:text="新着コラム"
                    android:textStyle="bold"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:layout_marginTop="32dp" />
                <TextView
                    android:id="@+id/notFoundColumns"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="登録がありません"
                    android:visibility="visible"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:textColor="#999999"
                    android:textStyle="bold"
                    android:textSize="20sp" />
                <LinearLayout
                    android:id="@+id/columnListContainer"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    android:orientation="vertical" />
                <Button
                    android:id="@+id/moreColumnButton"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="132dp"
                    android:layout_height="40dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="80dp"
                    android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
                    android:text="もっと見る"
                    android:textColor="@color/base_text"
                    app:backgroundTint="@null"
                    android:textSize="14sp"
                    android:padding="0dp"
                    android:visibility="gone"
                    android:stateListAnimator="@null"
                    android:gravity="center" />

            </LinearLayout>-->

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>