<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.createnewuser.CreateNewUserInputViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.createnewuser.CreateNewUserInputFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="82dp"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginEnd="28dp"
                    android:layout_marginTop="32dp"
                    android:text="メールアドレス変更"
                    android:textColor="@color/base_text"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textAlignment="center"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginEnd="28dp"
                    android:layout_marginTop="24dp"
                    android:textAlignment="center"
                    android:text="新しいメールアドレスを入力してください。"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title" />

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="40dp"
                    android:text="現在のメールアドレス"
                    android:textColor="@color/base_text"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/description" />

                <TextView
                    android:id="@+id/currentEmail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="12dp"
                    android:text="<EMAIL>"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView1" />

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="20dp"
                    android:text="新しいメールアドレス"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/currentEmail" />

                <include
                    android:id="@+id/requireText3"
                    layout="@layout/require_text"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="0dp"
                    app:layout_constraintStart_toEndOf="@id/textView4"
                    app:layout_constraintTop_toTopOf="@id/textView4" />

                <include
                    android:id="@+id/newEmail"
                    layout="@layout/input_text"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/requireText3" />

                <include
                    android:id="@+id/newEmailValidation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/newEmail"
                    app:layout_constraintEnd_toEndOf="@id/newEmail"
                    app:layout_constraintTop_toBottomOf="@id/newEmail" />

                <TextView
                    android:id="@+id/textView6"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:text="確認のためもう一度入力してください"
                    android:textColor="@color/base_text"
                    android:textSize="13sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/newEmailValidation" />

                <include
                    android:id="@+id/confirmEmail"
                    layout="@layout/input_text"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView6" />

                <include
                    android:id="@+id/confirmEmailValidation"
                    layout="@layout/validation_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginTop="0dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/confirmEmail"
                    app:layout_constraintEnd_toEndOf="@id/confirmEmail"
                    app:layout_constraintTop_toBottomOf="@id/confirmEmail" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/updateBtn"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="更新する"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>