<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"
        android:paddingTop="0dp"
        android:paddingBottom="0dp"
        android:orientation="vertical"
        android:background="@drawable/round_rectangle_5dp_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <TextView
            android:id="@+id/messageTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="56dp"
            android:layout_marginEnd="20dp"
            android:text="@string/common_modal_message"
            android:textAlignment="center"
            android:textColor="@color/base_text"
            android:textSize="14sp" />

        <Button
            android:id="@+id/closeButton"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="48dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/common_modal_button"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textAllCaps="false"
            app:backgroundTint="@null"
            app:elevation="0dp"/>

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="16dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
            android:textColor="@color/base_text"
            android:gravity="center"
            android:padding="0dp"
            android:textAllCaps="false"
            android:stateListAnimator="@null"
            android:text="@string/common_modal_cancel"
            android:textSize="16sp"
            android:visibility="gone"
            android:textStyle="normal"
            app:backgroundTint="@null"
            app:elevation="0dp" />
        <View
            android:id="@+id/spaceView"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            tools:ignore="MissingConstraints" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>