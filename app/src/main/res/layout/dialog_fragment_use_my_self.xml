<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"
        android:background="@drawable/round_rectangle_5dp_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/messageTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="56dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="48dp"
            android:text="@string/use_my_self_message"
            android:textAlignment="center"
            android:textColor="@color/base_text"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@+id/notUseButton"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/notUseButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/use_my_self_not_use"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintBottom_toTopOf="@id/cancelButton"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/messageTextView" />

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
            android:gravity="center"
            android:padding="0dp"
            android:stateListAnimator="@null"
            android:text="@string/use_my_self_cancel"
            android:textColor="@color/base_text"
            android:textSize="16sp"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/notUseButton" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>