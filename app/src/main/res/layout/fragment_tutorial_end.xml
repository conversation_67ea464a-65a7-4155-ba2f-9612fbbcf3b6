<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.tutorial.TutorialEndViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.tutorial.TutorialTopFragment">

        <ImageView
            android:id="@+id/keyVisualView"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="@dimen/tutorial_icon_margin_top"
            android:src="@drawable/icon_tutorial5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/tutorial_title_margin_top"
            android:text="就職・転職活動の準備をしよう"
            android:textColor="@color/base_text"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/keyVisualView" />

        <TextView
            android:id="@+id/descriptionTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="45dp"
            android:layout_marginTop="@dimen/tutorial_other_description_margin_top"
            android:layout_marginEnd="45dp"
            android:text="就活や転職活動に関する予定もアプリでしっかり管理。面接に役立つコラムも随時更新中！"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleView" />

        <View
            android:id="@+id/bottomView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tutorial_other_bottom_height"
            android:layout_marginTop="@dimen/tutorial_other_bottom_not_warning_margin_top"
            android:background="@color/base_tutorial_bg01"
            android:backgroundTint="@color/base_tutorial_bg01"
            app:layout_constraintTop_toBottomOf="@id/descriptionTextView" />

        <ImageView
            android:id="@+id/display1View"
            android:layout_width="146dp"
            android:layout_height="146dp"
            android:layout_marginTop="@dimen/tutorial_other_display1_margin_top"
            android:layout_marginStart="16dp"
            android:src="@drawable/img_android_tutorial5_1"
            app:layout_constraintTop_toTopOf="@id/bottomView"
            app:layout_constraintStart_toStartOf="@id/bottomView" />

        <LinearLayout
            android:id="@+id/displayDescription1View"
            android:layout_width="180dp"
            android:layout_height="65dp"
            android:layout_marginStart="@dimen/tutorial_other_display_description1_margin_start"
            android:layout_marginTop="@dimen/tutorial_other_display_description1_margin_top"
            android:layout_marginEnd="@dimen/tutorial_other_display_description1_margin_end"
            android:background="@drawable/round_rectangle_10dp_gray"
            app:layout_constraintStart_toStartOf="@id/bottomView"
            app:layout_constraintEnd_toEndOf="@id/bottomView"
            app:layout_constraintTop_toTopOf="@id/bottomView"
            android:gravity="top|center_vertical"
            android:orientation="vertical">

        <TextView
            android:id="@+id/displayDescription1TitleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="予定管理機能"
            android:textColor="@color/base_white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="@id/displayDescription1View"
            app:layout_constraintEnd_toEndOf="@id/displayDescription1View"
            app:layout_constraintStart_toStartOf="@id/displayDescription1View" />

        <TextView
            android:id="@+id/displayDescription1TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="予定の登録ができる"
            android:textColor="@color/base_white"
            android:textSize="12sp"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/displayDescription1TitleTextView"
            app:layout_constraintEnd_toEndOf="@id/displayDescription1View"
            app:layout_constraintStart_toStartOf="@id/displayDescription1View" />
        </LinearLayout>

        <ImageView
            android:id="@+id/display2View"
            android:layout_width="146dp"
            android:layout_height="146dp"
            android:layout_marginTop="@dimen/tutorial_other_display2_margin_top"
            android:layout_marginEnd="16dp"
            android:src="@drawable/img_android_tutorial5_2"
            app:layout_constraintTop_toTopOf="@id/bottomView"
            app:layout_constraintEnd_toEndOf="@id/bottomView" />

        <LinearLayout
            android:id="@+id/displayDescription2View"
            android:layout_width="180dp"
            android:layout_height="83dp"
            android:layout_marginStart="@dimen/tutorial_other_display_description2_margin_start"
            android:layout_marginTop="@dimen/tutorial_other_display_description2_margin_top"
            android:layout_marginEnd="@dimen/tutorial_other_display_description2_margin_end"
            android:background="@drawable/round_rectangle_10dp_gray"
            app:layout_constraintStart_toStartOf="@id/bottomView"
            app:layout_constraintEnd_toEndOf="@id/bottomView"
            app:layout_constraintTop_toTopOf="@id/bottomView"
            android:gravity="top|center_vertical"
            android:orientation="vertical">

        <TextView
            android:id="@+id/displayDescription2TitleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="コラム"
            android:textColor="@color/base_white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="@id/displayDescription2View"
            app:layout_constraintEnd_toEndOf="@id/displayDescription2View"
            app:layout_constraintStart_toStartOf="@id/displayDescription2View" />

        <TextView
            android:id="@+id/displayDescription2TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="就活や転職活動に\n関するコラムで対策"
            android:textColor="@color/base_white"
            android:textSize="12sp"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/displayDescription2TitleTextView"
            app:layout_constraintEnd_toEndOf="@id/displayDescription2View"
            app:layout_constraintStart_toStartOf="@id/displayDescription2View" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/pagerLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/tutorial_pager_margin_top"
            android:gravity="top|center_horizontal"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/bottomView">

            <ImageView
                android:id="@+id/pagerOne"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet" />

            <ImageView
                android:id="@+id/pagerTwo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerOne" />

            <ImageView
                android:id="@+id/pagerThree"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerTwo" />

            <ImageView
                android:id="@+id/pagerFour"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerThree" />

            <ImageView
                android:id="@+id/pagerFive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet"
                app:layout_constraintStart_toEndOf="@id/pagerFour" />

            <ImageView
                android:id="@+id/pagerSix"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/slider_pagination_bullet_active"
                app:layout_constraintStart_toEndOf="@id/pagerFive" />
        </LinearLayout>

        <Button
            android:id="@+id/startButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginTop="@dimen/tutorial_other_start_margin_top"
            android:layout_marginStart="60dp"
            android:layout_marginEnd="60dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:onClick="@{() -> viewModel.onStartButtonClick()}"
            android:text="はじめる"
            android:textColor="@color/base_white"
            android:textStyle="bold"
            app:backgroundTint="@null"
            android:textSize="16sp"
            android:padding="0dp"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/displayDescription2View"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/skipButton"
            android:layout_width="72dp"
            android:layout_height="32dp"
            android:layout_marginTop="@dimen/tutorial_skip_margin_top"
            android:layout_marginEnd="@dimen/tutorial_skip_margin_end"
            android:background="@drawable/round_rectangle_16dp_white_and_color_primary_line"
            android:onClick="@{() -> viewModel.onSkipButtonClick()}"
            android:stateListAnimator="@null"
            android:text="@string/tutorial_skip"
            android:padding="0dp"
            android:gravity="center"
            android:textColor="@color/color_primary"
            android:textSize="14sp"
            app:backgroundTint="@null"
            app:elevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>