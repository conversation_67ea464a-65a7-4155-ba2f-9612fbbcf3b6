<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.agreement.AgreementTopViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.agreement.AgreementTopFragment">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_marginTop="@dimen/agreement_logo_margin_top"
            android:src="@drawable/img_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/agreement_title_margin_top"
            android:gravity="center"
            android:text="@string/agreement_title"
            android:textColor="@color/base_text"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView" />

        <TextView
            android:id="@+id/descriptionView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="@dimen/agreement_description_margin_top"
            android:layout_marginEnd="28dp"
            android:gravity="center"
            android:text="@string/agreement_description"
            android:textColor="@color/base_text"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titleView" />

        <TextView
            android:id="@+id/textView6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/agreement_terms_margin_top"
            android:onClick="@{() -> viewModel.onTermsLinkClick()}"
            android:text="@string/agreement_terms"
            android:textColor="@color/color_primary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descriptionView" />

        <TextView
            android:id="@+id/textView7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/agreement_privacy_margin_top"
            android:onClick="@{() -> viewModel.onPrivacyLinkClick()}"
            android:text="@string/agreement_privacy"
            android:textColor="@color/color_primary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView6" />

        <LinearLayout
            android:id="@+id/agreeView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="45dp"
            android:layout_marginTop="@dimen/agreement_check_area_margin_top"
            android:layout_marginEnd="45dp"
            android:background="@color/base_10"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView7">

            <CheckBox
                android:id="@+id/checkbox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginStart="4dp"
                android:background="@color/base_white"
                android:backgroundTint="@color/base_white"
                android:checked="false"
                android:enabled="false" />

            <TextView
                android:id="@+id/textView8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="4dp"
                android:text="@string/agreement_agree"
                android:textColor="@color/base_text"
                android:textSize="14sp" />
        </LinearLayout>

        <Button
            android:id="@+id/startButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="@dimen/agreement_next_margin_top"
            android:layout_marginEnd="20dp"
            android:background="@drawable/round_rectangle_25dp_gray"
            android:drawableEnd="@drawable/ic_arrow_next_white_16dp"
            android:drawablePadding="8dp"
            android:enabled="false"
            android:gravity="center"
            android:onClick="@{() -> viewModel.onStartQuestionButtonClick()}"
            android:paddingStart="48dp"
            android:paddingTop="0dp"
            android:paddingEnd="16dp"
            android:paddingBottom="0dp"
            android:text="@string/agreement_start_question"
            android:textColor="@color/base_white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/agreeView" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>