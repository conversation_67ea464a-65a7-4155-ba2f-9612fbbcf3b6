<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_white"
    android:backgroundTint="@color/base_white">

    <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/mySelf"
            layout="@layout/input_text"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="28dp"
            android:layout_marginTop="21dp"
            android:layout_marginEnd="28dp"
            android:layout_marginBottom="12dp"
            app:layout_constraintBottom_toTopOf="@id/countText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar" />

        <TextView
            android:id="@+id/countText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="29dp"
            android:layout_marginBottom="32dp"
            android:text="300"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@id/saveButton"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/countTextBase"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:text="字 / 最大1000字"
            android:textColor="@color/base_text"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@id/saveButton"
            app:layout_constraintStart_toEndOf="@id/countText" />

        <Button
            android:id="@+id/saveButton"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/round_rectangle_25dp_color_primary"
            android:gravity="center"
            android:text="保存"
            android:textColor="@color/base_white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>