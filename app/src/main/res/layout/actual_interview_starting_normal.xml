<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/headerBar"
        layout="@layout/header_bar_with_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <jp.co.cac.ope.shokken.lab.ui.customview.SeekBarView
        android:id="@+id/seekBar"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerBar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_status"
        android:layout_width="match_parent"
        android:layout_height="97dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="20dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/seekBar">

        <TextView
            android:id="@+id/recording"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:background="@drawable/round_rectangle_5dp_orange"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingTop="4dp"
            android:paddingEnd="10dp"
            android:paddingBottom="4dp"
            android:text="撮影中"
            android:textColor="@color/base_white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/center_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/target_time_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/target_time_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="目安時間 : "
                    android:textColor="@color/gray_placeholder"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/target_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1分"
                    android:textColor="@color/gray_placeholder"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/timeCountLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="00:00"
                android:textColor="@color/base_text"
                android:textSize="20sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/kind_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/round_rectangle_5dp_gray"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingStart="8dp"
                android:paddingTop="3dp"
                android:paddingEnd="8dp"
                android:paddingBottom="3dp"
                android:visibility="visible">

                <TextView
                    android:id="@+id/kind_label_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="練習指標 : "
                    android:textColor="@color/base_text"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/kind_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="真剣・熱意"
                    android:textColor="@color/base_text"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/movieLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_status" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="20dp"
        android:src="@drawable/img_affectiva_gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/movieLayout" />

    <Button
        android:id="@+id/endButton"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/round_rectangle_25dp_color_primary"
        android:gravity="center"
        android:text="終了"
        android:textColor="@color/base_white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:backgroundTint="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
