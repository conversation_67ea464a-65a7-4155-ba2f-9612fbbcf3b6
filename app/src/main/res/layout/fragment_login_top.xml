<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools" >
    <data>
        <variable name="viewModel" type="jp.co.cac.ope.shokken.lab.ui.login.LoginTopViewModel" />
    </data>

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.login.LoginTopFragment"
    android:background="@color/base_white"
    android:backgroundTint="@color/base_white">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="182dp"
        android:layout_height="182dp"
        android:layout_marginTop="@dimen/login_top_logo_margin_top"
        android:src="@drawable/img_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/messageTxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="42dp"
        android:layout_marginTop="@dimen/login_top_message_text_margin_top"
        android:gravity="center"
        android:text="This application is intended for use exclusively by employees of [Company Name]. Unauthorized access is strictly prohibited. If you are not an employee, please do not attempt to log in."
        android:textAlignment="gravity"
        android:textColor="@color/base_text"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/login_top_already_login_margin_top"
        android:text="@string/login_already_login"
        android:textColor="@color/base_text"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/messageTxt" />

    <Button
        android:id="@+id/button"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="@dimen/login_top_login_button_margin_top"
        android:layout_marginEnd="20dp"
        android:background="@drawable/round_rectangle_25dp_color_primary"
        android:onClick="@{() -> viewModel.onLoginButtonClick()}"
        android:stateListAnimator="@null"
        android:text="@string/login_login_button"
        android:textSize="16sp"
        android:textStyle="bold"
        app:backgroundTint="@null"
        app:elevation="0dp"
        android:padding="0dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <TextView
        android:id="@+id/textView3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/login_top_not_account_margin_top"
        android:text="@string/login_not_account"
        android:textColor="@color/base_text"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/button" />

    <Button
        android:id="@+id/button2"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="@dimen/login_top_start_button_margin_top"
        android:layout_marginEnd="20dp"
        android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
        android:onClick="@{() -> viewModel.onStartButtonClick()}"
        android:stateListAnimator="@null"
        android:text="Register"
        android:textColor="@color/base_text"
        android:textSize="16sp"
        app:backgroundTint="@null"
        app:elevation="0dp"
        android:padding="0dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView3" />

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>
