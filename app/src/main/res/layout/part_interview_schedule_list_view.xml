<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="3dp"
    app:cardElevation="2dp"
    app:strokeColor="#382F2D26"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    android:layout_marginLeft="20dp"
    android:layout_marginRight="20dp"
    android:backgroundTint="@color/base_white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="16dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/dtTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:text="2022/12/15 PM13:00"
            android:textColor="@color/base_text"
            android:textStyle="bold"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/companyTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:singleLine="true"
            android:ellipsize="end"
            android:text="株式会社OOOOコンサルティング"
            android:textColor="@color/base_text"
            android:textSize="14sp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_marginTop="4dp">
            <ImageView
                android:id="@+id/iconEdit"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/icon_edit"
                app:layout_constraintStart_toStartOf="parent"
                tools:ignore="MissingConstraints" />
            <ImageView
                android:id="@+id/iconTrash"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/icon_trash"
                android:layout_marginLeft="20dp"
                app:layout_constraintStart_toEndOf="@+id/iconEdit"
                tools:ignore="MissingConstraints" />

            <View
                android:id="@+id/spaceView"
                android:layout_width="0dp"
                 android:layout_height="match_parent"
                app:layout_constraintStart_toEndOf="@+id/iconTrash"
                app:layout_constraintEnd_toStartOf="@+id/rightView">
            </View>
            <LinearLayout
                android:id="@+id/rightView"
                android:layout_width="183dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_gravity="right"
                android:gravity="right"
                android:layout_marginRight="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/spaceView"
                tools:ignore="MissingConstraints">
                <TextView
                    android:id="@+id/selectionStatusTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="24dp"
                    android:layout_marginTop="0dp"
                    android:layout_marginRight="0dp"
                    android:background="@drawable/round_rectangle_5dp_color_primary_usui"
                    android:singleLine="true"
                    android:ellipsize="end"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp"
                    android:text="インターンシップ期間インタ…"
                    android:textAlignment="center"
                    android:textColor="@color/base_text"
                    android:textSize="12sp" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>