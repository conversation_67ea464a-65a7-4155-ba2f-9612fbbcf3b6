<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mainView"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="top"
    tools:ignore="MissingConstraints">

    <Button
        android:id="@+id/upgradePremiumButton"
        android:layout_width="170dp"
        android:layout_height="24dp"
        android:background="@drawable/round_rectangle_12dp_white_and_main_color_primary_line"
        android:gravity="center"
        android:stateListAnimator="@null"
        android:text=""
        android:textColor="@color/color_primary"
        android:textSize="12sp"
        android:textStyle="bold"
        app:backgroundTint="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/nameTextView"
        android:layout_width="170dp"
        android:layout_height="24dp"
        android:text="Premiumにアップグレード"
        android:textColor="@color/color_primary"
        android:textStyle="bold"
        android:gravity="center"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>