<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/backHeaderBar"
        layout="@layout/header_bar_with_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="3/3"
        android:textColor="@color/gray_placeholder"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backHeaderBar" />

    <View
        android:id="@+id/currentBarBaseView"
        android:layout_width="match_parent"
        android:layout_height="7dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/round_rectangle_10dp_color_primary_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_status"
        android:layout_width="match_parent"
        android:layout_height="97dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="20dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/currentBarBaseView">

        <LinearLayout
            android:id="@+id/center_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/target_time_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/target_time_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="目安時間 : "
                    android:textColor="@color/gray_placeholder"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/target_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1分"
                    android:textColor="@color/gray_placeholder"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/timeCountLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ready"
                android:textColor="@color/base_text"
                android:textSize="20sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/kind_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/round_rectangle_5dp_gray"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingStart="8dp"
                android:paddingTop="3dp"
                android:paddingEnd="8dp"
                android:paddingBottom="3dp"
                android:visibility="visible">

                <TextView
                    android:id="@+id/kind_label_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="練習指標 : "
                    android:textColor="@color/base_text"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/kind_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="真剣・熱意"
                    android:textColor="@color/base_text"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/movieLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_status" />

    <LinearLayout
        android:id="@+id/preStartFooterLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/prestartText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:text=" ※Recording will begin on the next\n※Videos are stored on the device and the server."
            android:textColor="@color/base_text"
            android:textSize="14sp" />

        <LinearLayout
            android:id="@+id/buttonSingleLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/redoImage"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginBottom="24dp"
                android:clickable="true"
                android:focusable="true"
                android:src="@drawable/ic_replay" />

            <TextView
                android:id="@+id/redoText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginBottom="24dp"
                android:clickable="true"
                android:focusable="true"
                android:text="やり直す"
                android:textColor="@color/color_primary"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/readyingFooterLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/readyingText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="目安時間と練習指標を意識し、相手の目を見て話しましょう。\n準備ができたら「スタート」を押してください。 ※次の画面で音が出ます。"
            android:textColor="@color/base_text"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@id/startButton"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/startButton"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="12dp"
            android:layout_weight="1"
            android:background="@drawable/round_rectangle_25dp_gray"
            android:enabled="false"
            android:gravity="center"
            android:text="スタート"
            android:textColor="@color/base_white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@null" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
