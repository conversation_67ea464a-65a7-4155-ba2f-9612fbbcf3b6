<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.interview.InterviewRecordSelectViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.interview.InterviewRecordSelectFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/contentsLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="26dp"
                    android:layout_marginTop="13dp"
                    android:text="自己紹介"
                    android:textColor="@color/base_text"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/kind_label_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="26dp"
                    android:layout_marginTop="6dp"
                    android:text="練習指標 : "
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title" />

                <TextView
                    android:id="@+id/kind_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="真剣・熱意"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintStart_toEndOf="@id/kind_label_title"
                    app:layout_constraintTop_toTopOf="@id/kind_label_title" />

                <TextView
                    android:id="@+id/total_practice_count_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:text="総練習回数"
                    android:textColor="@color/base_text"
                    android:textSize="14sp"
                    app:layout_constraintStart_toEndOf="@id/kind_label"
                    app:layout_constraintTop_toTopOf="@id/kind_label" />

                <TextView
                    android:id="@+id/total_practice_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="00"
                    android:textColor="@color/base_text"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@id/total_practice_count_title"
                    app:layout_constraintStart_toEndOf="@id/total_practice_count_title" />

                <View
                    android:id="@+id/total_score_circle"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="22dp"
                    android:background="@drawable/circle_30dp_base_10"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="@id/total_score_circle"
                    app:layout_constraintEnd_toEndOf="@id/total_score_circle"
                    app:layout_constraintStart_toStartOf="@id/total_score_circle"
                    app:layout_constraintTop_toTopOf="@id/total_score_circle">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/icon_crown18" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="-2dp"
                        android:text="Best"
                        android:textColor="@color/gray_icon"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/best_score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="-10dp"
                        android:text="000"
                        android:textColor="@color/color_primary"
                        android:textSize="24sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/practiceGraphLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="26dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="22dp"
                    android:background="@drawable/round_rectangle_5dp_white_with_shadow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/total_score_circle">

                    <TextView
                        android:id="@+id/score_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginTop="20dp"
                        android:text="Score"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="00"
                        android:textColor="@color/base_text"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@id/score_title"
                        app:layout_constraintStart_toEndOf="@id/score_title" />

                    <TextView
                        android:id="@+id/score_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginTop="6dp"
                        android:text="2022/10/23 13:45"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/score_title" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/practiceGraph"
                        android:layout_width="288dp"
                        android:layout_height="166dp"
                        android:layout_marginStart="12dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="12dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/score_date" />

                    <ImageButton
                        android:id="@+id/prev"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginBottom="14dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/left_arrow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/practiceGraph" />

                    <TextView
                        android:id="@+id/prev_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:text="前の10件"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="@id/prev"
                        app:layout_constraintStart_toEndOf="@id/prev"
                        app:layout_constraintTop_toTopOf="@id/prev" />

                    <ImageButton
                        android:id="@+id/next"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="16dp"
                        android:background="@color/white_transparent"
                        android:src="@drawable/right_arrow"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/practiceGraph" />

                    <TextView
                        android:id="@+id/next_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:text="次の10件"
                        android:textColor="@color/base_text"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="@id/next"
                        app:layout_constraintEnd_toStartOf="@id/next"
                        app:layout_constraintTop_toTopOf="@id/next" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/record_list_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:text="すべての成績"
                    android:textColor="@color/base_text"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/practiceGraphLayout" />

                <LinearLayout
                    android:id="@+id/contentsContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="20dp"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@id/record_list_title" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>