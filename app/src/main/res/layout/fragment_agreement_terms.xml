<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="jp.co.cac.ope.shokken.lab.ui.agreement.AgreementTermsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_white"
        android:backgroundTint="@color/base_white"
        tools:context=".ui.agreement.AgreementTermsFragment">

        <include
            android:id="@+id/backHeaderBar"
            layout="@layout/header_bar_with_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backHeaderBar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="28dp">

                <TextView
                    android:id="@+id/longTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/agreement_terms_text"
                    android:textColor="@color/base_text"
                    android:textSize="14sp" />

                <Button
                    android:id="@+id/closeButton"
                    android:layout_width="100dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/round_rectangle_25dp_white_and_color_primary_line"
                    android:gravity="center"
                    android:onClick="@{() -> viewModel.onCloseButtonClick()}"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text="閉じる"
                    android:textColor="@color/base_text"
                    android:textSize="16sp"
                    app:backgroundTint="@null"
                    app:elevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/longTextView" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>